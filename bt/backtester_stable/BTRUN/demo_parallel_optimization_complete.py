#!/usr/bin/env python3
"""
Complete Parallel Optimization System Demo
==========================================

Comprehensive demonstration of the parallel optimization system:
- All optimization algorithms
- Real-time progress tracking
- Multi-objective optimization
- Performance benchmarking
- Integration with existing backtester
"""

import asyncio
import logging
import time
import json
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any
import matplotlib.pyplot as plt

# Import optimization components
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "backtester_v2"))

from strategy_optimizer.optimization_engine import OptimizationEngine, OptimizationConfig
from strategy_optimizer.parallel_optimizer import ParallelOptimizer
from strategy_optimizer.parameter_optimizer import ParameterOptimizer
from strategy_optimizer.performance_optimizer import PerformanceOptimizer
from strategy_optimizer.multi_objective_optimizer import MultiObjectiveOptimizer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ParallelOptimizationDemo:
    """Complete demonstration of parallel optimization system"""
    
    def __init__(self):
        """Initialize demo system"""
        self.optimization_engine = OptimizationEngine()
        self.results = {}
        
        logger.info("🚀 Parallel Optimization Demo System Initialized")
    
    def create_realistic_objective_function(self, strategy_type: str):
        """Create realistic objective function for strategy type"""
        
        def tbs_objective(parameters):
            """Time-Based Strategy objective function"""
            stop_loss = parameters.get('stop_loss', 0.02)
            take_profit = parameters.get('take_profit', 0.05)
            position_size = parameters.get('position_size', 0.02)
            lookback_period = parameters.get('lookback_period', 15)
            
            # Simulate realistic TBS performance
            base_return = 0.15  # 15% annual return
            volatility = 0.25   # 25% volatility
            
            # Parameter effects
            sl_effect = max(0.5, 1.0 - (stop_loss - 0.02) * 20)  # Prefer moderate SL
            tp_effect = min(1.5, 1.0 + (take_profit - 0.03) * 10)  # Prefer higher TP
            size_effect = max(0.7, 1.0 - abs(position_size - 0.02) * 30)  # Prefer optimal size
            lookback_effect = max(0.8, 1.0 - abs(lookback_period - 15) / 20)  # Prefer 15-day lookback
            
            # Calculate metrics
            adjusted_return = base_return * sl_effect * tp_effect * size_effect * lookback_effect
            adjusted_volatility = volatility * (1 + abs(position_size - 0.02) * 5)
            
            sharpe_ratio = adjusted_return / adjusted_volatility
            max_drawdown = min(0.4, 0.1 + adjusted_volatility * 0.3)
            win_rate = min(0.8, 0.4 + sharpe_ratio * 0.15)
            profit_factor = max(1.0, 1.0 + sharpe_ratio * 0.3)
            
            # Add realistic noise
            noise = np.random.normal(0, 0.05)
            sharpe_ratio += noise
            
            return {
                'sharpe_ratio': max(0.1, sharpe_ratio),
                'max_drawdown': max(0.01, max_drawdown),
                'win_rate': max(0.3, min(0.8, win_rate)),
                'profit_factor': max(1.0, profit_factor),
                'composite_score': max(0.1, sharpe_ratio)
            }
        
        def tv_objective(parameters):
            """TradingView Strategy objective function"""
            stop_loss = parameters.get('stop_loss', 0.015)
            take_profit = parameters.get('take_profit', 0.04)
            signal_threshold = parameters.get('signal_threshold', 0.7)
            exit_threshold = parameters.get('exit_threshold', 0.5)
            
            # TV strategies typically have different characteristics
            base_return = 0.12
            volatility = 0.3
            
            # Signal quality effects
            signal_effect = min(1.3, 0.7 + signal_threshold * 0.8)
            exit_effect = max(0.8, 1.0 - abs(exit_threshold - 0.5) * 0.6)
            
            adjusted_return = base_return * signal_effect * exit_effect
            sharpe_ratio = adjusted_return / volatility
            
            # Add noise
            sharpe_ratio += np.random.normal(0, 0.08)
            
            return {
                'sharpe_ratio': max(0.1, sharpe_ratio),
                'max_drawdown': min(0.35, 0.15 + volatility * 0.2),
                'win_rate': max(0.35, min(0.75, 0.45 + sharpe_ratio * 0.1)),
                'profit_factor': max(1.0, 1.0 + sharpe_ratio * 0.25),
                'composite_score': max(0.1, sharpe_ratio)
            }
        
        def oi_objective(parameters):
            """Open Interest Strategy objective function"""
            oi_threshold = parameters.get('oi_threshold', 0.3)
            volume_threshold = parameters.get('volume_threshold', 2.0)
            strike_shift = parameters.get('strike_shift', 2)
            
            # OI strategies have unique characteristics
            base_return = 0.18
            volatility = 0.35
            
            # OI-specific effects
            oi_effect = max(0.6, 1.0 - abs(oi_threshold - 0.3) * 2)
            volume_effect = max(0.7, 1.0 - abs(volume_threshold - 2.0) * 0.3)
            strike_effect = max(0.8, 1.0 - abs(strike_shift - 2) * 0.1)
            
            adjusted_return = base_return * oi_effect * volume_effect * strike_effect
            sharpe_ratio = adjusted_return / volatility
            
            # Add noise
            sharpe_ratio += np.random.normal(0, 0.1)
            
            return {
                'sharpe_ratio': max(0.1, sharpe_ratio),
                'max_drawdown': min(0.4, 0.2 + volatility * 0.15),
                'win_rate': max(0.3, min(0.7, 0.4 + sharpe_ratio * 0.12)),
                'profit_factor': max(1.0, 1.0 + sharpe_ratio * 0.2),
                'composite_score': max(0.1, sharpe_ratio)
            }
        
        objectives = {
            'TBS': tbs_objective,
            'TV': tv_objective,
            'OI': oi_objective
        }
        
        return objectives.get(strategy_type, tbs_objective)
    
    def get_parameter_bounds(self, strategy_type: str) -> Dict[str, tuple]:
        """Get realistic parameter bounds for strategy type"""
        
        bounds = {
            'TBS': {
                'stop_loss': (0.01, 0.05),
                'take_profit': (0.02, 0.1),
                'position_size': (0.01, 0.05),
                'lookback_period': (5, 30),
                'threshold': (0.005, 0.02)
            },
            'TV': {
                'stop_loss': (0.005, 0.03),
                'take_profit': (0.01, 0.08),
                'position_size': (0.005, 0.03),
                'signal_threshold': (0.6, 0.9),
                'exit_threshold': (0.3, 0.7)
            },
            'OI': {
                'oi_threshold': (0.1, 0.5),
                'volume_threshold': (1.2, 3.0),
                'strike_shift': (0, 5),
                'time_decay_factor': (0.8, 1.2),
                'position_size': (0.01, 0.04)
            }
        }
        
        return bounds.get(strategy_type, bounds['TBS'])
    
    async def demo_parameter_optimization(self):
        """Demonstrate parameter optimization with different algorithms"""
        
        logger.info("🔧 Demo 1: Parameter Optimization Algorithms")
        
        strategy_type = 'TBS'
        objective_function = self.create_realistic_objective_function(strategy_type)
        parameter_bounds = self.get_parameter_bounds(strategy_type)
        
        algorithms = ['grid', 'random', 'bayesian', 'genetic']
        results = {}
        
        for algorithm in algorithms:
            logger.info(f"   Testing {algorithm.upper()} algorithm...")
            
            config = OptimizationConfig(
                strategy_type=strategy_type,
                optimization_type='parameter',
                parallel_workers=4,
                max_iterations=50 if algorithm != 'grid' else 32,  # 2^5 for grid
                parameter_bounds=parameter_bounds
            )
            
            # Override algorithm
            self.optimization_engine.parameter_optimizer.config['algorithm'] = algorithm
            
            start_time = time.time()
            result = await self.optimization_engine.optimize_strategy(
                config, objective_function
            )
            execution_time = time.time() - start_time
            
            results[algorithm] = {
                'best_score': result.best_performance.get('objective_value', 0),
                'best_parameters': result.best_parameters,
                'execution_time': execution_time,
                'iterations': result.iterations_completed
            }
            
            logger.info(f"     {algorithm:8s}: score={results[algorithm]['best_score']:.4f}, "
                       f"time={execution_time:.2f}s, iterations={result.iterations_completed}")
        
        self.results['parameter_optimization'] = results
        return results
    
    async def demo_parallel_optimization(self):
        """Demonstrate parallel optimization with multiple workers"""
        
        logger.info("⚡ Demo 2: Parallel Optimization Performance")
        
        strategy_type = 'TV'
        objective_function = self.create_realistic_objective_function(strategy_type)
        parameter_bounds = self.get_parameter_bounds(strategy_type)
        
        worker_counts = [1, 2, 4, 8]
        results = {}
        
        for workers in worker_counts:
            logger.info(f"   Testing with {workers} workers...")
            
            config = OptimizationConfig(
                strategy_type=strategy_type,
                optimization_type='parameter',
                parallel_workers=workers,
                max_iterations=40,
                parameter_bounds=parameter_bounds
            )
            
            start_time = time.time()
            result = await self.optimization_engine.optimize_strategy(
                config, objective_function
            )
            execution_time = time.time() - start_time
            
            results[workers] = {
                'best_score': result.best_performance.get('objective_value', 0),
                'execution_time': execution_time,
                'speedup': results[1]['execution_time'] / execution_time if workers > 1 else 1.0
            }
            
            speedup_text = f", speedup={results[workers]['speedup']:.2f}x" if workers > 1 else ""
            logger.info(f"     {workers} workers: score={results[workers]['best_score']:.4f}, "
                       f"time={execution_time:.2f}s{speedup_text}")
        
        self.results['parallel_optimization'] = results
        return results
    
    async def demo_multi_objective_optimization(self):
        """Demonstrate multi-objective optimization"""
        
        logger.info("🎯 Demo 3: Multi-Objective Optimization")
        
        strategy_type = 'OI'
        parameter_bounds = self.get_parameter_bounds(strategy_type)
        
        def multi_objective_function(parameters):
            """Multi-objective function returning multiple objectives"""
            result = self.create_realistic_objective_function(strategy_type)(parameters)
            
            # Return objectives in order: sharpe_ratio, max_drawdown (minimize), win_rate
            return [
                result['sharpe_ratio'],
                -result['max_drawdown'],  # Negative because NSGA-II maximizes
                result['win_rate']
            ]
        
        config = OptimizationConfig(
            strategy_type=strategy_type,
            optimization_type='multi_objective',
            parallel_workers=4,
            max_iterations=200,  # 50 population * 4 generations
            parameter_bounds=parameter_bounds,
            objectives=['sharpe_ratio', 'max_drawdown', 'win_rate']
        )
        
        start_time = time.time()
        result = await self.optimization_engine.optimize_strategy(
            config, multi_objective_function
        )
        execution_time = time.time() - start_time
        
        # Get Pareto front
        pareto_front = self.optimization_engine.multi_objective_optimizer.get_pareto_front()
        trade_off_analysis = self.optimization_engine.multi_objective_optimizer.analyze_trade_offs()
        
        logger.info(f"   Multi-objective optimization completed in {execution_time:.2f}s")
        logger.info(f"   Pareto front size: {len(pareto_front)}")
        logger.info(f"   Best solution: {result.best_parameters}")
        logger.info(f"   Best performance: {result.best_performance}")
        
        if trade_off_analysis.get('objective_ranges'):
            logger.info("   Objective ranges:")
            for obj, ranges in trade_off_analysis['objective_ranges'].items():
                logger.info(f"     {obj}: {ranges['min']:.3f} - {ranges['max']:.3f}")
        
        self.results['multi_objective'] = {
            'pareto_front_size': len(pareto_front),
            'best_solution': result.best_parameters,
            'execution_time': execution_time,
            'trade_off_analysis': trade_off_analysis
        }
        
        return result
    
    async def demo_performance_optimization(self):
        """Demonstrate performance-focused optimization"""
        
        logger.info("📈 Demo 4: Performance Optimization")
        
        strategy_type = 'TBS'
        objective_function = self.create_realistic_objective_function(strategy_type)
        parameter_bounds = self.get_parameter_bounds(strategy_type)
        
        config = OptimizationConfig(
            strategy_type=strategy_type,
            optimization_type='performance',
            parallel_workers=4,
            max_iterations=60,
            parameter_bounds=parameter_bounds,
            objectives=['sharpe_ratio', 'max_drawdown', 'win_rate', 'profit_factor']
        )
        
        start_time = time.time()
        result = await self.optimization_engine.optimize_strategy(
            config, objective_function
        )
        execution_time = time.time() - start_time
        
        # Get performance summary
        performance_summary = self.optimization_engine.performance_optimizer.get_performance_summary()
        
        logger.info(f"   Performance optimization completed in {execution_time:.2f}s")
        logger.info(f"   Best composite score: {result.best_performance.get('objective_value', 0):.4f}")
        logger.info(f"   Best parameters: {result.best_parameters}")
        logger.info(f"   Total evaluations: {performance_summary.get('total_evaluations', 0)}")
        logger.info(f"   Constraint satisfaction rate: {performance_summary.get('constraint_satisfaction_rate', 0):.2%}")
        
        self.results['performance_optimization'] = {
            'best_score': result.best_performance.get('objective_value', 0),
            'best_parameters': result.best_parameters,
            'execution_time': execution_time,
            'performance_summary': performance_summary
        }
        
        return result
    
    async def demo_portfolio_optimization(self):
        """Demonstrate portfolio optimization across multiple strategies"""
        
        logger.info("📊 Demo 5: Portfolio Optimization")
        
        strategies = [
            OptimizationConfig(
                strategy_type='TBS',
                optimization_type='parameter',
                parallel_workers=2,
                max_iterations=30,
                parameter_bounds=self.get_parameter_bounds('TBS')
            ),
            OptimizationConfig(
                strategy_type='TV',
                optimization_type='parameter',
                parallel_workers=2,
                max_iterations=30,
                parameter_bounds=self.get_parameter_bounds('TV')
            ),
            OptimizationConfig(
                strategy_type='OI',
                optimization_type='parameter',
                parallel_workers=2,
                max_iterations=30,
                parameter_bounds=self.get_parameter_bounds('OI')
            )
        ]
        
        # Portfolio objective function
        def portfolio_objective(parameters):
            # This would typically consider portfolio-level metrics
            # For demo, we use individual strategy objectives
            return self.create_realistic_objective_function('TBS')(parameters)
        
        start_time = time.time()
        results = await self.optimization_engine.optimize_portfolio(
            strategies, portfolio_objective
        )
        execution_time = time.time() - start_time
        
        logger.info(f"   Portfolio optimization completed in {execution_time:.2f}s")
        
        for strategy_type, result in results.items():
            if result:
                logger.info(f"   {strategy_type}: score={result.best_performance.get('objective_value', 0):.4f}")
            else:
                logger.info(f"   {strategy_type}: optimization failed")
        
        self.results['portfolio_optimization'] = {
            'results': {k: v.best_performance if v else None for k, v in results.items()},
            'execution_time': execution_time
        }
        
        return results
    
    def print_comprehensive_summary(self):
        """Print comprehensive summary of all demonstrations"""
        
        logger.info("=" * 80)
        logger.info("📋 COMPREHENSIVE PARALLEL OPTIMIZATION DEMO SUMMARY")
        logger.info("=" * 80)
        
        # Parameter optimization summary
        if 'parameter_optimization' in self.results:
            logger.info("\n🔧 Parameter Optimization Algorithm Comparison:")
            param_results = self.results['parameter_optimization']
            
            for algorithm, metrics in param_results.items():
                logger.info(f"   {algorithm.upper():8s}: "
                           f"Score={metrics['best_score']:6.4f}, "
                           f"Time={metrics['execution_time']:6.2f}s, "
                           f"Iterations={metrics['iterations']:3d}")
        
        # Parallel optimization summary
        if 'parallel_optimization' in self.results:
            logger.info("\n⚡ Parallel Optimization Performance:")
            parallel_results = self.results['parallel_optimization']
            
            for workers, metrics in parallel_results.items():
                speedup_text = f", Speedup={metrics['speedup']:4.2f}x" if workers > 1 else ""
                logger.info(f"   {workers} Workers: "
                           f"Score={metrics['best_score']:6.4f}, "
                           f"Time={metrics['execution_time']:6.2f}s{speedup_text}")
        
        # Multi-objective summary
        if 'multi_objective' in self.results:
            mo_results = self.results['multi_objective']
            logger.info(f"\n🎯 Multi-Objective Optimization:")
            logger.info(f"   Pareto Front Size: {mo_results['pareto_front_size']}")
            logger.info(f"   Execution Time: {mo_results['execution_time']:.2f}s")
        
        # Performance optimization summary
        if 'performance_optimization' in self.results:
            perf_results = self.results['performance_optimization']
            logger.info(f"\n📈 Performance Optimization:")
            logger.info(f"   Best Score: {perf_results['best_score']:.4f}")
            logger.info(f"   Execution Time: {perf_results['execution_time']:.2f}s")
        
        # Portfolio optimization summary
        if 'portfolio_optimization' in self.results:
            portfolio_results = self.results['portfolio_optimization']
            logger.info(f"\n📊 Portfolio Optimization:")
            logger.info(f"   Execution Time: {portfolio_results['execution_time']:.2f}s")
            
            for strategy, result in portfolio_results['results'].items():
                if result:
                    logger.info(f"   {strategy}: {result.get('objective_value', 0):.4f}")
        
        logger.info("\n" + "=" * 80)
        logger.info("✅ ALL DEMONSTRATIONS COMPLETED SUCCESSFULLY!")
        logger.info("🚀 Parallel Optimization System is ready for production use.")
        logger.info("=" * 80)

async def run_complete_demo():
    """Run the complete parallel optimization demonstration"""
    
    logger.info("🚀 Starting Complete Parallel Optimization System Demo...")
    logger.info("=" * 80)
    
    demo = ParallelOptimizationDemo()
    
    try:
        # Run all demonstrations
        await demo.demo_parameter_optimization()
        await demo.demo_parallel_optimization()
        await demo.demo_multi_objective_optimization()
        await demo.demo_performance_optimization()
        await demo.demo_portfolio_optimization()
        
        # Print comprehensive summary
        demo.print_comprehensive_summary()
        
        # Save results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"parallel_optimization_demo_results_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(demo.results, f, indent=2, default=str)
        
        logger.info(f"📁 Results saved to: {results_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(run_complete_demo())
    exit(0 if success else 1)

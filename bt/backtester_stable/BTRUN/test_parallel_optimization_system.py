#!/usr/bin/env python3
"""
Parallel Optimization System Test
=================================

Comprehensive test suite for the parallel optimization system:
- Unit tests for optimization components
- Integration tests with real data
- Performance benchmarks
- UI/API testing
"""

import asyncio
import logging
import time
import json
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any
import unittest
from unittest.mock import Mock, patch

# Import optimization components
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "backtester_v2"))

from strategy_optimizer.optimization_engine import OptimizationEngine, OptimizationConfig
from strategy_optimizer.parallel_optimizer import ParallelOptimizer
from strategy_optimizer.parameter_optimizer import ParameterOptimizer
from strategy_optimizer.performance_optimizer import PerformanceOptimizer
from strategy_optimizer.multi_objective_optimizer import MultiObjectiveOptimizer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestOptimizationComponents(unittest.TestCase):
    """Test individual optimization components"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.parameter_bounds = {
            'stop_loss': (0.01, 0.05),
            'take_profit': (0.02, 0.1),
            'position_size': (0.01, 0.05)
        }
        
        self.mock_objective = self._create_mock_objective_function()
    
    def _create_mock_objective_function(self):
        """Create mock objective function for testing"""
        def objective_function(parameters):
            # Simple quadratic function with noise
            x = parameters.get('stop_loss', 0.02)
            y = parameters.get('take_profit', 0.05)
            z = parameters.get('position_size', 0.02)
            
            # Optimal around (0.02, 0.05, 0.02)
            score = -(x - 0.02)**2 - (y - 0.05)**2 - (z - 0.02)**2 + 1.0
            score += np.random.normal(0, 0.01)  # Add noise
            
            return max(0, score)
        
        return objective_function
    
    def test_parameter_optimizer_grid_search(self):
        """Test grid search parameter optimization"""
        logger.info("🔍 Testing Grid Search Parameter Optimizer...")
        
        optimizer = ParameterOptimizer({'algorithm': 'grid'})
        
        config = OptimizationConfig(
            strategy_type='TBS',
            optimization_type='parameter',
            parameter_bounds=self.parameter_bounds,
            max_iterations=27  # 3^3 grid
        )
        
        async def run_test():
            result = await optimizer.optimize(config, self.mock_objective)
            
            self.assertIsNotNone(result.best_parameters)
            self.assertGreater(result.best_performance['objective_value'], 0)
            self.assertEqual(result.status, 'completed')
            
            logger.info(f"✅ Grid search completed: best score = {result.best_performance['objective_value']:.4f}")
        
        asyncio.run(run_test())
    
    def test_parameter_optimizer_random_search(self):
        """Test random search parameter optimization"""
        logger.info("🎲 Testing Random Search Parameter Optimizer...")
        
        optimizer = ParameterOptimizer({'algorithm': 'random', 'max_evaluations': 50})
        
        config = OptimizationConfig(
            strategy_type='TBS',
            optimization_type='parameter',
            parameter_bounds=self.parameter_bounds,
            max_iterations=50
        )
        
        async def run_test():
            result = await optimizer.optimize(config, self.mock_objective)
            
            self.assertIsNotNone(result.best_parameters)
            self.assertGreater(result.best_performance['objective_value'], 0)
            self.assertEqual(result.status, 'completed')
            
            logger.info(f"✅ Random search completed: best score = {result.best_performance['objective_value']:.4f}")
        
        asyncio.run(run_test())
    
    def test_parameter_optimizer_bayesian(self):
        """Test Bayesian optimization"""
        logger.info("🧠 Testing Bayesian Parameter Optimizer...")
        
        optimizer = ParameterOptimizer({'algorithm': 'bayesian', 'max_evaluations': 30})
        
        config = OptimizationConfig(
            strategy_type='TBS',
            optimization_type='parameter',
            parameter_bounds=self.parameter_bounds,
            max_iterations=30
        )
        
        async def run_test():
            result = await optimizer.optimize(config, self.mock_objective)
            
            self.assertIsNotNone(result.best_parameters)
            self.assertGreater(result.best_performance['objective_value'], 0)
            self.assertEqual(result.status, 'completed')
            
            logger.info(f"✅ Bayesian optimization completed: best score = {result.best_performance['objective_value']:.4f}")
        
        asyncio.run(run_test())
    
    def test_performance_optimizer(self):
        """Test performance optimizer"""
        logger.info("📈 Testing Performance Optimizer...")
        
        def performance_objective(parameters):
            base_score = self.mock_objective(parameters)
            return {
                'sharpe_ratio': base_score * 2,
                'max_drawdown': max(0.01, 0.2 - base_score * 0.1),
                'win_rate': min(0.8, 0.4 + base_score * 0.3),
                'profit_factor': max(1.0, base_score * 1.5)
            }
        
        optimizer = PerformanceOptimizer()
        
        config = OptimizationConfig(
            strategy_type='TBS',
            optimization_type='performance',
            parameter_bounds=self.parameter_bounds,
            max_iterations=30
        )
        
        async def run_test():
            result = await optimizer.optimize(config, performance_objective)
            
            self.assertIsNotNone(result.best_parameters)
            self.assertIn('objective_value', result.best_performance)
            self.assertEqual(result.status, 'completed')
            
            logger.info(f"✅ Performance optimization completed: best score = {result.best_performance['objective_value']:.4f}")
        
        asyncio.run(run_test())
    
    def test_multi_objective_optimizer(self):
        """Test multi-objective optimizer"""
        logger.info("🎯 Testing Multi-Objective Optimizer...")
        
        def multi_objective_function(parameters):
            base_score = self.mock_objective(parameters)
            return [
                base_score * 2,  # Sharpe ratio (maximize)
                max(0.01, 0.2 - base_score * 0.1),  # Max drawdown (minimize)
                min(0.8, 0.4 + base_score * 0.3)  # Win rate (maximize)
            ]
        
        optimizer = MultiObjectiveOptimizer({
            'population_size': 20,
            'generations': 10,
            'objectives': ['sharpe_ratio', 'max_drawdown', 'win_rate'],
            'objective_directions': ['maximize', 'minimize', 'maximize']
        })
        
        config = OptimizationConfig(
            strategy_type='TBS',
            optimization_type='multi_objective',
            parameter_bounds=self.parameter_bounds,
            max_iterations=200  # 20 * 10
        )
        
        async def run_test():
            result = await optimizer.optimize(config, multi_objective_function)
            
            self.assertIsNotNone(result.best_parameters)
            self.assertIn('objective_value', result.best_performance)
            self.assertEqual(result.status, 'completed')
            
            # Check Pareto front
            pareto_front = optimizer.get_pareto_front()
            self.assertGreater(len(pareto_front), 0)
            
            logger.info(f"✅ Multi-objective optimization completed: Pareto front size = {len(pareto_front)}")
        
        asyncio.run(run_test())

class TestParallelOptimizer(unittest.TestCase):
    """Test parallel optimization functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.parameter_bounds = {
            'stop_loss': (0.01, 0.05),
            'take_profit': (0.02, 0.1),
            'position_size': (0.01, 0.05)
        }
        
        self.mock_objective = self._create_mock_objective_function()
    
    def _create_mock_objective_function(self):
        """Create mock objective function for testing"""
        def objective_function(parameters):
            # Simulate computation time
            time.sleep(0.1)
            
            x = parameters.get('stop_loss', 0.02)
            y = parameters.get('take_profit', 0.05)
            z = parameters.get('position_size', 0.02)
            
            score = -(x - 0.02)**2 - (y - 0.05)**2 - (z - 0.02)**2 + 1.0
            score += np.random.normal(0, 0.01)
            
            return max(0, score)
        
        return objective_function
    
    def test_parallel_optimization(self):
        """Test parallel optimization with multiple workers"""
        logger.info("⚡ Testing Parallel Optimizer...")
        
        optimizer = ParallelOptimizer({
            'max_workers': 4,
            'chunk_size': 5
        })
        
        config = OptimizationConfig(
            strategy_type='TBS',
            optimization_type='parameter',
            parallel_workers=4,
            parameter_bounds=self.parameter_bounds,
            max_iterations=20
        )
        
        async def run_test():
            start_time = time.time()
            
            result = await optimizer.optimize_parameters_parallel(
                config, self.mock_objective
            )
            
            execution_time = time.time() - start_time
            
            self.assertIsNotNone(result.best_parameters)
            self.assertGreater(result.best_performance['objective_value'], 0)
            self.assertEqual(result.status, 'completed')
            
            # Check performance metrics
            metrics = optimizer.get_performance_metrics()
            self.assertGreater(metrics['completed_tasks'], 0)
            self.assertGreaterEqual(metrics['worker_utilization'], 0)
            
            logger.info(f"✅ Parallel optimization completed in {execution_time:.2f}s")
            logger.info(f"   Best score: {result.best_performance['objective_value']:.4f}")
            logger.info(f"   Completed tasks: {metrics['completed_tasks']}")
            logger.info(f"   Worker utilization: {metrics['worker_utilization']:.2f}")
        
        asyncio.run(run_test())

class TestOptimizationEngine(unittest.TestCase):
    """Test the main optimization engine"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.engine = OptimizationEngine()
        self.parameter_bounds = {
            'stop_loss': (0.01, 0.05),
            'take_profit': (0.02, 0.1),
            'position_size': (0.01, 0.05)
        }
    
    def _create_mock_objective_function(self):
        """Create mock objective function"""
        def objective_function(parameters):
            x = parameters.get('stop_loss', 0.02)
            y = parameters.get('take_profit', 0.05)
            
            score = -(x - 0.02)**2 - (y - 0.05)**2 + 1.0
            score += np.random.normal(0, 0.01)
            
            return {
                'sharpe_ratio': max(0, score * 2),
                'max_drawdown': max(0.01, 0.2 - score * 0.1),
                'win_rate': min(0.8, 0.4 + score * 0.3)
            }
        
        return objective_function
    
    def test_optimization_engine_parameter(self):
        """Test optimization engine with parameter optimization"""
        logger.info("🔧 Testing Optimization Engine - Parameter Optimization...")
        
        config = OptimizationConfig(
            strategy_type='TBS',
            optimization_type='parameter',
            parallel_workers=2,
            max_iterations=20,
            parameter_bounds=self.parameter_bounds
        )
        
        objective_function = self._create_mock_objective_function()
        
        async def run_test():
            result = await self.engine.optimize_strategy(
                config, objective_function
            )
            
            self.assertIsNotNone(result.best_parameters)
            self.assertIn('objective_value', result.best_performance)
            self.assertEqual(result.status, 'completed')
            self.assertGreater(result.execution_time, 0)
            
            logger.info(f"✅ Parameter optimization completed in {result.execution_time:.2f}s")
        
        asyncio.run(run_test())
    
    def test_optimization_engine_performance(self):
        """Test optimization engine with performance optimization"""
        logger.info("📈 Testing Optimization Engine - Performance Optimization...")
        
        config = OptimizationConfig(
            strategy_type='TBS',
            optimization_type='performance',
            parallel_workers=2,
            max_iterations=20,
            parameter_bounds=self.parameter_bounds
        )
        
        objective_function = self._create_mock_objective_function()
        
        async def run_test():
            result = await self.engine.optimize_strategy(
                config, objective_function
            )
            
            self.assertIsNotNone(result.best_parameters)
            self.assertIn('objective_value', result.best_performance)
            self.assertEqual(result.status, 'completed')
            
            logger.info(f"✅ Performance optimization completed in {result.execution_time:.2f}s")
        
        asyncio.run(run_test())
    
    def test_portfolio_optimization(self):
        """Test portfolio optimization with multiple strategies"""
        logger.info("📊 Testing Portfolio Optimization...")
        
        strategies = [
            OptimizationConfig(
                strategy_type='TBS',
                optimization_type='parameter',
                parallel_workers=1,
                max_iterations=10,
                parameter_bounds=self.parameter_bounds
            ),
            OptimizationConfig(
                strategy_type='TV',
                optimization_type='parameter',
                parallel_workers=1,
                max_iterations=10,
                parameter_bounds=self.parameter_bounds
            )
        ]
        
        objective_function = self._create_mock_objective_function()
        
        async def run_test():
            results = await self.engine.optimize_portfolio(
                strategies, objective_function
            )
            
            self.assertEqual(len(results), 2)
            self.assertIn('TBS', results)
            self.assertIn('TV', results)
            
            for strategy_type, result in results.items():
                if result:  # Check if optimization succeeded
                    self.assertIsNotNone(result.best_parameters)
                    self.assertIn('objective_value', result.best_performance)
            
            logger.info(f"✅ Portfolio optimization completed")
        
        asyncio.run(run_test())

class TestPerformanceBenchmarks(unittest.TestCase):
    """Performance benchmarks for optimization algorithms"""
    
    def setUp(self):
        """Set up benchmark fixtures"""
        self.parameter_bounds = {
            'param1': (0, 1),
            'param2': (0, 1),
            'param3': (0, 1),
            'param4': (0, 1),
            'param5': (0, 1)
        }
        
        # Rosenbrock function for benchmarking
        def rosenbrock(parameters):
            x = [parameters.get(f'param{i+1}', 0.5) for i in range(5)]
            result = 0
            for i in range(len(x) - 1):
                result += 100 * (x[i+1] - x[i]**2)**2 + (1 - x[i])**2
            return -result  # Negative because we maximize
        
        self.benchmark_function = rosenbrock
    
    def test_algorithm_benchmark(self):
        """Benchmark different optimization algorithms"""
        logger.info("🏁 Running Algorithm Benchmark...")
        
        algorithms = ['grid', 'random', 'bayesian', 'genetic']
        results = {}
        
        async def run_benchmark():
            engine = OptimizationEngine()
            
            for algorithm in algorithms:
                logger.info(f"   Testing {algorithm} algorithm...")
                
                # Override algorithm in parameter optimizer
                engine.parameter_optimizer.config['algorithm'] = algorithm
                
                config = OptimizationConfig(
                    strategy_type='benchmark',
                    optimization_type='parameter',
                    max_iterations=50,
                    parameter_bounds=self.parameter_bounds
                )
                
                start_time = time.time()
                result = await engine.parameter_optimizer.optimize(
                    config, self.benchmark_function
                )
                execution_time = time.time() - start_time
                
                results[algorithm] = {
                    'best_score': result.best_performance['objective_value'],
                    'execution_time': execution_time,
                    'iterations': result.iterations_completed
                }
                
                logger.info(f"     {algorithm}: score={result.best_performance['objective_value']:.4f}, time={execution_time:.2f}s")
            
            # Print benchmark summary
            logger.info("📊 Benchmark Results Summary:")
            for algorithm, metrics in results.items():
                logger.info(f"   {algorithm:10s}: score={metrics['best_score']:8.4f}, time={metrics['execution_time']:6.2f}s")
        
        asyncio.run(run_benchmark())

def run_comprehensive_tests():
    """Run all tests in the comprehensive test suite"""
    
    logger.info("🚀 Starting Comprehensive Parallel Optimization System Tests...")
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestOptimizationComponents))
    test_suite.addTest(unittest.makeSuite(TestParallelOptimizer))
    test_suite.addTest(unittest.makeSuite(TestOptimizationEngine))
    test_suite.addTest(unittest.makeSuite(TestPerformanceBenchmarks))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    logger.info("=" * 80)
    if result.wasSuccessful():
        logger.info("✅ ALL TESTS PASSED! Parallel Optimization System is ready for production.")
    else:
        logger.error(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        
        for test, traceback in result.failures:
            logger.error(f"FAILURE: {test}")
            logger.error(traceback)
        
        for test, traceback in result.errors:
            logger.error(f"ERROR: {test}")
            logger.error(traceback)
    
    logger.info("=" * 80)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_comprehensive_tests()
    exit(0 if success else 1)

#!/usr/bin/env python3
"""
ATM Straddle ML Model
====================

Specialized ML model for At-The-Money (ATM) straddle strategies.
Focuses on volatility expansion and contraction patterns.

Key Features:
- Volatility prediction and modeling
- Time decay optimization
- Delta-neutral position management
- IV skew analysis
- Market regime adaptation
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import joblib
from sklearn.ensemble import RandomForestRegressor, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report

logger = logging.getLogger(__name__)

class ATMStraddleModel:
    """
    ML Model for ATM Straddle Strategy
    
    Predicts optimal entry/exit points for ATM straddles based on:
    - Implied volatility patterns
    - Market regime conditions
    - Technical indicators
    - Time decay factors
    - Volume and open interest patterns
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize ATM Straddle Model"""
        self.config = config or self._get_default_config()
        self.models = {}
        self.scalers = {}
        self.feature_names = []
        self.is_trained = False
        self.performance_metrics = {}
        
        logger.info("🎯 ATM Straddle Model initialized")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default model configuration"""
        return {
            'model_types': {
                'volatility_predictor': 'random_forest',
                'direction_classifier': 'gradient_boosting',
                'entry_timing': 'logistic_regression',
                'exit_timing': 'svm'
            },
            'hyperparameters': {
                'random_forest': {
                    'n_estimators': 100,
                    'max_depth': 10,
                    'min_samples_split': 5,
                    'random_state': 42
                },
                'gradient_boosting': {
                    'n_estimators': 100,
                    'learning_rate': 0.1,
                    'max_depth': 6,
                    'random_state': 42
                },
                'logistic_regression': {
                    'C': 1.0,
                    'random_state': 42,
                    'max_iter': 1000
                },
                'svm': {
                    'C': 1.0,
                    'kernel': 'rbf',
                    'random_state': 42
                }
            },
            'features': {
                'technical': [
                    'current_price', 'ema_3', 'ema_5', 'ema_10', 'ema_15',
                    'vwap', 'previous_day_vwap', 'rsi', 'macd', 'bollinger_position'
                ],
                'volatility': [
                    'current_iv', 'iv_percentile', 'iv_skew', 'realized_vol_5d',
                    'realized_vol_20d', 'iv_rank', 'hv_iv_ratio'
                ],
                'market_structure': [
                    'volume_ratio', 'open_interest_change', 'put_call_ratio',
                    'smart_money_index', 'institutional_flow'
                ],
                'regime': [
                    'regime_bullish', 'regime_bearish', 'regime_neutral',
                    'regime_high_vol', 'regime_normal_vol', 'regime_low_vol',
                    'regime_confidence', 'regime_stability'
                ],
                'time': [
                    'time_to_expiry', 'days_to_expiry', 'hour_of_day',
                    'day_of_week', 'is_expiry_week'
                ]
            },
            'targets': {
                'volatility_expansion': 'vol_expansion_5min',
                'direction': 'price_direction_15min',
                'entry_signal': 'optimal_entry',
                'exit_signal': 'optimal_exit'
            },
            'thresholds': {
                'min_confidence': 0.65,
                'min_iv_percentile': 20,
                'max_iv_percentile': 80,
                'min_time_to_expiry': 30,  # minutes
                'max_time_to_expiry': 240  # minutes
            }
        }
    
    async def initialize(self):
        """Initialize the model components"""
        try:
            logger.info("🚀 Initializing ATM Straddle Model...")
            
            # Initialize models
            self._initialize_models()
            
            # Initialize scalers
            self._initialize_scalers()
            
            # Try to load pre-trained models
            await self._load_pretrained_models()
            
            logger.info("✅ ATM Straddle Model initialization complete")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize ATM Straddle Model: {e}")
            raise
    
    def _initialize_models(self):
        """Initialize ML model instances"""
        config = self.config['hyperparameters']
        
        self.models = {
            'volatility_predictor': RandomForestRegressor(**config['random_forest']),
            'direction_classifier': GradientBoostingClassifier(**config['gradient_boosting']),
            'entry_timing': LogisticRegression(**config['logistic_regression']),
            'exit_timing': SVC(**config['svm'], probability=True)
        }
        
        logger.info("✅ ML model instances initialized")
    
    def _initialize_scalers(self):
        """Initialize feature scalers"""
        self.scalers = {
            'volatility_predictor': StandardScaler(),
            'direction_classifier': StandardScaler(),
            'entry_timing': StandardScaler(),
            'exit_timing': StandardScaler()
        }
        
        logger.info("✅ Feature scalers initialized")
    
    async def _load_pretrained_models(self):
        """Load pre-trained models if available"""
        try:
            # Try to load models from disk
            model_path = self.config.get('model_path', 'models/atm_straddle_model.pkl')
            
            # For now, we'll use mock pre-trained models
            # In production, this would load actual trained models
            logger.info("📁 Loading pre-trained ATM straddle models...")
            
            # Mock training data for demonstration
            await self._create_mock_training_data()
            
            self.is_trained = True
            logger.info("✅ Pre-trained models loaded successfully")
            
        except Exception as e:
            logger.warning(f"⚠️ Could not load pre-trained models: {e}")
            logger.info("🔄 Will use default models (training required)")
    
    async def _create_mock_training_data(self):
        """Create mock training data for demonstration"""
        # Generate synthetic training data
        n_samples = 1000
        np.random.seed(42)
        
        # Generate features
        features = {}
        
        # Technical features
        features.update({
            'current_price': np.random.normal(100, 10, n_samples),
            'ema_3': np.random.normal(100, 8, n_samples),
            'ema_5': np.random.normal(100, 7, n_samples),
            'ema_10': np.random.normal(100, 6, n_samples),
            'ema_15': np.random.normal(100, 5, n_samples),
            'vwap': np.random.normal(100, 5, n_samples),
            'previous_day_vwap': np.random.normal(100, 5, n_samples),
            'rsi': np.random.uniform(20, 80, n_samples),
            'macd': np.random.normal(0, 2, n_samples),
            'bollinger_position': np.random.uniform(-1, 1, n_samples)
        })
        
        # Volatility features
        features.update({
            'current_iv': np.random.uniform(0.1, 0.5, n_samples),
            'iv_percentile': np.random.uniform(0, 100, n_samples),
            'iv_skew': np.random.normal(0, 0.1, n_samples),
            'realized_vol_5d': np.random.uniform(0.1, 0.4, n_samples),
            'realized_vol_20d': np.random.uniform(0.1, 0.3, n_samples),
            'iv_rank': np.random.uniform(0, 100, n_samples),
            'hv_iv_ratio': np.random.uniform(0.5, 1.5, n_samples)
        })
        
        # Market structure features
        features.update({
            'volume_ratio': np.random.uniform(0.5, 2.0, n_samples),
            'open_interest_change': np.random.normal(0, 0.1, n_samples),
            'put_call_ratio': np.random.uniform(0.5, 1.5, n_samples),
            'smart_money_index': np.random.uniform(0, 1, n_samples),
            'institutional_flow': np.random.normal(0, 1, n_samples)
        })
        
        # Regime features (one-hot encoded)
        regime_types = np.random.choice([0, 1], size=(n_samples, 6))
        features.update({
            'regime_bullish': regime_types[:, 0],
            'regime_bearish': regime_types[:, 1],
            'regime_neutral': regime_types[:, 2],
            'regime_high_vol': regime_types[:, 3],
            'regime_normal_vol': regime_types[:, 4],
            'regime_low_vol': regime_types[:, 5],
            'regime_confidence': np.random.uniform(0.6, 0.95, n_samples),
            'regime_stability': np.random.uniform(0.5, 0.9, n_samples)
        })
        
        # Time features
        features.update({
            'time_to_expiry': np.random.uniform(30, 240, n_samples),
            'days_to_expiry': np.random.uniform(1, 10, n_samples),
            'hour_of_day': np.random.randint(9, 16, n_samples),
            'day_of_week': np.random.randint(0, 5, n_samples),
            'is_expiry_week': np.random.choice([0, 1], n_samples)
        })
        
        # Create DataFrame
        X = pd.DataFrame(features)
        self.feature_names = list(X.columns)
        
        # Generate targets
        y_volatility = np.random.uniform(-0.1, 0.1, n_samples)  # Volatility expansion
        y_direction = np.random.choice([0, 1], n_samples)  # Price direction
        y_entry = np.random.choice([0, 1], n_samples)  # Entry signal
        y_exit = np.random.choice([0, 1], n_samples)  # Exit signal
        
        # Train models with mock data
        X_scaled = self.scalers['volatility_predictor'].fit_transform(X)
        self.models['volatility_predictor'].fit(X_scaled, y_volatility)
        
        X_scaled = self.scalers['direction_classifier'].fit_transform(X)
        self.models['direction_classifier'].fit(X_scaled, y_direction)
        
        X_scaled = self.scalers['entry_timing'].fit_transform(X)
        self.models['entry_timing'].fit(X_scaled, y_entry)
        
        X_scaled = self.scalers['exit_timing'].fit_transform(X)
        self.models['exit_timing'].fit(X_scaled, y_exit)
        
        logger.info("✅ Mock training completed")
    
    async def predict(self, features: Dict[str, float]) -> Dict[str, Any]:
        """
        Generate ATM straddle predictions
        
        Args:
            features: Dictionary of feature values
            
        Returns:
            Dictionary containing predictions and confidence scores
        """
        try:
            if not self.is_trained:
                raise ValueError("Model not trained. Call initialize() first.")
            
            # Convert features to DataFrame
            feature_df = pd.DataFrame([features])
            
            # Ensure all required features are present
            missing_features = set(self.feature_names) - set(feature_df.columns)
            if missing_features:
                logger.warning(f"⚠️ Missing features: {missing_features}")
                # Fill missing features with defaults
                for feature in missing_features:
                    feature_df[feature] = 0.0
            
            # Reorder columns to match training data
            feature_df = feature_df[self.feature_names]
            
            # Generate predictions
            predictions = {}
            
            # Volatility expansion prediction
            X_vol = self.scalers['volatility_predictor'].transform(feature_df)
            vol_expansion = self.models['volatility_predictor'].predict(X_vol)[0]
            predictions['volatility_expansion'] = vol_expansion
            
            # Direction prediction
            X_dir = self.scalers['direction_classifier'].transform(feature_df)
            direction_proba = self.models['direction_classifier'].predict_proba(X_dir)[0]
            predictions['direction_probability'] = direction_proba[1]  # Probability of upward movement
            
            # Entry timing prediction
            X_entry = self.scalers['entry_timing'].transform(feature_df)
            entry_proba = self.models['entry_timing'].predict_proba(X_entry)[0]
            predictions['entry_probability'] = entry_proba[1]  # Probability of good entry
            
            # Exit timing prediction
            X_exit = self.scalers['exit_timing'].transform(feature_df)
            exit_proba = self.models['exit_timing'].predict_proba(X_exit)[0]
            predictions['exit_probability'] = exit_proba[1]  # Probability of good exit
            
            # Generate overall signal
            signal = self._generate_signal(predictions, features)
            
            return signal
            
        except Exception as e:
            logger.error(f"❌ Prediction failed: {e}")
            return self._get_default_prediction()
    
    def _generate_signal(self, predictions: Dict[str, float], features: Dict[str, float]) -> Dict[str, Any]:
        """Generate trading signal from predictions"""
        
        # Calculate overall confidence
        confidence_factors = [
            predictions['entry_probability'],
            abs(predictions['volatility_expansion']) * 10,  # Scale volatility prediction
            predictions['direction_probability'] if predictions['direction_probability'] > 0.5 else (1 - predictions['direction_probability'])
        ]
        
        overall_confidence = np.mean(confidence_factors)
        
        # Determine prediction type
        if predictions['entry_probability'] > 0.65 and abs(predictions['volatility_expansion']) > 0.02:
            if predictions['volatility_expansion'] > 0:
                prediction = 'BUY'  # Expect volatility expansion
            else:
                prediction = 'SELL'  # Expect volatility contraction
        else:
            prediction = 'HOLD'
        
        # Calculate position sizing
        position_size = min(0.1, overall_confidence * 0.15)  # Max 10% position
        
        # Calculate entry/exit prices (simplified)
        current_price = features.get('current_price', 100)
        entry_price = current_price
        
        if prediction == 'BUY':
            target_price = current_price * (1 + abs(predictions['volatility_expansion']))
            stop_loss = current_price * (1 - abs(predictions['volatility_expansion']) * 0.5)
        elif prediction == 'SELL':
            target_price = current_price * (1 - abs(predictions['volatility_expansion']))
            stop_loss = current_price * (1 + abs(predictions['volatility_expansion']) * 0.5)
        else:
            target_price = None
            stop_loss = None
        
        # Feature importance (mock)
        feature_importance = {
            'current_iv': 0.25,
            'iv_percentile': 0.20,
            'volatility_expansion': 0.18,
            'regime_confidence': 0.15,
            'time_to_expiry': 0.12,
            'volume_ratio': 0.10
        }
        
        return {
            'prediction': prediction,
            'confidence': overall_confidence,
            'entry_price': entry_price,
            'target_price': target_price,
            'stop_loss': stop_loss,
            'position_size': position_size,
            'iv_prediction': features.get('current_iv', 0.2) + predictions['volatility_expansion'],
            'regime_alignment': features.get('regime_confidence', 0.7),
            'feature_importance': feature_importance,
            'raw_predictions': predictions
        }
    
    def _get_default_prediction(self) -> Dict[str, Any]:
        """Get default prediction when model fails"""
        return {
            'prediction': 'HOLD',
            'confidence': 0.0,
            'entry_price': None,
            'target_price': None,
            'stop_loss': None,
            'position_size': 0.0,
            'iv_prediction': None,
            'regime_alignment': 0.0,
            'feature_importance': {},
            'raw_predictions': {}
        }
    
    async def train(self, training_data: pd.DataFrame, targets: Dict[str, pd.Series]):
        """Train the model with new data"""
        try:
            logger.info("🎓 Training ATM Straddle Model...")
            
            # Prepare features
            X = training_data[self.feature_names]
            
            # Train each model
            for model_name, model in self.models.items():
                if model_name in targets:
                    y = targets[model_name]
                    
                    # Scale features
                    X_scaled = self.scalers[model_name].fit_transform(X)
                    
                    # Train model
                    model.fit(X_scaled, y)
                    
                    logger.info(f"✅ {model_name} trained successfully")
            
            self.is_trained = True
            logger.info("✅ ATM Straddle Model training complete")
            
        except Exception as e:
            logger.error(f"❌ Training failed: {e}")
            raise
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information and statistics"""
        return {
            'model_type': 'ATM_Straddle',
            'is_trained': self.is_trained,
            'feature_count': len(self.feature_names),
            'model_components': list(self.models.keys()),
            'performance_metrics': self.performance_metrics,
            'config': self.config
        }

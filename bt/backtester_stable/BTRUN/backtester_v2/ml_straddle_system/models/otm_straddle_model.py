#!/usr/bin/env python3
"""
OTM Straddle ML Model
====================

Specialized ML model for Out-of-The-Money (OTM) straddle strategies.
Focuses on volatility expansion and low-probability, high-reward scenarios.
"""

import logging
import numpy as np
from typing import Dict, Any, Optional
from .atm_straddle_model import ATMStraddleModel

logger = logging.getLogger(__name__)

class OTMStraddleModel(ATMStraddleModel):
    """OTM Straddle Model - inherits from ATM with OTM-specific adjustments"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize OTM Straddle Model"""
        super().__init__(config)
        self.model_type = 'OTM_STRADDLE'
        logger.info("🎯 OTM Straddle Model initialized")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get OTM-specific configuration"""
        config = super()._get_default_config()
        
        # OTM-specific adjustments
        config['thresholds'].update({
            'min_confidence': 0.75,  # Higher confidence for OTM
            'min_iv_expansion': 0.3, # Minimum IV expansion expected
            'max_time_decay': 0.1    # Maximum acceptable time decay
        })
        
        config['features']['volatility_expansion'] = [
            'iv_expansion_probability', 'volatility_breakout_score',
            'event_proximity', 'earnings_impact', 'news_sentiment'
        ]
        
        return config
    
    def _generate_signal(self, predictions: Dict[str, float], features: Dict[str, float]) -> Dict[str, Any]:
        """Generate OTM-specific trading signal"""
        
        # Get base signal
        signal = super()._generate_signal(predictions, features)
        
        # OTM-specific adjustments
        if signal['prediction'] != 'HOLD':
            # Adjust for volatility expansion probability
            vol_expansion_prob = self._calculate_volatility_expansion_probability(features)
            signal['confidence'] *= vol_expansion_prob
            
            # OTM position sizing (larger due to lower cost)
            signal['position_size'] *= 1.5  # 50% increase for OTM
            
            # OTM-specific risk management
            signal['stop_loss'] *= 0.8  # Tighter stops for OTM
            signal['target_price'] *= 2.0  # Higher targets for OTM
        
        return signal
    
    def _calculate_volatility_expansion_probability(self, features: Dict[str, float]) -> float:
        """Calculate probability of volatility expansion"""
        
        # Simplified volatility expansion analysis
        iv_rank = features.get('iv_rank', 50.0) / 100.0
        volume_surge = features.get('volume_ratio', 1.0)
        
        # Higher probability when IV is low and volume is high
        expansion_prob = (1 - iv_rank) * min(volume_surge / 2.0, 1.0)
        return max(0.3, min(1.0, expansion_prob))

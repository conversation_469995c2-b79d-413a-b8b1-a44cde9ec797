#!/usr/bin/env python3
"""
Triple Straddle ML Model
=======================

Specialized ML model for Triple Straddle strategies.
Combines ATM, ITM, and OTM positions for optimized risk/reward.
"""

import logging
import numpy as np
from typing import Dict, Any, Optional
from .atm_straddle_model import ATMStraddleModel

logger = logging.getLogger(__name__)

class TripleStraddleModel(ATMStraddleModel):
    """Triple Straddle Model - multi-leg optimization"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize Triple Straddle Model"""
        super().__init__(config)
        self.model_type = 'TRIPLE_STRADDLE'
        logger.info("🎯 Triple Straddle Model initialized")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get Triple-specific configuration"""
        config = super()._get_default_config()
        
        # Triple-specific adjustments
        config['thresholds'].update({
            'min_confidence': 0.80,  # Highest confidence for complex strategy
            'min_correlation': 0.6,  # Minimum correlation between legs
            'max_complexity_cost': 0.05  # Maximum cost for complexity
        })
        
        config['features']['multi_leg'] = [
            'leg_correlation', 'portfolio_balance', 'execution_cost',
            'liquidity_score', 'complexity_premium'
        ]
        
        config['leg_weights'] = {
            'atm_weight': 0.5,
            'itm_weight': 0.3,
            'otm_weight': 0.2
        }
        
        return config
    
    def _generate_signal(self, predictions: Dict[str, float], features: Dict[str, float]) -> Dict[str, Any]:
        """Generate Triple-specific trading signal"""
        
        # Get base signal
        signal = super()._generate_signal(predictions, features)
        
        # Triple-specific adjustments
        if signal['prediction'] != 'HOLD':
            # Calculate multi-leg optimization
            leg_optimization = self._calculate_leg_optimization(features)
            signal['confidence'] *= leg_optimization['efficiency']
            
            # Triple position sizing (distributed across legs)
            signal['position_size'] *= 0.6  # Reduced due to complexity
            
            # Add leg-specific information
            signal['leg_allocation'] = leg_optimization['allocation']
            signal['execution_complexity'] = leg_optimization['complexity']
        
        return signal
    
    def _calculate_leg_optimization(self, features: Dict[str, float]) -> Dict[str, Any]:
        """Calculate optimal allocation across triple straddle legs"""
        
        weights = self.config['leg_weights']
        
        # Simplified optimization
        atm_efficiency = features.get('atm_efficiency', 0.7)
        itm_efficiency = features.get('itm_efficiency', 0.6)
        otm_efficiency = features.get('otm_efficiency', 0.5)
        
        # Weighted efficiency
        total_efficiency = (
            atm_efficiency * weights['atm_weight'] +
            itm_efficiency * weights['itm_weight'] +
            otm_efficiency * weights['otm_weight']
        )
        
        return {
            'efficiency': total_efficiency,
            'allocation': weights,
            'complexity': 0.8  # High complexity score
        }

#!/usr/bin/env python3
"""
ITM Straddle ML Model
====================

Specialized ML model for In-The-Money (ITM) straddle strategies.
Focuses on directional bias and delta management.
"""

import logging
import numpy as np
from typing import Dict, Any, Optional
from .atm_straddle_model import ATMStraddleModel

logger = logging.getLogger(__name__)

class ITMStraddleModel(ATMStraddleModel):
    """ITM Straddle Model - inherits from ATM with ITM-specific adjustments"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize ITM Straddle Model"""
        super().__init__(config)
        self.model_type = 'ITM_STRADDLE'
        logger.info("🎯 ITM Straddle Model initialized")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get ITM-specific configuration"""
        config = super()._get_default_config()
        
        # ITM-specific adjustments
        config['thresholds'].update({
            'min_confidence': 0.70,  # Higher confidence for ITM
            'min_delta': 0.3,        # Minimum delta for ITM
            'max_delta': 0.7         # Maximum delta for ITM
        })
        
        config['features']['directional'] = [
            'trend_strength', 'momentum_score', 'support_resistance',
            'volume_profile', 'institutional_flow'
        ]
        
        return config
    
    def _generate_signal(self, predictions: Dict[str, float], features: Dict[str, float]) -> Dict[str, Any]:
        """Generate ITM-specific trading signal"""
        
        # Get base signal
        signal = super()._generate_signal(predictions, features)
        
        # ITM-specific adjustments
        if signal['prediction'] != 'HOLD':
            # Adjust for directional bias
            directional_confidence = self._calculate_directional_confidence(features)
            signal['confidence'] *= directional_confidence
            
            # ITM position sizing (typically smaller due to higher delta)
            signal['position_size'] *= 0.8  # 20% reduction for ITM
            
            # ITM-specific risk management
            signal['stop_loss'] *= 1.2  # Wider stops for ITM
            signal['target_price'] *= 0.9  # More conservative targets
        
        return signal
    
    def _calculate_directional_confidence(self, features: Dict[str, float]) -> float:
        """Calculate confidence based on directional indicators"""
        
        # Simplified directional analysis
        trend_strength = features.get('trend_strength', 0.5)
        momentum_score = features.get('momentum_score', 0.5)
        
        directional_confidence = (trend_strength + momentum_score) / 2
        return max(0.5, min(1.0, directional_confidence))

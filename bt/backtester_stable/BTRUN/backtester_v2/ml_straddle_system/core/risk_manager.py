#!/usr/bin/env python3
"""
Risk Manager for ML Straddle System
===================================

Comprehensive risk management for straddle strategies:
- Position sizing and limits
- Portfolio risk monitoring
- Dynamic risk adjustments
- Stop loss and profit taking
- Exposure management
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class RiskLimits:
    """Risk limits configuration"""
    max_position_size: float
    max_portfolio_exposure: float
    max_daily_loss: float
    max_drawdown: float
    max_concentration: float
    min_liquidity_ratio: float

@dataclass
class RiskMetrics:
    """Current risk metrics"""
    current_exposure: float
    portfolio_var: float
    daily_pnl: float
    max_drawdown: float
    sharpe_ratio: float
    risk_score: float

class RiskManager:
    """
    Comprehensive Risk Manager for ML Straddle System
    
    Features:
    - Real-time risk monitoring
    - Dynamic position sizing
    - Portfolio exposure limits
    - Risk-adjusted signal filtering
    - Emergency risk controls
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize Risk Manager"""
        self.config = config or self._get_default_config()
        self.risk_limits = self._create_risk_limits()
        self.positions = []
        self.pnl_history = []
        self.risk_events = []
        
        logger.info("🛡️ Risk Manager initialized")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default risk management configuration"""
        return {
            'position_limits': {
                'max_position_size': 0.1,      # 10% of portfolio
                'max_portfolio_exposure': 0.5,  # 50% total exposure
                'max_concentration': 0.3,       # 30% in single strategy
                'min_position_size': 0.001      # 0.1% minimum
            },
            'loss_limits': {
                'max_daily_loss': 0.05,        # 5% daily loss limit
                'max_drawdown': 0.15,          # 15% max drawdown
                'stop_loss_multiplier': 2.0,   # 2x premium stop loss
                'profit_target_multiplier': 1.5 # 1.5x premium profit target
            },
            'risk_metrics': {
                'var_confidence': 0.95,        # 95% VaR confidence
                'lookback_days': 30,           # 30-day lookback
                'rebalance_threshold': 0.1,    # 10% rebalance threshold
                'emergency_threshold': 0.8     # 80% of limit triggers emergency
            },
            'market_conditions': {
                'high_vol_multiplier': 0.7,    # Reduce size in high vol
                'low_liquidity_multiplier': 0.5, # Reduce size in low liquidity
                'regime_adjustment': True,      # Adjust for market regime
                'correlation_limit': 0.7       # Max correlation between positions
            }
        }
    
    def _create_risk_limits(self) -> RiskLimits:
        """Create risk limits from configuration"""
        pos_config = self.config['position_limits']
        loss_config = self.config['loss_limits']
        
        return RiskLimits(
            max_position_size=pos_config['max_position_size'],
            max_portfolio_exposure=pos_config['max_portfolio_exposure'],
            max_daily_loss=loss_config['max_daily_loss'],
            max_drawdown=loss_config['max_drawdown'],
            max_concentration=pos_config['max_concentration'],
            min_liquidity_ratio=0.2  # 20% minimum liquidity
        )
    
    async def initialize(self):
        """Initialize risk management system"""
        try:
            logger.info("🚀 Initializing Risk Manager...")
            
            # Initialize risk monitoring
            await self._initialize_risk_monitoring()
            
            # Load historical data if available
            await self._load_historical_data()
            
            logger.info("✅ Risk Manager initialization complete")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Risk Manager: {e}")
            raise
    
    async def _initialize_risk_monitoring(self):
        """Initialize risk monitoring components"""
        # Initialize risk tracking
        self.risk_metrics = RiskMetrics(
            current_exposure=0.0,
            portfolio_var=0.0,
            daily_pnl=0.0,
            max_drawdown=0.0,
            sharpe_ratio=0.0,
            risk_score=0.0
        )
        
        logger.info("✅ Risk monitoring initialized")
    
    async def _load_historical_data(self):
        """Load historical risk data"""
        # In production, this would load from database
        # For now, initialize with empty history
        self.pnl_history = []
        logger.info("✅ Historical risk data loaded")
    
    async def adjust_signal(
        self,
        prediction_result: Dict[str, Any],
        market_context: Any
    ) -> Dict[str, Any]:
        """
        Adjust trading signal based on risk management rules
        
        Args:
            prediction_result: Raw ML prediction
            market_context: Current market conditions
            
        Returns:
            Risk-adjusted signal
        """
        try:
            # Start with original prediction
            adjusted_signal = prediction_result.copy()
            
            # Check risk limits
            risk_check = await self._check_risk_limits(prediction_result, market_context)
            
            if not risk_check['allowed']:
                # Block signal if risk limits exceeded
                adjusted_signal['prediction'] = 'HOLD'
                adjusted_signal['confidence'] = 0.0
                adjusted_signal['risk_reason'] = risk_check['reason']
                
                logger.warning(f"🛡️ Signal blocked by risk management: {risk_check['reason']}")
                return adjusted_signal
            
            # Adjust position size based on risk
            original_size = prediction_result.get('position_size', 0.02)
            adjusted_size = await self._calculate_risk_adjusted_size(
                original_size, prediction_result, market_context
            )
            
            adjusted_signal['position_size'] = adjusted_size
            
            # Adjust stop loss and profit targets
            risk_adjusted_levels = await self._calculate_risk_levels(
                prediction_result, market_context
            )
            
            adjusted_signal.update(risk_adjusted_levels)
            
            # Add risk metadata
            adjusted_signal['risk_score'] = await self._calculate_signal_risk_score(
                adjusted_signal, market_context
            )
            
            logger.debug(f"🛡️ Signal adjusted: size {original_size:.3f} → {adjusted_size:.3f}")
            
            return adjusted_signal
            
        except Exception as e:
            logger.error(f"❌ Signal adjustment failed: {e}")
            # Return safe default
            return {
                'prediction': 'HOLD',
                'confidence': 0.0,
                'position_size': 0.0,
                'risk_reason': f'Risk adjustment failed: {e}'
            }
    
    async def _check_risk_limits(
        self,
        prediction_result: Dict[str, Any],
        market_context: Any
    ) -> Dict[str, Any]:
        """Check if signal violates risk limits"""
        
        # Check portfolio exposure
        current_exposure = await self._calculate_current_exposure()
        proposed_size = prediction_result.get('position_size', 0.02)
        
        if current_exposure + proposed_size > self.risk_limits.max_portfolio_exposure:
            return {
                'allowed': False,
                'reason': f'Portfolio exposure limit exceeded: {current_exposure + proposed_size:.1%} > {self.risk_limits.max_portfolio_exposure:.1%}'
            }
        
        # Check daily loss limit
        daily_pnl = await self._calculate_daily_pnl()
        if daily_pnl < -self.risk_limits.max_daily_loss:
            return {
                'allowed': False,
                'reason': f'Daily loss limit exceeded: {daily_pnl:.1%} < -{self.risk_limits.max_daily_loss:.1%}'
            }
        
        # Check position size limit
        if proposed_size > self.risk_limits.max_position_size:
            return {
                'allowed': False,
                'reason': f'Position size limit exceeded: {proposed_size:.1%} > {self.risk_limits.max_position_size:.1%}'
            }
        
        # Check market conditions
        market_risk_check = await self._check_market_conditions(market_context)
        if not market_risk_check['allowed']:
            return market_risk_check
        
        return {'allowed': True, 'reason': 'All risk checks passed'}
    
    async def _calculate_current_exposure(self) -> float:
        """Calculate current portfolio exposure"""
        # Sum up all active position sizes
        total_exposure = sum(pos.get('position_size', 0) for pos in self.positions)
        return total_exposure
    
    async def _calculate_daily_pnl(self) -> float:
        """Calculate today's P&L"""
        today = datetime.now().date()
        
        daily_pnl = 0.0
        for pnl_entry in self.pnl_history:
            if pnl_entry.get('date', datetime.now().date()) == today:
                daily_pnl += pnl_entry.get('pnl', 0.0)
        
        return daily_pnl
    
    async def _check_market_conditions(self, market_context: Any) -> Dict[str, Any]:
        """Check market conditions for additional risk"""
        
        # Check volatility regime
        current_iv = getattr(market_context, 'current_iv', 0.2)
        if current_iv > 0.4:  # High volatility
            return {
                'allowed': False,
                'reason': f'High volatility environment: IV={current_iv:.1%} > 40%'
            }
        
        # Check volume
        volume_ratio = getattr(market_context, 'current_volume', 1000) / 1000  # Simplified
        if volume_ratio < 0.5:  # Low volume
            return {
                'allowed': False,
                'reason': f'Low volume environment: ratio={volume_ratio:.2f} < 0.5'
            }
        
        # Check time to expiry
        time_to_expiry = getattr(market_context, 'time_to_expiry', 60)
        if time_to_expiry < 15:  # Too close to expiry
            return {
                'allowed': False,
                'reason': f'Too close to expiry: {time_to_expiry} minutes < 15'
            }
        
        return {'allowed': True, 'reason': 'Market conditions acceptable'}
    
    async def _calculate_risk_adjusted_size(
        self,
        original_size: float,
        prediction_result: Dict[str, Any],
        market_context: Any
    ) -> float:
        """Calculate risk-adjusted position size"""
        
        adjusted_size = original_size
        
        # Confidence adjustment
        confidence = prediction_result.get('confidence', 0.5)
        confidence_multiplier = 0.5 + confidence  # 0.5 to 1.5 range
        adjusted_size *= confidence_multiplier
        
        # Volatility adjustment
        current_iv = getattr(market_context, 'current_iv', 0.2)
        if current_iv > 0.3:
            vol_multiplier = self.config['market_conditions']['high_vol_multiplier']
            adjusted_size *= vol_multiplier
        
        # Portfolio heat adjustment
        current_exposure = await self._calculate_current_exposure()
        if current_exposure > 0.3:  # Already 30% exposed
            heat_multiplier = max(0.5, 1 - (current_exposure - 0.3) * 2)
            adjusted_size *= heat_multiplier
        
        # Apply limits
        adjusted_size = max(
            self.config['position_limits']['min_position_size'],
            min(adjusted_size, self.risk_limits.max_position_size)
        )
        
        return adjusted_size
    
    async def _calculate_risk_levels(
        self,
        prediction_result: Dict[str, Any],
        market_context: Any
    ) -> Dict[str, float]:
        """Calculate risk-adjusted stop loss and profit targets"""
        
        entry_price = prediction_result.get('entry_price', 100.0)
        position_size = prediction_result.get('position_size', 0.02)
        
        # Calculate premium (simplified)
        estimated_premium = entry_price * 0.02  # 2% of underlying
        
        # Stop loss calculation
        stop_loss_multiplier = self.config['loss_limits']['stop_loss_multiplier']
        stop_loss_amount = estimated_premium * stop_loss_multiplier
        
        # Profit target calculation
        profit_target_multiplier = self.config['loss_limits']['profit_target_multiplier']
        profit_target_amount = estimated_premium * profit_target_multiplier
        
        # Adjust for volatility
        current_iv = getattr(market_context, 'current_iv', 0.2)
        vol_adjustment = 1 + (current_iv - 0.2) * 2  # Wider stops in high vol
        
        return {
            'stop_loss': stop_loss_amount * vol_adjustment,
            'profit_target': profit_target_amount,
            'risk_amount': stop_loss_amount * position_size,
            'reward_amount': profit_target_amount * position_size,
            'risk_reward_ratio': profit_target_amount / (stop_loss_amount * vol_adjustment)
        }
    
    async def _calculate_signal_risk_score(
        self,
        signal: Dict[str, Any],
        market_context: Any
    ) -> float:
        """Calculate overall risk score for the signal"""
        
        risk_factors = []
        
        # Position size risk
        position_size = signal.get('position_size', 0.02)
        size_risk = position_size / self.risk_limits.max_position_size
        risk_factors.append(size_risk)
        
        # Confidence risk (inverse)
        confidence = signal.get('confidence', 0.5)
        confidence_risk = 1 - confidence
        risk_factors.append(confidence_risk)
        
        # Market volatility risk
        current_iv = getattr(market_context, 'current_iv', 0.2)
        vol_risk = min(1.0, current_iv / 0.4)  # Normalize to 40% IV
        risk_factors.append(vol_risk)
        
        # Time risk
        time_to_expiry = getattr(market_context, 'time_to_expiry', 60)
        time_risk = max(0, 1 - time_to_expiry / 120)  # Risk increases as expiry approaches
        risk_factors.append(time_risk)
        
        # Portfolio concentration risk
        current_exposure = await self._calculate_current_exposure()
        concentration_risk = current_exposure / self.risk_limits.max_portfolio_exposure
        risk_factors.append(concentration_risk)
        
        # Calculate weighted average
        weights = [0.3, 0.2, 0.2, 0.15, 0.15]  # Position size and confidence are most important
        risk_score = sum(factor * weight for factor, weight in zip(risk_factors, weights))
        
        return min(1.0, max(0.0, risk_score))
    
    async def monitor_positions(self, positions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Monitor active positions for risk management"""
        
        try:
            self.positions = positions
            
            # Calculate current risk metrics
            risk_metrics = await self._calculate_risk_metrics()
            
            # Check for risk violations
            violations = await self._check_risk_violations(risk_metrics)
            
            # Generate risk actions if needed
            actions = await self._generate_risk_actions(violations)
            
            return {
                'risk_metrics': risk_metrics,
                'violations': violations,
                'actions': actions,
                'monitoring_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Position monitoring failed: {e}")
            return {
                'risk_metrics': {},
                'violations': [],
                'actions': [],
                'error': str(e)
            }
    
    async def _calculate_risk_metrics(self) -> Dict[str, float]:
        """Calculate current portfolio risk metrics"""
        
        if not self.positions:
            return {
                'total_exposure': 0.0,
                'portfolio_var': 0.0,
                'max_drawdown': 0.0,
                'sharpe_ratio': 0.0,
                'risk_score': 0.0
            }
        
        # Calculate total exposure
        total_exposure = sum(pos.get('position_size', 0) for pos in self.positions)
        
        # Calculate portfolio VaR (simplified)
        position_values = [pos.get('current_pnl', 0) for pos in self.positions]
        portfolio_var = np.percentile(position_values, 5) if position_values else 0.0  # 5% VaR
        
        # Calculate max drawdown
        if self.pnl_history:
            cumulative_pnl = np.cumsum([entry.get('pnl', 0) for entry in self.pnl_history])
            running_max = np.maximum.accumulate(cumulative_pnl)
            drawdowns = (cumulative_pnl - running_max) / np.maximum(running_max, 1)
            max_drawdown = np.min(drawdowns) if len(drawdowns) > 0 else 0.0
        else:
            max_drawdown = 0.0
        
        # Calculate Sharpe ratio (simplified)
        if self.pnl_history and len(self.pnl_history) > 1:
            returns = [entry.get('pnl', 0) for entry in self.pnl_history]
            sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0.0
        else:
            sharpe_ratio = 0.0
        
        # Overall risk score
        risk_score = min(1.0, total_exposure / self.risk_limits.max_portfolio_exposure)
        
        return {
            'total_exposure': total_exposure,
            'portfolio_var': portfolio_var,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'risk_score': risk_score
        }
    
    async def _check_risk_violations(self, risk_metrics: Dict[str, float]) -> List[Dict[str, Any]]:
        """Check for risk limit violations"""
        
        violations = []
        
        # Check exposure limit
        if risk_metrics['total_exposure'] > self.risk_limits.max_portfolio_exposure:
            violations.append({
                'type': 'EXPOSURE_LIMIT',
                'severity': 'HIGH',
                'current': risk_metrics['total_exposure'],
                'limit': self.risk_limits.max_portfolio_exposure,
                'message': f"Portfolio exposure {risk_metrics['total_exposure']:.1%} exceeds limit {self.risk_limits.max_portfolio_exposure:.1%}"
            })
        
        # Check drawdown limit
        if abs(risk_metrics['max_drawdown']) > self.risk_limits.max_drawdown:
            violations.append({
                'type': 'DRAWDOWN_LIMIT',
                'severity': 'HIGH',
                'current': abs(risk_metrics['max_drawdown']),
                'limit': self.risk_limits.max_drawdown,
                'message': f"Max drawdown {abs(risk_metrics['max_drawdown']):.1%} exceeds limit {self.risk_limits.max_drawdown:.1%}"
            })
        
        # Check daily loss
        daily_pnl = await self._calculate_daily_pnl()
        if daily_pnl < -self.risk_limits.max_daily_loss:
            violations.append({
                'type': 'DAILY_LOSS_LIMIT',
                'severity': 'CRITICAL',
                'current': abs(daily_pnl),
                'limit': self.risk_limits.max_daily_loss,
                'message': f"Daily loss {abs(daily_pnl):.1%} exceeds limit {self.risk_limits.max_daily_loss:.1%}"
            })
        
        return violations
    
    async def _generate_risk_actions(self, violations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate risk management actions based on violations"""
        
        actions = []
        
        for violation in violations:
            if violation['type'] == 'EXPOSURE_LIMIT':
                actions.append({
                    'action': 'REDUCE_POSITIONS',
                    'priority': 'HIGH',
                    'target_reduction': violation['current'] - violation['limit'],
                    'message': 'Reduce position sizes to meet exposure limit'
                })
            
            elif violation['type'] == 'DRAWDOWN_LIMIT':
                actions.append({
                    'action': 'STOP_NEW_POSITIONS',
                    'priority': 'HIGH',
                    'message': 'Stop opening new positions until drawdown recovers'
                })
            
            elif violation['type'] == 'DAILY_LOSS_LIMIT':
                actions.append({
                    'action': 'EMERGENCY_STOP',
                    'priority': 'CRITICAL',
                    'message': 'Emergency stop - close all positions and halt trading'
                })
        
        return actions
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """Get comprehensive risk summary"""
        
        return {
            'risk_limits': {
                'max_position_size': self.risk_limits.max_position_size,
                'max_portfolio_exposure': self.risk_limits.max_portfolio_exposure,
                'max_daily_loss': self.risk_limits.max_daily_loss,
                'max_drawdown': self.risk_limits.max_drawdown
            },
            'current_metrics': {
                'active_positions': len(self.positions),
                'total_exposure': sum(pos.get('position_size', 0) for pos in self.positions),
                'daily_pnl': asyncio.run(self._calculate_daily_pnl()) if self.pnl_history else 0.0
            },
            'risk_events': len(self.risk_events),
            'last_update': datetime.now().isoformat()
        }

#!/usr/bin/env python3
"""
Straddle ML Engine - Core ML System for Straddle Strategies
===========================================================

Main ML engine that orchestrates all straddle-related ML operations:
- Position analysis and optimization
- Volatility prediction and modeling
- Risk management and position sizing
- Real-time signal generation
- Multi-model ensemble predictions
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import asyncio
from dataclasses import dataclass
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class StraddleType(Enum):
    """Straddle strategy types"""
    ATM = "ATM"
    ITM = "ITM" 
    OTM = "OTM"
    TRIPLE = "TRIPLE"

class PredictionType(Enum):
    """ML prediction types"""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"

@dataclass
class StraddleSignal:
    """ML-generated straddle signal"""
    straddle_type: StraddleType
    prediction: PredictionType
    confidence: float
    entry_price: Optional[float] = None
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None
    position_size: Optional[float] = None
    iv_prediction: Optional[float] = None
    regime_alignment: Optional[float] = None
    feature_importance: Optional[Dict[str, float]] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class MarketContext:
    """Current market context for ML predictions"""
    current_price: float
    current_iv: float
    current_volume: int
    regime_type: str
    regime_confidence: float
    time_to_expiry: int  # minutes
    vwap: float
    previous_day_vwap: float
    ema_3: float
    ema_5: float
    ema_10: float
    ema_15: float
    
class StraddleMLEngine:
    """
    Core ML Engine for Straddle Strategies
    
    Provides comprehensive ML capabilities for straddle trading:
    - Real-time signal generation
    - Multi-model ensemble predictions
    - Risk-adjusted position sizing
    - Volatility forecasting
    - Market regime integration
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the Straddle ML Engine"""
        self.config = config or self._get_default_config()
        self.models = {}
        self.feature_processors = {}
        self.is_initialized = False
        self.prediction_cache = {}
        self.performance_stats = {
            'total_predictions': 0,
            'successful_predictions': 0,
            'avg_confidence': 0.0,
            'avg_latency_ms': 0.0,
            'last_prediction_time': None
        }
        
        logger.info("🧠 Straddle ML Engine initialized")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'models': {
                'atm_model_path': 'models/atm_straddle_model.pkl',
                'itm_model_path': 'models/itm_straddle_model.pkl',
                'otm_model_path': 'models/otm_straddle_model.pkl',
                'triple_model_path': 'models/triple_straddle_model.pkl'
            },
            'features': {
                'technical_indicators': ['ema_3', 'ema_5', 'ema_10', 'ema_15', 'vwap', 'prev_day_vwap'],
                'volatility_indicators': ['iv_skew', 'iv_percentile', 'realized_vol'],
                'market_structure': ['smart_money_index', 'order_flow', 'institutional_activity'],
                'regime_indicators': ['regime_type', 'regime_confidence', 'regime_stability']
            },
            'prediction': {
                'confidence_threshold': 0.65,
                'ensemble_weights': {
                    'random_forest': 0.3,
                    'gradient_boosting': 0.25,
                    'logistic_regression': 0.25,
                    'neural_network': 0.2
                },
                'cache_duration_seconds': 30
            },
            'risk_management': {
                'max_position_size': 0.1,  # 10% of portfolio
                'min_confidence': 0.6,
                'max_iv_percentile': 80,
                'min_time_to_expiry': 30  # minutes
            }
        }
    
    async def initialize(self) -> bool:
        """Initialize all ML components"""
        try:
            logger.info("🚀 Initializing Straddle ML Engine components...")
            
            # Initialize feature processors
            await self._initialize_feature_processors()
            
            # Initialize ML models
            await self._initialize_models()
            
            # Initialize risk manager
            await self._initialize_risk_manager()
            
            self.is_initialized = True
            logger.info("✅ Straddle ML Engine initialization complete")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Straddle ML Engine: {e}")
            return False
    
    async def _initialize_feature_processors(self):
        """Initialize feature processing components"""
        from ..features.technical_features import TechnicalFeatures
        from ..features.volatility_features import VolatilityFeatures
        from ..features.market_structure_features import MarketStructureFeatures
        from ..features.regime_features import RegimeFeatures
        
        self.feature_processors = {
            'technical': TechnicalFeatures(),
            'volatility': VolatilityFeatures(),
            'market_structure': MarketStructureFeatures(),
            'regime': RegimeFeatures()
        }
        
        logger.info("✅ Feature processors initialized")
    
    async def _initialize_models(self):
        """Initialize ML models"""
        from ..models.atm_straddle_model import ATMStraddleModel
        from ..models.itm_straddle_model import ITMStraddleModel
        from ..models.otm_straddle_model import OTMStraddleModel
        from ..models.triple_straddle_model import TripleStraddleModel
        
        self.models = {
            StraddleType.ATM: ATMStraddleModel(),
            StraddleType.ITM: ITMStraddleModel(),
            StraddleType.OTM: OTMStraddleModel(),
            StraddleType.TRIPLE: TripleStraddleModel()
        }
        
        # Initialize each model
        for straddle_type, model in self.models.items():
            await model.initialize()
        
        logger.info("✅ ML models initialized")
    
    async def _initialize_risk_manager(self):
        """Initialize risk management component"""
        from ..core.risk_manager import RiskManager
        
        self.risk_manager = RiskManager()
        await self.risk_manager.initialize()
        
        logger.info("✅ Risk manager initialized")
    
    async def generate_straddle_signal(
        self, 
        straddle_type: StraddleType,
        market_context: MarketContext,
        historical_data: Optional[pd.DataFrame] = None
    ) -> StraddleSignal:
        """
        Generate ML-enhanced straddle signal
        
        Args:
            straddle_type: Type of straddle strategy
            market_context: Current market conditions
            historical_data: Historical price/volume data
            
        Returns:
            StraddleSignal with ML predictions and confidence
        """
        start_time = datetime.now()
        
        try:
            if not self.is_initialized:
                await self.initialize()
            
            # Check cache first
            cache_key = f"{straddle_type.value}_{market_context.current_price}_{market_context.regime_type}"
            if cache_key in self.prediction_cache:
                cached_signal, cache_time = self.prediction_cache[cache_key]
                if (datetime.now() - cache_time).seconds < self.config['prediction']['cache_duration_seconds']:
                    logger.debug(f"🔄 Using cached prediction for {straddle_type.value}")
                    return cached_signal
            
            # Generate features
            features = await self._generate_features(market_context, historical_data)
            
            # Get model prediction
            model = self.models[straddle_type]
            prediction_result = await model.predict(features)
            
            # Apply risk management
            risk_adjusted_signal = await self.risk_manager.adjust_signal(
                prediction_result, market_context
            )
            
            # Create final signal
            prediction_str = risk_adjusted_signal['prediction']
            if isinstance(prediction_str, str):
                prediction_enum = PredictionType(prediction_str)
            else:
                prediction_enum = prediction_str

            signal = StraddleSignal(
                straddle_type=straddle_type,
                prediction=prediction_enum,
                confidence=risk_adjusted_signal['confidence'],
                entry_price=risk_adjusted_signal.get('entry_price'),
                target_price=risk_adjusted_signal.get('target_price'),
                stop_loss=risk_adjusted_signal.get('stop_loss'),
                position_size=risk_adjusted_signal.get('position_size'),
                iv_prediction=risk_adjusted_signal.get('iv_prediction'),
                regime_alignment=risk_adjusted_signal.get('regime_alignment'),
                feature_importance=risk_adjusted_signal.get('feature_importance')
            )
            
            # Cache the signal
            self.prediction_cache[cache_key] = (signal, datetime.now())
            
            # Update performance stats
            self._update_performance_stats(start_time, signal.confidence)
            
            logger.info(f"🎯 Generated {straddle_type.value} signal: {signal.prediction.value} "
                       f"(confidence: {signal.confidence:.3f})")
            
            return signal
            
        except Exception as e:
            logger.error(f"❌ Failed to generate straddle signal: {e}")
            # Return default signal
            return StraddleSignal(
                straddle_type=straddle_type,
                prediction=PredictionType.HOLD,
                confidence=0.0
            )
    
    async def _generate_features(
        self, 
        market_context: MarketContext, 
        historical_data: Optional[pd.DataFrame]
    ) -> Dict[str, float]:
        """Generate comprehensive feature set for ML prediction"""
        features = {}
        
        # Technical features
        technical_features = await self.feature_processors['technical'].generate_features(
            market_context, historical_data
        )
        features.update(technical_features)
        
        # Volatility features
        volatility_features = await self.feature_processors['volatility'].generate_features(
            market_context, historical_data
        )
        features.update(volatility_features)
        
        # Market structure features
        market_structure_features = await self.feature_processors['market_structure'].generate_features(
            market_context, historical_data
        )
        features.update(market_structure_features)
        
        # Regime features
        regime_features = await self.feature_processors['regime'].generate_features(
            market_context, historical_data
        )
        features.update(regime_features)
        
        return features
    
    def _update_performance_stats(self, start_time: datetime, confidence: float):
        """Update performance statistics"""
        latency_ms = (datetime.now() - start_time).total_seconds() * 1000
        
        self.performance_stats['total_predictions'] += 1
        self.performance_stats['avg_latency_ms'] = (
            (self.performance_stats['avg_latency_ms'] * (self.performance_stats['total_predictions'] - 1) + latency_ms) /
            self.performance_stats['total_predictions']
        )
        self.performance_stats['avg_confidence'] = (
            (self.performance_stats['avg_confidence'] * (self.performance_stats['total_predictions'] - 1) + confidence) /
            self.performance_stats['total_predictions']
        )
        self.performance_stats['last_prediction_time'] = datetime.now()
    
    async def get_ensemble_prediction(
        self,
        market_context: MarketContext,
        historical_data: Optional[pd.DataFrame] = None
    ) -> Dict[StraddleType, StraddleSignal]:
        """
        Get ensemble predictions for all straddle types
        
        Returns:
            Dictionary mapping straddle types to their signals
        """
        signals = {}
        
        # Generate signals for all straddle types
        tasks = []
        for straddle_type in StraddleType:
            task = self.generate_straddle_signal(straddle_type, market_context, historical_data)
            tasks.append((straddle_type, task))
        
        # Execute all predictions concurrently
        results = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)
        
        # Collect results
        for (straddle_type, _), result in zip(tasks, results):
            if isinstance(result, Exception):
                logger.error(f"❌ Failed to get {straddle_type.value} prediction: {result}")
                signals[straddle_type] = StraddleSignal(
                    straddle_type=straddle_type,
                    prediction=PredictionType.HOLD,
                    confidence=0.0
                )
            else:
                signals[straddle_type] = result
        
        return signals
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics"""
        return self.performance_stats.copy()
    
    def clear_cache(self):
        """Clear prediction cache"""
        self.prediction_cache.clear()
        logger.info("🧹 Prediction cache cleared")
    
    async def shutdown(self):
        """Shutdown the ML engine"""
        logger.info("🛑 Shutting down Straddle ML Engine...")
        
        # Shutdown models
        for model in self.models.values():
            if hasattr(model, 'shutdown'):
                await model.shutdown()
        
        # Clear cache
        self.clear_cache()
        
        self.is_initialized = False
        logger.info("✅ Straddle ML Engine shutdown complete")

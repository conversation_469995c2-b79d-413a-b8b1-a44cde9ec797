#!/usr/bin/env python3
"""
Position Analyzer for ML Straddle System
========================================

Analyzes straddle positions for optimal entry/exit points:
- ATM/ITM/OTM position analysis
- Greeks calculation and monitoring
- Risk assessment and position sizing
- Performance tracking and optimization
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class PositionMetrics:
    """Position analysis metrics"""
    delta: float
    gamma: float
    theta: float
    vega: float
    implied_volatility: float
    time_value: float
    intrinsic_value: float
    break_even_upper: float
    break_even_lower: float
    max_profit: float
    max_loss: float
    probability_profit: float

class PositionAnalyzer:
    """
    Position Analyzer for Straddle Strategies
    
    Provides comprehensive position analysis including:
    - Greeks calculation
    - Risk metrics
    - Profit/loss scenarios
    - Optimal sizing recommendations
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize Position Analyzer"""
        self.config = config or self._get_default_config()
        logger.info("📊 Position Analyzer initialized")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'risk_free_rate': 0.05,
            'dividend_yield': 0.0,
            'calculation_precision': 4,
            'monte_carlo_simulations': 10000,
            'confidence_levels': [0.68, 0.95, 0.99]
        }
    
    async def analyze_position(
        self,
        position_type: str,
        strike_price: float,
        current_price: float,
        time_to_expiry: float,
        implied_volatility: float,
        position_size: float = 1.0
    ) -> PositionMetrics:
        """
        Analyze straddle position
        
        Args:
            position_type: 'ATM', 'ITM', or 'OTM'
            strike_price: Strike price of the straddle
            current_price: Current underlying price
            time_to_expiry: Time to expiry in years
            implied_volatility: Implied volatility
            position_size: Position size multiplier
            
        Returns:
            PositionMetrics with comprehensive analysis
        """
        try:
            # Calculate basic metrics
            call_metrics = self._calculate_option_metrics(
                'call', strike_price, current_price, time_to_expiry, implied_volatility
            )
            
            put_metrics = self._calculate_option_metrics(
                'put', strike_price, current_price, time_to_expiry, implied_volatility
            )
            
            # Combine for straddle
            straddle_metrics = self._combine_straddle_metrics(call_metrics, put_metrics)
            
            # Calculate position-specific adjustments
            adjusted_metrics = self._adjust_for_position_size(straddle_metrics, position_size)
            
            logger.debug(f"📊 Analyzed {position_type} position: Delta={adjusted_metrics.delta:.4f}")
            
            return adjusted_metrics
            
        except Exception as e:
            logger.error(f"❌ Position analysis failed: {e}")
            return self._get_default_metrics()
    
    def _calculate_option_metrics(
        self,
        option_type: str,
        strike: float,
        spot: float,
        time_to_expiry: float,
        volatility: float
    ) -> Dict[str, float]:
        """Calculate option metrics using simplified Black-Scholes"""
        
        # Simplified Black-Scholes implementation
        r = self.config['risk_free_rate']
        q = self.config['dividend_yield']
        
        # Handle edge cases
        if time_to_expiry <= 0:
            time_to_expiry = 1/365  # 1 day minimum
        
        if volatility <= 0:
            volatility = 0.01  # 1% minimum
        
        # Calculate d1 and d2
        d1 = (np.log(spot / strike) + (r - q + 0.5 * volatility**2) * time_to_expiry) / (volatility * np.sqrt(time_to_expiry))
        d2 = d1 - volatility * np.sqrt(time_to_expiry)
        
        # Standard normal CDF approximation
        def norm_cdf(x):
            return 0.5 * (1 + np.sign(x) * np.sqrt(1 - np.exp(-2 * x**2 / np.pi)))
        
        # Calculate option price and Greeks
        if option_type == 'call':
            price = spot * np.exp(-q * time_to_expiry) * norm_cdf(d1) - strike * np.exp(-r * time_to_expiry) * norm_cdf(d2)
            delta = np.exp(-q * time_to_expiry) * norm_cdf(d1)
            intrinsic = max(0, spot - strike)
        else:  # put
            price = strike * np.exp(-r * time_to_expiry) * norm_cdf(-d2) - spot * np.exp(-q * time_to_expiry) * norm_cdf(-d1)
            delta = -np.exp(-q * time_to_expiry) * norm_cdf(-d1)
            intrinsic = max(0, strike - spot)
        
        # Calculate other Greeks (simplified)
        gamma = np.exp(-q * time_to_expiry) * self._norm_pdf(d1) / (spot * volatility * np.sqrt(time_to_expiry))
        theta = (-spot * self._norm_pdf(d1) * volatility * np.exp(-q * time_to_expiry) / (2 * np.sqrt(time_to_expiry)) 
                - r * strike * np.exp(-r * time_to_expiry) * norm_cdf(d2 if option_type == 'call' else -d2))
        vega = spot * np.sqrt(time_to_expiry) * self._norm_pdf(d1) * np.exp(-q * time_to_expiry)
        
        return {
            'price': price,
            'delta': delta,
            'gamma': gamma,
            'theta': theta / 365,  # Per day
            'vega': vega / 100,    # Per 1% volatility change
            'intrinsic_value': intrinsic,
            'time_value': price - intrinsic
        }
    
    def _norm_pdf(self, x):
        """Standard normal probability density function"""
        return np.exp(-0.5 * x**2) / np.sqrt(2 * np.pi)
    
    def _combine_straddle_metrics(self, call_metrics: Dict, put_metrics: Dict) -> PositionMetrics:
        """Combine call and put metrics for straddle"""
        
        total_price = call_metrics['price'] + put_metrics['price']
        
        return PositionMetrics(
            delta=call_metrics['delta'] + put_metrics['delta'],
            gamma=call_metrics['gamma'] + put_metrics['gamma'],
            theta=call_metrics['theta'] + put_metrics['theta'],
            vega=call_metrics['vega'] + put_metrics['vega'],
            implied_volatility=0.25,  # Placeholder
            time_value=call_metrics['time_value'] + put_metrics['time_value'],
            intrinsic_value=call_metrics['intrinsic_value'] + put_metrics['intrinsic_value'],
            break_even_upper=call_metrics['price'] + put_metrics['price'] + call_metrics.get('strike', 100),
            break_even_lower=call_metrics.get('strike', 100) - (call_metrics['price'] + put_metrics['price']),
            max_profit=float('inf'),  # Unlimited for long straddle
            max_loss=total_price,     # Premium paid
            probability_profit=0.5    # Simplified
        )
    
    def _adjust_for_position_size(self, metrics: PositionMetrics, position_size: float) -> PositionMetrics:
        """Adjust metrics for position size"""
        
        return PositionMetrics(
            delta=metrics.delta * position_size,
            gamma=metrics.gamma * position_size,
            theta=metrics.theta * position_size,
            vega=metrics.vega * position_size,
            implied_volatility=metrics.implied_volatility,
            time_value=metrics.time_value * position_size,
            intrinsic_value=metrics.intrinsic_value * position_size,
            break_even_upper=metrics.break_even_upper,  # Breakevens don't scale
            break_even_lower=metrics.break_even_lower,
            max_profit=metrics.max_profit,
            max_loss=metrics.max_loss * position_size,
            probability_profit=metrics.probability_profit
        )
    
    def _get_default_metrics(self) -> PositionMetrics:
        """Get default metrics when calculation fails"""
        return PositionMetrics(
            delta=0.0,
            gamma=0.0,
            theta=0.0,
            vega=0.0,
            implied_volatility=0.2,
            time_value=0.0,
            intrinsic_value=0.0,
            break_even_upper=100.0,
            break_even_lower=100.0,
            max_profit=0.0,
            max_loss=0.0,
            probability_profit=0.5
        )
    
    async def calculate_optimal_sizing(
        self,
        portfolio_value: float,
        risk_tolerance: float,
        position_metrics: PositionMetrics,
        market_volatility: float
    ) -> Dict[str, float]:
        """Calculate optimal position sizing"""
        
        try:
            # Kelly Criterion-based sizing (simplified)
            win_probability = position_metrics.probability_profit
            avg_win = portfolio_value * 0.1  # Assume 10% average win
            avg_loss = position_metrics.max_loss
            
            if avg_loss > 0:
                kelly_fraction = (win_probability * avg_win - (1 - win_probability) * avg_loss) / avg_win
            else:
                kelly_fraction = 0.0
            
            # Apply risk tolerance
            recommended_size = max(0, min(kelly_fraction * risk_tolerance, 0.1))  # Max 10%
            
            # Volatility adjustment
            vol_adjustment = max(0.5, 1 - (market_volatility - 0.2) * 2)
            adjusted_size = recommended_size * vol_adjustment
            
            return {
                'recommended_size': adjusted_size,
                'kelly_fraction': kelly_fraction,
                'volatility_adjustment': vol_adjustment,
                'max_position_value': portfolio_value * adjusted_size,
                'risk_amount': position_metrics.max_loss * adjusted_size
            }
            
        except Exception as e:
            logger.error(f"❌ Optimal sizing calculation failed: {e}")
            return {
                'recommended_size': 0.02,  # Default 2%
                'kelly_fraction': 0.0,
                'volatility_adjustment': 1.0,
                'max_position_value': portfolio_value * 0.02,
                'risk_amount': portfolio_value * 0.01
            }
    
    async def simulate_pnl_scenarios(
        self,
        position_metrics: PositionMetrics,
        current_price: float,
        time_horizon_days: int = 30,
        price_scenarios: Optional[List[float]] = None
    ) -> Dict[str, Any]:
        """Simulate P&L scenarios for the position"""
        
        try:
            if price_scenarios is None:
                # Generate price scenarios
                price_scenarios = self._generate_price_scenarios(current_price, time_horizon_days)
            
            pnl_scenarios = []
            
            for scenario_price in price_scenarios:
                # Simplified P&L calculation
                call_pnl = max(0, scenario_price - current_price) - position_metrics.max_loss / 2
                put_pnl = max(0, current_price - scenario_price) - position_metrics.max_loss / 2
                total_pnl = call_pnl + put_pnl
                
                pnl_scenarios.append({
                    'price': scenario_price,
                    'pnl': total_pnl,
                    'return_pct': total_pnl / position_metrics.max_loss if position_metrics.max_loss > 0 else 0
                })
            
            # Calculate statistics
            pnl_values = [scenario['pnl'] for scenario in pnl_scenarios]
            
            return {
                'scenarios': pnl_scenarios,
                'expected_pnl': np.mean(pnl_values),
                'pnl_std': np.std(pnl_values),
                'max_pnl': max(pnl_values),
                'min_pnl': min(pnl_values),
                'profit_probability': len([p for p in pnl_values if p > 0]) / len(pnl_values),
                'percentiles': {
                    '5th': np.percentile(pnl_values, 5),
                    '25th': np.percentile(pnl_values, 25),
                    '50th': np.percentile(pnl_values, 50),
                    '75th': np.percentile(pnl_values, 75),
                    '95th': np.percentile(pnl_values, 95)
                }
            }
            
        except Exception as e:
            logger.error(f"❌ P&L simulation failed: {e}")
            return {
                'scenarios': [],
                'expected_pnl': 0.0,
                'pnl_std': 0.0,
                'max_pnl': 0.0,
                'min_pnl': 0.0,
                'profit_probability': 0.5,
                'percentiles': {}
            }
    
    def _generate_price_scenarios(self, current_price: float, days: int, num_scenarios: int = 1000) -> List[float]:
        """Generate price scenarios using Monte Carlo"""
        
        # Simplified geometric Brownian motion
        volatility = 0.25  # 25% annual volatility
        drift = 0.05       # 5% annual drift
        dt = days / 365
        
        scenarios = []
        for _ in range(num_scenarios):
            random_shock = np.random.normal(0, 1)
            scenario_price = current_price * np.exp((drift - 0.5 * volatility**2) * dt + volatility * np.sqrt(dt) * random_shock)
            scenarios.append(scenario_price)
        
        return scenarios
    
    async def get_position_summary(self, position_metrics: PositionMetrics) -> Dict[str, Any]:
        """Get comprehensive position summary"""
        
        return {
            'greeks': {
                'delta': round(position_metrics.delta, 4),
                'gamma': round(position_metrics.gamma, 4),
                'theta': round(position_metrics.theta, 4),
                'vega': round(position_metrics.vega, 4)
            },
            'risk_metrics': {
                'max_loss': round(position_metrics.max_loss, 2),
                'break_even_upper': round(position_metrics.break_even_upper, 2),
                'break_even_lower': round(position_metrics.break_even_lower, 2),
                'probability_profit': round(position_metrics.probability_profit, 3)
            },
            'value_components': {
                'intrinsic_value': round(position_metrics.intrinsic_value, 2),
                'time_value': round(position_metrics.time_value, 2),
                'total_value': round(position_metrics.intrinsic_value + position_metrics.time_value, 2)
            },
            'market_exposure': {
                'delta_exposure': abs(position_metrics.delta),
                'gamma_risk': position_metrics.gamma,
                'theta_decay': position_metrics.theta,
                'vega_sensitivity': position_metrics.vega
            }
        }

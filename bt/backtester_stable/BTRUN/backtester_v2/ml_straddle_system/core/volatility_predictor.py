#!/usr/bin/env python3
"""
Volatility Predictor for ML Straddle System
===========================================

Advanced volatility prediction and modeling:
- Implied volatility forecasting
- Realized volatility calculation
- Volatility surface modeling
- IV skew analysis
- Volatility regime detection
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler

logger = logging.getLogger(__name__)

class VolatilityPredictor:
    """
    Advanced Volatility Predictor for Straddle Strategies
    
    Provides comprehensive volatility analysis including:
    - IV forecasting
    - Volatility surface modeling
    - Skew analysis
    - Regime detection
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize Volatility Predictor"""
        self.config = config or self._get_default_config()
        self.models = {}
        self.scalers = {}
        self.is_trained = False
        
        logger.info("📈 Volatility Predictor initialized")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'lookback_periods': [5, 10, 20, 30],
            'prediction_horizons': [1, 5, 10, 30],  # days
            'volatility_models': ['garch', 'ewma', 'ml'],
            'skew_calculation_method': 'polynomial',
            'surface_interpolation': 'cubic',
            'regime_thresholds': {
                'low_vol': 0.15,
                'normal_vol': 0.25,
                'high_vol': 0.35
            }
        }
    
    async def initialize(self):
        """Initialize volatility models"""
        try:
            logger.info("🚀 Initializing Volatility Predictor...")
            
            # Initialize ML models
            self._initialize_ml_models()
            
            # Initialize scalers
            self._initialize_scalers()
            
            # Load or create training data
            await self._prepare_training_data()
            
            self.is_trained = True
            logger.info("✅ Volatility Predictor initialization complete")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Volatility Predictor: {e}")
            raise
    
    def _initialize_ml_models(self):
        """Initialize ML models for volatility prediction"""
        self.models = {
            'iv_predictor': RandomForestRegressor(n_estimators=100, random_state=42),
            'rv_predictor': RandomForestRegressor(n_estimators=100, random_state=42),
            'skew_predictor': RandomForestRegressor(n_estimators=50, random_state=42)
        }
    
    def _initialize_scalers(self):
        """Initialize feature scalers"""
        self.scalers = {
            'iv_features': StandardScaler(),
            'rv_features': StandardScaler(),
            'skew_features': StandardScaler()
        }
    
    async def _prepare_training_data(self):
        """Prepare training data for volatility models"""
        # Generate synthetic training data for demonstration
        n_samples = 1000
        np.random.seed(42)
        
        # Generate features
        features = {
            'price_returns': np.random.normal(0, 0.02, n_samples),
            'volume_ratio': np.random.uniform(0.5, 2.0, n_samples),
            'iv_rank': np.random.uniform(0, 100, n_samples),
            'term_structure': np.random.normal(0, 0.05, n_samples),
            'skew_level': np.random.normal(0, 0.1, n_samples)
        }
        
        # Generate targets
        iv_target = 0.2 + 0.1 * np.random.normal(0, 1, n_samples)
        rv_target = 0.18 + 0.08 * np.random.normal(0, 1, n_samples)
        skew_target = 0.05 + 0.03 * np.random.normal(0, 1, n_samples)
        
        # Prepare feature matrix
        X = np.column_stack([features[key] for key in features.keys()])
        
        # Train models
        X_scaled = self.scalers['iv_features'].fit_transform(X)
        self.models['iv_predictor'].fit(X_scaled, iv_target)
        
        X_scaled = self.scalers['rv_features'].fit_transform(X)
        self.models['rv_predictor'].fit(X_scaled, rv_target)
        
        X_scaled = self.scalers['skew_features'].fit_transform(X)
        self.models['skew_predictor'].fit(X_scaled, skew_target)
        
        logger.info("✅ Volatility models trained with synthetic data")
    
    async def predict_implied_volatility(
        self,
        current_iv: float,
        market_data: Dict[str, Any],
        horizon_days: int = 5
    ) -> Dict[str, float]:
        """
        Predict implied volatility for given horizon
        
        Args:
            current_iv: Current implied volatility
            market_data: Market context data
            horizon_days: Prediction horizon in days
            
        Returns:
            Dictionary with volatility predictions
        """
        try:
            if not self.is_trained:
                await self.initialize()
            
            # Extract features from market data
            features = self._extract_volatility_features(current_iv, market_data)
            
            # Prepare feature vector
            feature_vector = np.array([
                features.get('price_returns', 0.0),
                features.get('volume_ratio', 1.0),
                features.get('iv_rank', 50.0),
                features.get('term_structure', 0.0),
                features.get('skew_level', 0.0)
            ]).reshape(1, -1)
            
            # Scale features
            X_scaled = self.scalers['iv_features'].transform(feature_vector)
            
            # Predict
            predicted_iv = self.models['iv_predictor'].predict(X_scaled)[0]
            
            # Calculate confidence and adjustments
            confidence = self._calculate_prediction_confidence(features, predicted_iv, current_iv)
            
            # Apply regime adjustments
            regime_adjusted_iv = self._apply_regime_adjustments(predicted_iv, market_data)
            
            return {
                'predicted_iv': max(0.05, min(1.0, predicted_iv)),
                'regime_adjusted_iv': max(0.05, min(1.0, regime_adjusted_iv)),
                'current_iv': current_iv,
                'iv_change': predicted_iv - current_iv,
                'iv_change_pct': (predicted_iv - current_iv) / current_iv if current_iv > 0 else 0,
                'confidence': confidence,
                'horizon_days': horizon_days,
                'prediction_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ IV prediction failed: {e}")
            return self._get_default_iv_prediction(current_iv)
    
    def _extract_volatility_features(self, current_iv: float, market_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract features for volatility prediction"""
        
        features = {}
        
        # Price-based features
        current_price = market_data.get('current_price', 100.0)
        previous_price = market_data.get('previous_price', current_price)
        features['price_returns'] = (current_price - previous_price) / previous_price if previous_price > 0 else 0.0
        
        # Volume features
        current_volume = market_data.get('current_volume', 1000)
        avg_volume = market_data.get('avg_volume', current_volume)
        features['volume_ratio'] = current_volume / avg_volume if avg_volume > 0 else 1.0
        
        # IV features
        features['iv_rank'] = self._calculate_iv_rank(current_iv, market_data)
        features['term_structure'] = self._calculate_term_structure(market_data)
        features['skew_level'] = self._calculate_skew_level(market_data)
        
        return features
    
    def _calculate_iv_rank(self, current_iv: float, market_data: Dict[str, Any]) -> float:
        """Calculate IV rank (simplified)"""
        # In production, this would use historical IV data
        # For now, use a simplified calculation
        
        # Assume typical IV range of 0.1 to 0.5
        min_iv = 0.1
        max_iv = 0.5
        
        iv_rank = ((current_iv - min_iv) / (max_iv - min_iv)) * 100
        return max(0, min(100, iv_rank))
    
    def _calculate_term_structure(self, market_data: Dict[str, Any]) -> float:
        """Calculate term structure slope"""
        # Simplified term structure calculation
        # In production, this would use multiple expiration IVs
        
        short_term_iv = market_data.get('short_term_iv', 0.2)
        long_term_iv = market_data.get('long_term_iv', 0.22)
        
        return long_term_iv - short_term_iv
    
    def _calculate_skew_level(self, market_data: Dict[str, Any]) -> float:
        """Calculate volatility skew level"""
        # Simplified skew calculation
        # In production, this would use put/call IV differences
        
        atm_iv = market_data.get('atm_iv', 0.2)
        otm_put_iv = market_data.get('otm_put_iv', 0.22)
        
        return otm_put_iv - atm_iv
    
    def _calculate_prediction_confidence(self, features: Dict[str, float], predicted_iv: float, current_iv: float) -> float:
        """Calculate confidence in the prediction"""
        
        # Base confidence
        confidence = 0.7
        
        # Adjust based on feature quality
        if features.get('volume_ratio', 1.0) > 1.5:
            confidence += 0.1  # High volume increases confidence
        
        if abs(features.get('price_returns', 0.0)) > 0.02:
            confidence -= 0.1  # High price moves decrease confidence
        
        # Adjust based on prediction magnitude
        iv_change_magnitude = abs(predicted_iv - current_iv) / current_iv if current_iv > 0 else 0
        if iv_change_magnitude > 0.2:
            confidence -= 0.2  # Large changes are less confident
        
        return max(0.1, min(0.95, confidence))
    
    def _apply_regime_adjustments(self, predicted_iv: float, market_data: Dict[str, Any]) -> float:
        """Apply market regime adjustments to IV prediction"""
        
        regime_type = market_data.get('regime_type', 'NORMAL_VOLATILE')
        regime_confidence = market_data.get('regime_confidence', 0.7)
        
        adjustment_factor = 1.0
        
        if 'HIGH_VOLATILE' in regime_type:
            adjustment_factor = 1.1 + (regime_confidence - 0.5) * 0.2
        elif 'LOW_VOLATILE' in regime_type:
            adjustment_factor = 0.9 - (regime_confidence - 0.5) * 0.2
        
        return predicted_iv * adjustment_factor
    
    async def calculate_realized_volatility(
        self,
        price_data: pd.DataFrame,
        window: int = 20
    ) -> Dict[str, float]:
        """Calculate realized volatility from price data"""
        
        try:
            if len(price_data) < window:
                logger.warning(f"⚠️ Insufficient data for RV calculation: {len(price_data)} < {window}")
                return {'realized_vol': 0.2, 'annualized_vol': 0.2}
            
            # Calculate returns
            if 'close' in price_data.columns:
                returns = price_data['close'].pct_change().dropna()
            else:
                logger.warning("⚠️ No 'close' column found, using first numeric column")
                returns = price_data.iloc[:, 0].pct_change().dropna()
            
            # Calculate realized volatility
            rv = returns.rolling(window=window).std().iloc[-1]
            
            # Annualize (assuming daily data)
            annualized_rv = rv * np.sqrt(252)
            
            return {
                'realized_vol': float(rv) if not np.isnan(rv) else 0.2,
                'annualized_vol': float(annualized_rv) if not np.isnan(annualized_rv) else 0.2,
                'window': window,
                'data_points': len(returns)
            }
            
        except Exception as e:
            logger.error(f"❌ Realized volatility calculation failed: {e}")
            return {'realized_vol': 0.2, 'annualized_vol': 0.2}
    
    async def analyze_volatility_surface(
        self,
        iv_data: Dict[str, Dict[str, float]]
    ) -> Dict[str, Any]:
        """Analyze volatility surface characteristics"""
        
        try:
            # Extract strikes and expirations
            strikes = []
            expirations = []
            ivs = []
            
            for expiry, strike_data in iv_data.items():
                for strike, iv in strike_data.items():
                    strikes.append(float(strike))
                    expirations.append(expiry)
                    ivs.append(iv)
            
            if len(ivs) == 0:
                return self._get_default_surface_analysis()
            
            # Calculate surface metrics
            avg_iv = np.mean(ivs)
            iv_std = np.std(ivs)
            min_iv = np.min(ivs)
            max_iv = np.max(ivs)
            
            # Calculate skew (simplified)
            skew = self._calculate_surface_skew(strikes, ivs)
            
            # Calculate term structure
            term_structure = self._calculate_surface_term_structure(expirations, ivs)
            
            return {
                'surface_metrics': {
                    'avg_iv': avg_iv,
                    'iv_std': iv_std,
                    'min_iv': min_iv,
                    'max_iv': max_iv,
                    'iv_range': max_iv - min_iv
                },
                'skew_analysis': skew,
                'term_structure': term_structure,
                'data_points': len(ivs),
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Volatility surface analysis failed: {e}")
            return self._get_default_surface_analysis()
    
    def _calculate_surface_skew(self, strikes: List[float], ivs: List[float]) -> Dict[str, float]:
        """Calculate volatility skew metrics"""
        
        if len(strikes) < 3:
            return {'skew': 0.0, 'convexity': 0.0}
        
        # Sort by strike
        sorted_data = sorted(zip(strikes, ivs))
        sorted_strikes, sorted_ivs = zip(*sorted_data)
        
        # Calculate skew as slope
        skew = (sorted_ivs[-1] - sorted_ivs[0]) / (sorted_strikes[-1] - sorted_strikes[0])
        
        # Calculate convexity (simplified)
        if len(sorted_ivs) >= 3:
            mid_idx = len(sorted_ivs) // 2
            convexity = sorted_ivs[mid_idx] - (sorted_ivs[0] + sorted_ivs[-1]) / 2
        else:
            convexity = 0.0
        
        return {
            'skew': skew,
            'convexity': convexity,
            'skew_direction': 'negative' if skew < 0 else 'positive'
        }
    
    def _calculate_surface_term_structure(self, expirations: List[str], ivs: List[float]) -> Dict[str, float]:
        """Calculate term structure characteristics"""
        
        # Group by expiration
        expiry_groups = {}
        for exp, iv in zip(expirations, ivs):
            if exp not in expiry_groups:
                expiry_groups[exp] = []
            expiry_groups[exp].append(iv)
        
        # Calculate average IV per expiration
        expiry_avgs = {exp: np.mean(iv_list) for exp, iv_list in expiry_groups.items()}
        
        if len(expiry_avgs) < 2:
            return {'slope': 0.0, 'curvature': 0.0}
        
        # Sort by expiration (simplified - assumes sortable format)
        sorted_expiries = sorted(expiry_avgs.items())
        
        # Calculate slope
        if len(sorted_expiries) >= 2:
            slope = sorted_expiries[-1][1] - sorted_expiries[0][1]
        else:
            slope = 0.0
        
        return {
            'slope': slope,
            'curvature': 0.0,  # Simplified
            'term_structure_shape': 'upward' if slope > 0 else 'downward'
        }
    
    def _get_default_iv_prediction(self, current_iv: float) -> Dict[str, float]:
        """Get default IV prediction when calculation fails"""
        return {
            'predicted_iv': current_iv,
            'regime_adjusted_iv': current_iv,
            'current_iv': current_iv,
            'iv_change': 0.0,
            'iv_change_pct': 0.0,
            'confidence': 0.5,
            'horizon_days': 5,
            'prediction_timestamp': datetime.now().isoformat()
        }
    
    def _get_default_surface_analysis(self) -> Dict[str, Any]:
        """Get default surface analysis when calculation fails"""
        return {
            'surface_metrics': {
                'avg_iv': 0.2,
                'iv_std': 0.05,
                'min_iv': 0.15,
                'max_iv': 0.25,
                'iv_range': 0.1
            },
            'skew_analysis': {
                'skew': 0.0,
                'convexity': 0.0,
                'skew_direction': 'neutral'
            },
            'term_structure': {
                'slope': 0.0,
                'curvature': 0.0,
                'term_structure_shape': 'flat'
            },
            'data_points': 0,
            'analysis_timestamp': datetime.now().isoformat()
        }
    
    async def detect_volatility_regime(self, current_iv: float, historical_data: Optional[pd.DataFrame] = None) -> Dict[str, Any]:
        """Detect current volatility regime"""
        
        try:
            thresholds = self.config['regime_thresholds']
            
            # Determine regime based on current IV
            if current_iv < thresholds['low_vol']:
                regime = 'LOW_VOLATILE'
                regime_strength = (thresholds['low_vol'] - current_iv) / thresholds['low_vol']
            elif current_iv > thresholds['high_vol']:
                regime = 'HIGH_VOLATILE'
                regime_strength = (current_iv - thresholds['high_vol']) / thresholds['high_vol']
            else:
                regime = 'NORMAL_VOLATILE'
                regime_strength = 1.0 - abs(current_iv - thresholds['normal_vol']) / (thresholds['high_vol'] - thresholds['low_vol'])
            
            # Calculate regime persistence (simplified)
            persistence = 0.7 + np.random.random() * 0.2  # Mock persistence
            
            return {
                'regime': regime,
                'regime_strength': min(1.0, max(0.0, regime_strength)),
                'persistence': persistence,
                'current_iv': current_iv,
                'thresholds': thresholds,
                'detection_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Volatility regime detection failed: {e}")
            return {
                'regime': 'NORMAL_VOLATILE',
                'regime_strength': 0.5,
                'persistence': 0.7,
                'current_iv': current_iv,
                'thresholds': self.config['regime_thresholds'],
                'detection_timestamp': datetime.now().isoformat()
            }

#!/usr/bin/env python3
"""
ML OTM Straddle Strategy
========================

ML-enhanced Out-of-The-Money straddle strategy with volatility expansion focus.
"""

import logging
from .ml_atm_straddle import MLATMStraddle

logger = logging.getLogger(__name__)

class MLOTMStraddle(MLATMStraddle):
    """OTM Straddle Strategy - inherits from ATM with OTM-specific adjustments"""
    
    def __init__(self, config=None):
        """Initialize OTM Straddle Strategy"""
        super().__init__(config)
        logger.info("🎯 ML OTM Straddle Strategy initialized")

#!/usr/bin/env python3
"""
ML Triple Straddle Strategy
===========================

ML-enhanced Triple straddle strategy combining ATM, ITM, and OTM positions.
"""

import logging
from .ml_atm_straddle import MLATMStraddle

logger = logging.getLogger(__name__)

class MLTripleStraddle(MLATMStraddle):
    """Triple Straddle Strategy - inherits from ATM with multi-leg optimization"""
    
    def __init__(self, config=None):
        """Initialize Triple Straddle Strategy"""
        super().__init__(config)
        logger.info("🎯 ML Triple Straddle Strategy initialized")

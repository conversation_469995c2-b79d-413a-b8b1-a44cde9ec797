#!/usr/bin/env python3
"""
ML ITM Straddle Strategy
========================

ML-enhanced In-The-Money straddle strategy with directional bias.
"""

import logging
from .ml_atm_straddle import MLATMStraddle

logger = logging.getLogger(__name__)

class MLITMStraddle(MLATMStraddle):
    """ITM Straddle Strategy - inherits from ATM with ITM-specific adjustments"""
    
    def __init__(self, config=None):
        """Initialize ITM Straddle Strategy"""
        super().__init__(config)
        logger.info("🎯 ML ITM Straddle Strategy initialized")

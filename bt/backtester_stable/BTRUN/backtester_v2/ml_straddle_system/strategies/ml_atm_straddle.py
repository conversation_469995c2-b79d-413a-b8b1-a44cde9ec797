#!/usr/bin/env python3
"""
ML ATM Straddle Strategy
========================

ML-enhanced At-The-Money straddle strategy with:
- Real-time volatility prediction
- Market regime awareness
- Smart position sizing
- Dynamic entry/exit timing
- Risk management integration
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import asyncio

from ..core.straddle_ml_engine import StraddleMLEngine, StraddleType, MarketContext, StraddleSignal

logger = logging.getLogger(__name__)

@dataclass
class StraddlePosition:
    """Represents an active straddle position"""
    entry_time: datetime
    entry_price: float
    call_strike: float
    put_strike: float
    call_premium: float
    put_premium: float
    position_size: float
    target_profit: Optional[float] = None
    stop_loss: Optional[float] = None
    current_pnl: float = 0.0
    is_active: bool = True
    exit_reason: Optional[str] = None

class MLATMStraddle:
    """
    ML-Enhanced ATM Straddle Strategy
    
    Features:
    - ML-driven entry/exit signals
    - Volatility expansion/contraction prediction
    - Market regime adaptation
    - Dynamic position sizing
    - Risk management integration
    - Real-time performance monitoring
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize ML ATM Straddle Strategy"""
        self.config = config or self._get_default_config()
        self.ml_engine = StraddleMLEngine(self.config.get('ml_engine', {}))
        self.active_positions = []
        self.closed_positions = []
        self.performance_stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'win_rate': 0.0,
            'avg_win': 0.0,
            'avg_loss': 0.0,
            'sharpe_ratio': 0.0
        }
        self.is_initialized = False
        
        logger.info("🎯 ML ATM Straddle Strategy initialized")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default strategy configuration"""
        return {
            'entry_criteria': {
                'min_confidence': 0.65,
                'min_iv_percentile': 20,
                'max_iv_percentile': 80,
                'min_time_to_expiry': 30,  # minutes
                'max_time_to_expiry': 240,  # minutes
                'min_volume_ratio': 0.8,
                'regime_alignment_threshold': 0.6
            },
            'position_sizing': {
                'base_position_size': 0.02,  # 2% of portfolio
                'max_position_size': 0.1,    # 10% of portfolio
                'confidence_multiplier': 1.5,
                'volatility_adjustment': True
            },
            'risk_management': {
                'stop_loss_percentage': 0.5,  # 50% of premium paid
                'target_profit_percentage': 1.0,  # 100% of premium paid
                'max_time_in_position': 120,  # minutes
                'max_concurrent_positions': 3,
                'daily_loss_limit': 0.05  # 5% of portfolio
            },
            'exit_criteria': {
                'profit_target_confidence': 0.7,
                'stop_loss_confidence': 0.8,
                'time_decay_threshold': 0.3,
                'volatility_contraction_threshold': 0.2
            },
            'ml_engine': {
                'prediction_cache_duration': 30,  # seconds
                'ensemble_weights': {
                    'volatility_predictor': 0.4,
                    'direction_classifier': 0.3,
                    'entry_timing': 0.2,
                    'exit_timing': 0.1
                }
            }
        }
    
    async def initialize(self) -> bool:
        """Initialize the strategy"""
        try:
            logger.info("🚀 Initializing ML ATM Straddle Strategy...")
            
            # Initialize ML engine
            await self.ml_engine.initialize()
            
            self.is_initialized = True
            logger.info("✅ ML ATM Straddle Strategy initialization complete")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize ML ATM Straddle Strategy: {e}")
            return False
    
    async def generate_signal(
        self,
        market_context: MarketContext,
        historical_data: Optional[pd.DataFrame] = None
    ) -> Dict[str, Any]:
        """
        Generate trading signal for ATM straddle
        
        Args:
            market_context: Current market conditions
            historical_data: Historical price/volume data
            
        Returns:
            Dictionary containing signal information
        """
        try:
            if not self.is_initialized:
                await self.initialize()
            
            # Get ML prediction
            ml_signal = await self.ml_engine.generate_straddle_signal(
                StraddleType.ATM, market_context, historical_data
            )
            
            # Apply strategy-specific filters
            signal = await self._apply_strategy_filters(ml_signal, market_context)
            
            # Calculate position sizing
            if signal['action'] in ['BUY', 'SELL']:
                signal['position_size'] = self._calculate_position_size(
                    signal['confidence'], market_context
                )
            
            # Add strategy metadata
            signal['strategy'] = 'ML_ATM_STRADDLE'
            signal['timestamp'] = datetime.now()
            signal['market_context'] = market_context
            
            logger.info(f"🎯 ATM Straddle Signal: {signal['action']} "
                       f"(confidence: {signal['confidence']:.3f})")
            
            return signal
            
        except Exception as e:
            logger.error(f"❌ Failed to generate ATM straddle signal: {e}")
            return self._get_default_signal()
    
    async def _apply_strategy_filters(
        self, 
        ml_signal: StraddleSignal, 
        market_context: MarketContext
    ) -> Dict[str, Any]:
        """Apply strategy-specific filters to ML signal"""
        
        signal = {
            'action': 'HOLD',
            'confidence': ml_signal.confidence,
            'entry_price': ml_signal.entry_price,
            'target_price': ml_signal.target_price,
            'stop_loss': ml_signal.stop_loss,
            'iv_prediction': ml_signal.iv_prediction,
            'regime_alignment': ml_signal.regime_alignment,
            'feature_importance': ml_signal.feature_importance,
            'ml_prediction': ml_signal.prediction.value,
            'filters_passed': []
        }
        
        # Check entry criteria
        entry_config = self.config['entry_criteria']
        
        # Confidence filter
        if ml_signal.confidence < entry_config['min_confidence']:
            signal['filters_passed'].append(f"Low confidence: {ml_signal.confidence:.3f}")
            return signal
        
        # IV percentile filter
        current_iv = getattr(market_context, 'current_iv', 0.2)
        iv_percentile = self._calculate_iv_percentile(current_iv, market_context)
        
        if iv_percentile < entry_config['min_iv_percentile']:
            signal['filters_passed'].append(f"IV too low: {iv_percentile:.1f}%")
            return signal
        
        if iv_percentile > entry_config['max_iv_percentile']:
            signal['filters_passed'].append(f"IV too high: {iv_percentile:.1f}%")
            return signal
        
        # Time to expiry filter
        time_to_expiry = getattr(market_context, 'time_to_expiry', 60)
        if time_to_expiry < entry_config['min_time_to_expiry']:
            signal['filters_passed'].append(f"Too close to expiry: {time_to_expiry}min")
            return signal
        
        if time_to_expiry > entry_config['max_time_to_expiry']:
            signal['filters_passed'].append(f"Too far from expiry: {time_to_expiry}min")
            return signal
        
        # Volume filter
        volume_ratio = self._calculate_volume_ratio(market_context)
        if volume_ratio < entry_config['min_volume_ratio']:
            signal['filters_passed'].append(f"Low volume: {volume_ratio:.2f}")
            return signal
        
        # Regime alignment filter
        if ml_signal.regime_alignment and ml_signal.regime_alignment < entry_config['regime_alignment_threshold']:
            signal['filters_passed'].append(f"Poor regime alignment: {ml_signal.regime_alignment:.3f}")
            return signal
        
        # Position limit filter
        if len(self.active_positions) >= self.config['risk_management']['max_concurrent_positions']:
            signal['filters_passed'].append("Max positions reached")
            return signal
        
        # Daily loss limit filter
        if self._check_daily_loss_limit():
            signal['filters_passed'].append("Daily loss limit reached")
            return signal
        
        # All filters passed
        signal['action'] = ml_signal.prediction.value
        signal['filters_passed'] = ['All filters passed']
        
        return signal
    
    def _calculate_position_size(self, confidence: float, market_context: MarketContext) -> float:
        """Calculate position size based on confidence and market conditions"""
        config = self.config['position_sizing']
        
        # Base position size
        base_size = config['base_position_size']
        
        # Confidence adjustment
        confidence_multiplier = 1 + (confidence - 0.5) * config['confidence_multiplier']
        
        # Volatility adjustment
        volatility_multiplier = 1.0
        if config['volatility_adjustment']:
            current_iv = getattr(market_context, 'current_iv', 0.2)
            # Reduce size for high volatility
            volatility_multiplier = max(0.5, 1 - (current_iv - 0.2) * 2)
        
        # Calculate final position size
        position_size = base_size * confidence_multiplier * volatility_multiplier
        
        # Apply maximum limit
        position_size = min(position_size, config['max_position_size'])
        
        return position_size
    
    def _calculate_iv_percentile(self, current_iv: float, market_context: MarketContext) -> float:
        """Calculate IV percentile (simplified implementation)"""
        # In production, this would use historical IV data
        # For now, use a simplified calculation
        
        # Assume typical IV range of 0.1 to 0.5
        min_iv = 0.1
        max_iv = 0.5
        
        percentile = ((current_iv - min_iv) / (max_iv - min_iv)) * 100
        return max(0, min(100, percentile))
    
    def _calculate_volume_ratio(self, market_context: MarketContext) -> float:
        """Calculate current volume ratio to average"""
        current_volume = getattr(market_context, 'current_volume', 1000)
        
        # In production, this would use historical volume data
        # For now, assume average volume of 1000
        avg_volume = 1000
        
        return current_volume / avg_volume if avg_volume > 0 else 1.0
    
    def _check_daily_loss_limit(self) -> bool:
        """Check if daily loss limit has been reached"""
        # Calculate today's PnL
        today = datetime.now().date()
        daily_pnl = 0.0
        
        for position in self.closed_positions:
            if position.entry_time.date() == today:
                daily_pnl += position.current_pnl
        
        # Add unrealized PnL from active positions
        for position in self.active_positions:
            daily_pnl += position.current_pnl
        
        # Check against limit (assuming portfolio value of 100,000)
        portfolio_value = 100000  # This should come from portfolio manager
        daily_loss_limit = portfolio_value * self.config['risk_management']['daily_loss_limit']
        
        return daily_pnl < -daily_loss_limit
    
    async def execute_trade(self, signal: Dict[str, Any]) -> bool:
        """Execute trade based on signal"""
        try:
            if signal['action'] not in ['BUY', 'SELL']:
                return False
            
            # Create position
            position = StraddlePosition(
                entry_time=datetime.now(),
                entry_price=signal['entry_price'],
                call_strike=signal['entry_price'],  # ATM
                put_strike=signal['entry_price'],   # ATM
                call_premium=signal['entry_price'] * 0.02,  # Simplified premium calculation
                put_premium=signal['entry_price'] * 0.02,   # Simplified premium calculation
                position_size=signal['position_size'],
                target_profit=signal.get('target_price'),
                stop_loss=signal.get('stop_loss')
            )
            
            # Add to active positions
            self.active_positions.append(position)
            
            # Update performance stats
            self.performance_stats['total_trades'] += 1
            
            logger.info(f"✅ Executed ATM straddle trade: {signal['action']} "
                       f"at {position.entry_price} (size: {position.position_size:.3f})")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to execute trade: {e}")
            return False
    
    async def manage_positions(self, market_context: MarketContext) -> List[Dict[str, Any]]:
        """Manage active positions"""
        actions = []
        
        for position in self.active_positions[:]:  # Copy list to allow modification
            action = await self._check_position_exit(position, market_context)
            if action:
                actions.append(action)
        
        return actions
    
    async def _check_position_exit(
        self, 
        position: StraddlePosition, 
        market_context: MarketContext
    ) -> Optional[Dict[str, Any]]:
        """Check if position should be exited"""
        
        current_price = market_context.current_price
        
        # Update position PnL (simplified calculation)
        position.current_pnl = self._calculate_position_pnl(position, current_price)
        
        # Check exit conditions
        exit_reason = None
        
        # Profit target
        if position.target_profit and position.current_pnl >= position.target_profit:
            exit_reason = "Profit target reached"
        
        # Stop loss
        elif position.stop_loss and position.current_pnl <= -position.stop_loss:
            exit_reason = "Stop loss triggered"
        
        # Time-based exit
        elif self._check_time_exit(position):
            exit_reason = "Time limit reached"
        
        # ML-based exit signal
        elif await self._check_ml_exit(position, market_context):
            exit_reason = "ML exit signal"
        
        if exit_reason:
            return await self._close_position(position, exit_reason, market_context)
        
        return None
    
    def _calculate_position_pnl(self, position: StraddlePosition, current_price: float) -> float:
        """Calculate current position PnL (simplified)"""
        # Simplified straddle PnL calculation
        # In production, this would use real option pricing
        
        total_premium_paid = position.call_premium + position.put_premium
        
        # Calculate intrinsic value
        call_intrinsic = max(0, current_price - position.call_strike)
        put_intrinsic = max(0, position.put_strike - current_price)
        
        # Simplified time value (decreases over time)
        time_elapsed = (datetime.now() - position.entry_time).total_seconds() / 3600  # hours
        time_decay_factor = max(0.1, 1 - time_elapsed / 24)  # Decay over 24 hours
        
        estimated_call_value = call_intrinsic + (position.call_premium * 0.5 * time_decay_factor)
        estimated_put_value = put_intrinsic + (position.put_premium * 0.5 * time_decay_factor)
        
        current_value = estimated_call_value + estimated_put_value
        pnl = (current_value - total_premium_paid) * position.position_size
        
        return pnl
    
    def _check_time_exit(self, position: StraddlePosition) -> bool:
        """Check if position should be exited due to time"""
        max_time = self.config['risk_management']['max_time_in_position']
        time_elapsed = (datetime.now() - position.entry_time).total_seconds() / 60  # minutes
        
        return time_elapsed >= max_time
    
    async def _check_ml_exit(self, position: StraddlePosition, market_context: MarketContext) -> bool:
        """Check ML-based exit conditions"""
        try:
            # Get fresh ML signal
            ml_signal = await self.ml_engine.generate_straddle_signal(
                StraddleType.ATM, market_context
            )
            
            exit_config = self.config['exit_criteria']
            
            # Check for exit signal
            if ml_signal.prediction.value == 'SELL' and ml_signal.confidence > exit_config['profit_target_confidence']:
                return True
            
            # Check for volatility contraction
            if ml_signal.iv_prediction:
                current_iv = getattr(market_context, 'current_iv', 0.2)
                iv_change = (ml_signal.iv_prediction - current_iv) / current_iv
                
                if iv_change < -exit_config['volatility_contraction_threshold']:
                    return True
            
            return False
            
        except Exception as e:
            logger.warning(f"⚠️ ML exit check failed: {e}")
            return False
    
    async def _close_position(
        self, 
        position: StraddlePosition, 
        exit_reason: str, 
        market_context: MarketContext
    ) -> Dict[str, Any]:
        """Close position and update statistics"""
        
        # Mark position as closed
        position.is_active = False
        position.exit_reason = exit_reason
        
        # Move to closed positions
        self.active_positions.remove(position)
        self.closed_positions.append(position)
        
        # Update performance statistics
        self._update_performance_stats(position)
        
        logger.info(f"🔚 Closed ATM straddle position: {exit_reason} "
                   f"(PnL: {position.current_pnl:.2f})")
        
        return {
            'action': 'CLOSE_POSITION',
            'position': position,
            'exit_reason': exit_reason,
            'pnl': position.current_pnl,
            'timestamp': datetime.now()
        }
    
    def _update_performance_stats(self, position: StraddlePosition):
        """Update strategy performance statistics"""
        pnl = position.current_pnl
        
        # Update totals
        self.performance_stats['total_pnl'] += pnl
        
        # Update win/loss counts
        if pnl > 0:
            self.performance_stats['winning_trades'] += 1
            
            # Update average win
            current_wins = self.performance_stats['winning_trades']
            current_avg_win = self.performance_stats['avg_win']
            self.performance_stats['avg_win'] = (current_avg_win * (current_wins - 1) + pnl) / current_wins
            
        else:
            self.performance_stats['losing_trades'] += 1
            
            # Update average loss
            current_losses = self.performance_stats['losing_trades']
            current_avg_loss = self.performance_stats['avg_loss']
            self.performance_stats['avg_loss'] = (current_avg_loss * (current_losses - 1) + abs(pnl)) / current_losses
        
        # Update win rate
        total_trades = self.performance_stats['total_trades']
        if total_trades > 0:
            self.performance_stats['win_rate'] = self.performance_stats['winning_trades'] / total_trades
        
        # Update max drawdown (simplified)
        if pnl < 0:
            self.performance_stats['max_drawdown'] = min(
                self.performance_stats['max_drawdown'], 
                self.performance_stats['total_pnl']
            )
    
    def _get_default_signal(self) -> Dict[str, Any]:
        """Get default signal when generation fails"""
        return {
            'action': 'HOLD',
            'confidence': 0.0,
            'entry_price': None,
            'target_price': None,
            'stop_loss': None,
            'position_size': 0.0,
            'strategy': 'ML_ATM_STRADDLE',
            'timestamp': datetime.now(),
            'error': 'Signal generation failed'
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics"""
        return self.performance_stats.copy()
    
    def get_active_positions(self) -> List[StraddlePosition]:
        """Get list of active positions"""
        return self.active_positions.copy()
    
    async def shutdown(self):
        """Shutdown the strategy"""
        logger.info("🛑 Shutting down ML ATM Straddle Strategy...")
        
        # Close all active positions
        for position in self.active_positions:
            position.is_active = False
            position.exit_reason = "Strategy shutdown"
        
        # Shutdown ML engine
        await self.ml_engine.shutdown()
        
        self.is_initialized = False
        logger.info("✅ ML ATM Straddle Strategy shutdown complete")

#!/usr/bin/env python3
"""
Volatility Features for ML Straddle System
==========================================

Comprehensive volatility feature engineering:
- IV skew and percentile calculations
- Realized volatility metrics
- Volatility surface analysis
- Term structure features
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class VolatilityFeatures:
    """Volatility Feature Generator"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize Volatility Features generator"""
        self.config = config or self._get_default_config()
        logger.info("📈 Volatility Features generator initialized")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'iv_percentile_lookback': 252,  # 1 year
            'realized_vol_windows': [5, 10, 20, 30],
            'skew_strikes': [-10, -5, 0, 5, 10],  # Relative to ATM
            'term_structure_periods': [7, 14, 30, 60, 90]
        }
    
    async def generate_features(
        self, 
        market_context, 
        historical_data: Optional[pd.DataFrame] = None
    ) -> Dict[str, float]:
        """Generate volatility features"""
        
        features = {}
        
        try:
            # Basic IV features from market context
            current_iv = getattr(market_context, 'current_iv', 0.2)
            features['current_iv'] = current_iv
            
            # IV percentile (simplified)
            features['iv_percentile'] = self._calculate_iv_percentile(current_iv)
            
            # IV rank (simplified)
            features['iv_rank'] = self._calculate_iv_rank(current_iv)
            
            # Realized volatility features
            if historical_data is not None:
                rv_features = self._calculate_realized_volatility_features(historical_data)
                features.update(rv_features)
            else:
                features.update(self._get_default_rv_features())
            
            # IV vs RV comparison
            features['hv_iv_ratio'] = features.get('realized_vol_20d', 0.18) / current_iv if current_iv > 0 else 1.0
            
            # Volatility skew features (simplified)
            features.update(self._calculate_skew_features(current_iv))
            
            # Term structure features (simplified)
            features.update(self._calculate_term_structure_features(current_iv))
            
            logger.debug(f"📈 Generated {len(features)} volatility features")
            return features
            
        except Exception as e:
            logger.error(f"❌ Failed to generate volatility features: {e}")
            return self._get_default_features()
    
    def _calculate_iv_percentile(self, current_iv: float) -> float:
        """Calculate IV percentile (simplified)"""
        # In production, this would use historical IV data
        # For now, use a simplified calculation based on typical ranges
        
        # Assume typical IV range of 0.1 to 0.5
        min_iv = 0.1
        max_iv = 0.5
        
        percentile = ((current_iv - min_iv) / (max_iv - min_iv)) * 100
        return max(0, min(100, percentile))
    
    def _calculate_iv_rank(self, current_iv: float) -> float:
        """Calculate IV rank (simplified)"""
        # Similar to percentile but with different calculation
        # Assume 252-day lookback
        
        # Simplified: use normal distribution assumption
        mean_iv = 0.25
        std_iv = 0.08
        
        z_score = (current_iv - mean_iv) / std_iv
        # Convert to rank (0-100)
        rank = 50 + (z_score * 20)  # Approximate normal CDF
        
        return max(0, min(100, rank))
    
    def _calculate_realized_volatility_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate realized volatility features"""
        features = {}
        
        try:
            if 'close' not in df.columns or len(df) < 5:
                return self._get_default_rv_features()
            
            # Calculate returns
            returns = df['close'].pct_change().dropna()
            
            # Calculate RV for different windows
            for window in self.config['realized_vol_windows']:
                if len(returns) >= window:
                    rv = returns.rolling(window=window).std().iloc[-1]
                    # Annualize (assuming daily data)
                    annualized_rv = rv * np.sqrt(252)
                    features[f'realized_vol_{window}d'] = float(annualized_rv) if not np.isnan(annualized_rv) else 0.2
                else:
                    features[f'realized_vol_{window}d'] = 0.2
            
            # Volatility of volatility
            if len(returns) >= 20:
                vol_series = returns.rolling(window=5).std()
                vol_of_vol = vol_series.std()
                features['vol_of_vol'] = float(vol_of_vol) if not np.isnan(vol_of_vol) else 0.05
            else:
                features['vol_of_vol'] = 0.05
            
            # Volatility trend
            if len(returns) >= 10:
                recent_vol = returns.tail(5).std()
                past_vol = returns.iloc[-10:-5].std()
                vol_trend = (recent_vol - past_vol) / past_vol if past_vol > 0 else 0.0
                features['vol_trend'] = float(vol_trend) if not np.isnan(vol_trend) else 0.0
            else:
                features['vol_trend'] = 0.0
                
        except Exception as e:
            logger.warning(f"⚠️ RV calculation failed: {e}")
            features.update(self._get_default_rv_features())
        
        return features
    
    def _get_default_rv_features(self) -> Dict[str, float]:
        """Get default realized volatility features"""
        features = {}
        for window in self.config['realized_vol_windows']:
            features[f'realized_vol_{window}d'] = 0.2  # 20% default
        
        features.update({
            'vol_of_vol': 0.05,
            'vol_trend': 0.0
        })
        
        return features
    
    def _calculate_skew_features(self, current_iv: float) -> Dict[str, float]:
        """Calculate volatility skew features (simplified)"""
        
        # In production, this would use actual option chain data
        # For now, generate realistic skew patterns
        
        features = {}
        
        # Typical equity skew pattern (higher IV for OTM puts)
        atm_iv = current_iv
        
        # Simulate skew
        otm_put_iv = atm_iv * 1.1  # 10% higher for OTM puts
        otm_call_iv = atm_iv * 0.95  # 5% lower for OTM calls
        
        features['iv_skew'] = otm_put_iv - otm_call_iv
        features['put_skew'] = otm_put_iv - atm_iv
        features['call_skew'] = atm_iv - otm_call_iv
        
        # Skew slope (change per strike)
        features['skew_slope'] = features['iv_skew'] / 20  # Assume 20-point spread
        
        # Skew convexity (simplified)
        features['skew_convexity'] = abs(features['put_skew'] - features['call_skew']) / 2
        
        return features
    
    def _calculate_term_structure_features(self, current_iv: float) -> Dict[str, float]:
        """Calculate term structure features (simplified)"""
        
        # In production, this would use actual term structure data
        # For now, generate realistic term structure patterns
        
        features = {}
        
        # Typical term structure (contango - longer term higher IV)
        short_term_iv = current_iv * 0.95  # Front month slightly lower
        medium_term_iv = current_iv
        long_term_iv = current_iv * 1.05  # Back months slightly higher
        
        features['short_term_iv'] = short_term_iv
        features['medium_term_iv'] = medium_term_iv
        features['long_term_iv'] = long_term_iv
        
        # Term structure slope
        features['term_structure_slope'] = long_term_iv - short_term_iv
        
        # Term structure curvature
        features['term_structure_curvature'] = medium_term_iv - (short_term_iv + long_term_iv) / 2
        
        # Term structure shape
        if features['term_structure_slope'] > 0.02:
            features['term_structure_shape'] = 1.0  # Contango
        elif features['term_structure_slope'] < -0.02:
            features['term_structure_shape'] = -1.0  # Backwardation
        else:
            features['term_structure_shape'] = 0.0  # Flat
        
        return features
    
    def _get_default_features(self) -> Dict[str, float]:
        """Get default features when calculation fails"""
        features = {
            'current_iv': 0.2,
            'iv_percentile': 50.0,
            'iv_rank': 50.0,
            'hv_iv_ratio': 0.9,
            'iv_skew': 0.02,
            'put_skew': 0.015,
            'call_skew': -0.005,
            'skew_slope': 0.001,
            'skew_convexity': 0.01,
            'short_term_iv': 0.19,
            'medium_term_iv': 0.2,
            'long_term_iv': 0.21,
            'term_structure_slope': 0.02,
            'term_structure_curvature': 0.0,
            'term_structure_shape': 1.0
        }
        
        # Add default RV features
        features.update(self._get_default_rv_features())
        
        return features

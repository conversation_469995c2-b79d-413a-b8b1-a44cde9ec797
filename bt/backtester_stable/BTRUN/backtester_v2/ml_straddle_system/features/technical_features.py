#!/usr/bin/env python3
"""
Technical Features for ML Straddle System
=========================================

Comprehensive technical indicator feature engineering for straddle strategies:
- EMA indicators (3, 5, 10, 15 periods)
- VWAP and Previous Day VWAP
- RSI, MACD, Bollinger Bands
- Volume and momentum indicators
- Price action patterns
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
# import talib  # Commented out - using simplified calculations instead

logger = logging.getLogger(__name__)

class TechnicalFeatures:
    """
    Technical Feature Generator for ML Straddle System
    
    Generates comprehensive technical indicators optimized for straddle strategies:
    - Trend indicators (EMAs, VWAP)
    - Momentum indicators (RSI, MACD)
    - Volatility indicators (Bollinger Bands, ATR)
    - Volume indicators (Volume ratios, OBV)
    - Price action patterns
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize Technical Features generator"""
        self.config = config or self._get_default_config()
        logger.info("📊 Technical Features generator initialized")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for technical features"""
        return {
            'ema_periods': [3, 5, 10, 15],
            'rsi_period': 14,
            'macd_periods': {'fast': 12, 'slow': 26, 'signal': 9},
            'bollinger_periods': {'period': 20, 'std': 2},
            'atr_period': 14,
            'volume_sma_period': 20,
            'vwap_periods': [1, 5, 20],  # 1-day, 5-day, 20-day VWAP
            'momentum_periods': [5, 10, 20],
            'price_change_periods': [1, 5, 15, 30]  # minutes
        }
    
    async def generate_features(
        self, 
        market_context, 
        historical_data: Optional[pd.DataFrame] = None
    ) -> Dict[str, float]:
        """
        Generate comprehensive technical features
        
        Args:
            market_context: Current market conditions
            historical_data: Historical OHLCV data
            
        Returns:
            Dictionary of technical features
        """
        features = {}
        
        try:
            # Basic price features from market context
            features.update(self._get_basic_price_features(market_context))
            
            # If historical data is available, calculate advanced features
            if historical_data is not None and len(historical_data) > 0:
                features.update(await self._calculate_advanced_features(historical_data))
            else:
                # Use simplified features based on market context only
                features.update(self._get_simplified_features(market_context))
            
            logger.debug(f"📊 Generated {len(features)} technical features")
            return features
            
        except Exception as e:
            logger.error(f"❌ Failed to generate technical features: {e}")
            return self._get_default_features()
    
    def _get_basic_price_features(self, market_context) -> Dict[str, float]:
        """Extract basic price features from market context"""
        features = {}
        
        # Current price information
        features['current_price'] = getattr(market_context, 'current_price', 100.0)
        features['vwap'] = getattr(market_context, 'vwap', features['current_price'])
        features['previous_day_vwap'] = getattr(market_context, 'previous_day_vwap', features['current_price'])
        
        # EMA values from market context
        features['ema_3'] = getattr(market_context, 'ema_3', features['current_price'])
        features['ema_5'] = getattr(market_context, 'ema_5', features['current_price'])
        features['ema_10'] = getattr(market_context, 'ema_10', features['current_price'])
        features['ema_15'] = getattr(market_context, 'ema_15', features['current_price'])
        
        # Volume information
        features['current_volume'] = getattr(market_context, 'current_volume', 1000)
        
        return features
    
    async def _calculate_advanced_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate advanced technical features from historical data"""
        features = {}
        
        # Ensure we have required columns
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        if not all(col in df.columns for col in required_cols):
            logger.warning("⚠️ Missing required OHLCV columns, using simplified features")
            return {}
        
        # Get latest values
        latest = df.iloc[-1]
        
        # Price features
        features['current_price'] = float(latest['close'])
        features['open_price'] = float(latest['open'])
        features['high_price'] = float(latest['high'])
        features['low_price'] = float(latest['low'])
        features['current_volume'] = float(latest['volume'])
        
        # EMA calculations (simplified)
        for period in self.config['ema_periods']:
            if len(df) >= period:
                ema_values = self._calculate_ema(df['close'].values, period)
                features[f'ema_{period}'] = float(ema_values[-1]) if not np.isnan(ema_values[-1]) else features['current_price']
            else:
                features[f'ema_{period}'] = features['current_price']
        
        # VWAP calculations
        features.update(self._calculate_vwap_features(df))
        
        # RSI (simplified)
        if len(df) >= self.config['rsi_period']:
            rsi_values = self._calculate_rsi(df['close'].values, self.config['rsi_period'])
            features['rsi'] = float(rsi_values[-1]) if not np.isnan(rsi_values[-1]) else 50.0
        else:
            features['rsi'] = 50.0
        
        # MACD
        features.update(self._calculate_macd_features(df))
        
        # Bollinger Bands
        features.update(self._calculate_bollinger_features(df))
        
        # ATR (Average True Range) - simplified
        if len(df) >= self.config['atr_period']:
            atr_values = self._calculate_atr(df, self.config['atr_period'])
            features['atr'] = float(atr_values[-1]) if not np.isnan(atr_values[-1]) else 0.0
        else:
            features['atr'] = 0.0
        
        # Volume features
        features.update(self._calculate_volume_features(df))
        
        # Momentum features
        features.update(self._calculate_momentum_features(df))
        
        # Price change features
        features.update(self._calculate_price_change_features(df))
        
        # Pattern recognition features
        features.update(self._calculate_pattern_features(df))
        
        return features
    
    def _calculate_vwap_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate VWAP-related features"""
        features = {}
        
        try:
            # Calculate VWAP for different periods
            for period in self.config['vwap_periods']:
                if len(df) >= period:
                    # Calculate VWAP for the last 'period' rows
                    recent_df = df.tail(period)
                    typical_price = (recent_df['high'] + recent_df['low'] + recent_df['close']) / 3
                    vwap = (typical_price * recent_df['volume']).sum() / recent_df['volume'].sum()
                    features[f'vwap_{period}d'] = float(vwap)
                else:
                    features[f'vwap_{period}d'] = float(df['close'].iloc[-1])
            
            # Current VWAP (today's VWAP)
            features['vwap'] = features.get('vwap_1d', float(df['close'].iloc[-1]))
            
            # Previous day VWAP (if available)
            if len(df) >= 2:
                features['previous_day_vwap'] = features.get('vwap_1d', float(df['close'].iloc[-2]))
            else:
                features['previous_day_vwap'] = features['vwap']
            
            # VWAP relative position
            current_price = float(df['close'].iloc[-1])
            features['vwap_relative_position'] = (current_price - features['vwap']) / features['vwap']
            
        except Exception as e:
            logger.warning(f"⚠️ VWAP calculation failed: {e}")
            current_price = float(df['close'].iloc[-1])
            features['vwap'] = current_price
            features['previous_day_vwap'] = current_price
            features['vwap_relative_position'] = 0.0
        
        return features
    
    def _calculate_macd_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate MACD-related features"""
        features = {}
        
        try:
            macd_config = self.config['macd_periods']
            min_periods = max(macd_config['slow'], macd_config['signal'])
            
            if len(df) >= min_periods:
                macd, macd_signal, macd_hist = self._calculate_macd(
                    df['close'].values,
                    macd_config['fast'],
                    macd_config['slow'],
                    macd_config['signal']
                )

                features['macd'] = float(macd[-1]) if not np.isnan(macd[-1]) else 0.0
                features['macd_signal'] = float(macd_signal[-1]) if not np.isnan(macd_signal[-1]) else 0.0
                features['macd_histogram'] = float(macd_hist[-1]) if not np.isnan(macd_hist[-1]) else 0.0

                # MACD crossover signals
                if len(macd) >= 2:
                    features['macd_bullish_crossover'] = 1.0 if (macd[-1] > macd_signal[-1] and macd[-2] <= macd_signal[-2]) else 0.0
                    features['macd_bearish_crossover'] = 1.0 if (macd[-1] < macd_signal[-1] and macd[-2] >= macd_signal[-2]) else 0.0
                else:
                    features['macd_bullish_crossover'] = 0.0
                    features['macd_bearish_crossover'] = 0.0
            else:
                features['macd'] = 0.0
                features['macd_signal'] = 0.0
                features['macd_histogram'] = 0.0
                features['macd_bullish_crossover'] = 0.0
                features['macd_bearish_crossover'] = 0.0
                
        except Exception as e:
            logger.warning(f"⚠️ MACD calculation failed: {e}")
            features.update({
                'macd': 0.0, 'macd_signal': 0.0, 'macd_histogram': 0.0,
                'macd_bullish_crossover': 0.0, 'macd_bearish_crossover': 0.0
            })
        
        return features
    
    def _calculate_bollinger_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate Bollinger Bands features"""
        features = {}
        
        try:
            bb_config = self.config['bollinger_periods']
            
            if len(df) >= bb_config['period']:
                upper, middle, lower = self._calculate_bollinger_bands(
                    df['close'].values,
                    bb_config['period'],
                    bb_config['std']
                )
                
                current_price = float(df['close'].iloc[-1])
                features['bb_upper'] = float(upper[-1]) if not np.isnan(upper[-1]) else current_price
                features['bb_middle'] = float(middle[-1]) if not np.isnan(middle[-1]) else current_price
                features['bb_lower'] = float(lower[-1]) if not np.isnan(lower[-1]) else current_price
                
                # Bollinger Band position (0 = lower band, 1 = upper band)
                bb_width = features['bb_upper'] - features['bb_lower']
                if bb_width > 0:
                    features['bollinger_position'] = (current_price - features['bb_lower']) / bb_width
                else:
                    features['bollinger_position'] = 0.5
                
                # Bollinger Band squeeze indicator
                features['bb_squeeze'] = 1.0 if bb_width < (current_price * 0.02) else 0.0  # 2% squeeze threshold
                
            else:
                current_price = float(df['close'].iloc[-1])
                features['bb_upper'] = current_price * 1.02
                features['bb_middle'] = current_price
                features['bb_lower'] = current_price * 0.98
                features['bollinger_position'] = 0.5
                features['bb_squeeze'] = 0.0
                
        except Exception as e:
            logger.warning(f"⚠️ Bollinger Bands calculation failed: {e}")
            current_price = float(df['close'].iloc[-1])
            features.update({
                'bb_upper': current_price * 1.02, 'bb_middle': current_price, 'bb_lower': current_price * 0.98,
                'bollinger_position': 0.5, 'bb_squeeze': 0.0
            })
        
        return features
    
    def _calculate_volume_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate volume-related features"""
        features = {}
        
        try:
            current_volume = float(df['volume'].iloc[-1])
            features['current_volume'] = current_volume
            
            # Volume SMA
            volume_sma_period = self.config['volume_sma_period']
            if len(df) >= volume_sma_period:
                volume_sma = df['volume'].tail(volume_sma_period).mean()
                features['volume_sma'] = float(volume_sma)
                features['volume_ratio'] = current_volume / volume_sma if volume_sma > 0 else 1.0
            else:
                features['volume_sma'] = current_volume
                features['volume_ratio'] = 1.0
            
            # On-Balance Volume (OBV) - simplified
            if len(df) >= 2:
                obv_values = self._calculate_obv(df['close'].values, df['volume'].values)
                features['obv'] = float(obv_values[-1]) if not np.isnan(obv_values[-1]) else 0.0

                # OBV trend
                if len(obv_values) >= 5:
                    obv_trend = np.polyfit(range(5), obv_values[-5:], 1)[0]
                    features['obv_trend'] = float(obv_trend)
                else:
                    features['obv_trend'] = 0.0
            else:
                features['obv'] = 0.0
                features['obv_trend'] = 0.0
                
        except Exception as e:
            logger.warning(f"⚠️ Volume features calculation failed: {e}")
            features.update({
                'current_volume': 1000.0, 'volume_sma': 1000.0, 'volume_ratio': 1.0,
                'obv': 0.0, 'obv_trend': 0.0
            })
        
        return features
    
    def _calculate_momentum_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate momentum-related features"""
        features = {}
        
        try:
            for period in self.config['momentum_periods']:
                if len(df) >= period + 1:
                    # Rate of Change (ROC) - simplified
                    roc_values = self._calculate_roc(df['close'].values, period)
                    features[f'roc_{period}'] = float(roc_values[-1]) if not np.isnan(roc_values[-1]) else 0.0

                    # Momentum - simplified
                    momentum_values = self._calculate_momentum(df['close'].values, period)
                    features[f'momentum_{period}'] = float(momentum_values[-1]) if not np.isnan(momentum_values[-1]) else 0.0
                else:
                    features[f'roc_{period}'] = 0.0
                    features[f'momentum_{period}'] = 0.0
                    
        except Exception as e:
            logger.warning(f"⚠️ Momentum features calculation failed: {e}")
            for period in self.config['momentum_periods']:
                features[f'roc_{period}'] = 0.0
                features[f'momentum_{period}'] = 0.0
        
        return features
    
    def _calculate_price_change_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate price change features for different time periods"""
        features = {}
        
        try:
            current_price = float(df['close'].iloc[-1])
            
            for period in self.config['price_change_periods']:
                if len(df) >= period + 1:
                    past_price = float(df['close'].iloc[-(period + 1)])
                    price_change = (current_price - past_price) / past_price
                    features[f'price_change_{period}min'] = price_change
                else:
                    features[f'price_change_{period}min'] = 0.0
                    
        except Exception as e:
            logger.warning(f"⚠️ Price change features calculation failed: {e}")
            for period in self.config['price_change_periods']:
                features[f'price_change_{period}min'] = 0.0
        
        return features
    
    def _calculate_pattern_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate candlestick pattern features"""
        features = {}
        
        try:
            if len(df) >= 5:  # Minimum required for pattern recognition
                # Simplified pattern recognition
                features['pattern_doji'] = self._detect_doji_pattern(df)
                features['pattern_hammer'] = self._detect_hammer_pattern(df)
                features['pattern_engulfing'] = self._detect_engulfing_pattern(df)
                
            else:
                features['pattern_doji'] = 0.0
                features['pattern_hammer'] = 0.0
                features['pattern_engulfing'] = 0.0
                
        except Exception as e:
            logger.warning(f"⚠️ Pattern features calculation failed: {e}")
            features.update({
                'pattern_doji': 0.0, 'pattern_hammer': 0.0, 'pattern_engulfing': 0.0
            })
        
        return features
    
    def _get_simplified_features(self, market_context) -> Dict[str, float]:
        """Get simplified features when historical data is not available"""
        features = {}
        
        # Use market context values or defaults
        current_price = getattr(market_context, 'current_price', 100.0)
        
        # Default technical indicators
        features.update({
            'rsi': 50.0,
            'macd': 0.0,
            'macd_signal': 0.0,
            'macd_histogram': 0.0,
            'macd_bullish_crossover': 0.0,
            'macd_bearish_crossover': 0.0,
            'bb_upper': current_price * 1.02,
            'bb_middle': current_price,
            'bb_lower': current_price * 0.98,
            'bollinger_position': 0.5,
            'bb_squeeze': 0.0,
            'atr': current_price * 0.01,
            'volume_ratio': 1.0,
            'obv': 0.0,
            'obv_trend': 0.0
        })
        
        # Default momentum and price change features
        for period in self.config['momentum_periods']:
            features[f'roc_{period}'] = 0.0
            features[f'momentum_{period}'] = 0.0
        
        for period in self.config['price_change_periods']:
            features[f'price_change_{period}min'] = 0.0
        
        # Default pattern features
        features.update({
            'pattern_doji': 0.0,
            'pattern_hammer': 0.0,
            'pattern_engulfing': 0.0
        })
        
        return features
    
    def _get_default_features(self) -> Dict[str, float]:
        """Get default features when calculation fails"""
        features = {
            'current_price': 100.0,
            'vwap': 100.0,
            'previous_day_vwap': 100.0,
            'current_volume': 1000.0
        }
        
        # Default EMA values
        for period in self.config['ema_periods']:
            features[f'ema_{period}'] = 100.0
        
        # Add all other default features
        features.update(self._get_simplified_features(type('MockContext', (), {'current_price': 100.0})()))
        
        return features

    def _calculate_ema(self, prices, period):
        """Calculate Exponential Moving Average (simplified)"""
        alpha = 2.0 / (period + 1.0)
        ema = np.zeros_like(prices)
        ema[0] = prices[0]

        for i in range(1, len(prices)):
            ema[i] = alpha * prices[i] + (1 - alpha) * ema[i-1]

        return ema

    def _calculate_rsi(self, prices, period):
        """Calculate RSI (simplified)"""
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gains = np.zeros_like(prices)
        avg_losses = np.zeros_like(prices)

        # Initial averages
        if len(gains) >= period:
            avg_gains[period] = np.mean(gains[:period])
            avg_losses[period] = np.mean(losses[:period])

            # Smoothed averages
            for i in range(period + 1, len(prices)):
                avg_gains[i] = (avg_gains[i-1] * (period - 1) + gains[i-1]) / period
                avg_losses[i] = (avg_losses[i-1] * (period - 1) + losses[i-1]) / period

        rs = avg_gains / np.where(avg_losses == 0, 1e-10, avg_losses)
        rsi = 100 - (100 / (1 + rs))

        return rsi

    def _calculate_atr(self, df, period):
        """Calculate Average True Range (simplified)"""
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values

        # True Range calculation
        tr1 = high - low
        tr2 = np.abs(high - np.roll(close, 1))
        tr3 = np.abs(low - np.roll(close, 1))

        tr = np.maximum(tr1, np.maximum(tr2, tr3))
        tr[0] = tr1[0]  # First value

        # Simple moving average of TR
        atr = np.zeros_like(tr)
        atr[period-1] = np.mean(tr[:period])

        for i in range(period, len(tr)):
            atr[i] = (atr[i-1] * (period - 1) + tr[i]) / period

        return atr

    def _calculate_macd(self, prices, fast, slow, signal):
        """Calculate MACD (simplified)"""
        ema_fast = self._calculate_ema(prices, fast)
        ema_slow = self._calculate_ema(prices, slow)

        macd = ema_fast - ema_slow
        macd_signal = self._calculate_ema(macd, signal)
        macd_hist = macd - macd_signal

        return macd, macd_signal, macd_hist

    def _calculate_bollinger_bands(self, prices, period, std_dev):
        """Calculate Bollinger Bands (simplified)"""
        sma = np.zeros_like(prices)
        std = np.zeros_like(prices)

        for i in range(period-1, len(prices)):
            window = prices[i-period+1:i+1]
            sma[i] = np.mean(window)
            std[i] = np.std(window)

        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)

        return upper, sma, lower

    def _calculate_obv(self, prices, volumes):
        """Calculate On-Balance Volume (simplified)"""
        obv = np.zeros_like(prices)
        obv[0] = volumes[0]

        for i in range(1, len(prices)):
            if prices[i] > prices[i-1]:
                obv[i] = obv[i-1] + volumes[i]
            elif prices[i] < prices[i-1]:
                obv[i] = obv[i-1] - volumes[i]
            else:
                obv[i] = obv[i-1]

        return obv

    def _calculate_roc(self, prices, period):
        """Calculate Rate of Change (simplified)"""
        roc = np.zeros_like(prices)

        for i in range(period, len(prices)):
            roc[i] = ((prices[i] - prices[i-period]) / prices[i-period]) * 100

        return roc

    def _calculate_momentum(self, prices, period):
        """Calculate Momentum (simplified)"""
        momentum = np.zeros_like(prices)

        for i in range(period, len(prices)):
            momentum[i] = prices[i] - prices[i-period]

        return momentum

    def _detect_doji_pattern(self, df):
        """Detect Doji pattern (simplified)"""
        latest = df.iloc[-1]
        body_size = abs(latest['close'] - latest['open'])
        range_size = latest['high'] - latest['low']

        # Doji: small body relative to range
        return 1.0 if body_size < (range_size * 0.1) else 0.0

    def _detect_hammer_pattern(self, df):
        """Detect Hammer pattern (simplified)"""
        latest = df.iloc[-1]
        body_size = abs(latest['close'] - latest['open'])
        lower_shadow = min(latest['open'], latest['close']) - latest['low']
        upper_shadow = latest['high'] - max(latest['open'], latest['close'])

        # Hammer: long lower shadow, small upper shadow
        return 1.0 if (lower_shadow > body_size * 2 and upper_shadow < body_size) else 0.0

    def _detect_engulfing_pattern(self, df):
        """Detect Engulfing pattern (simplified)"""
        if len(df) < 2:
            return 0.0

        current = df.iloc[-1]
        previous = df.iloc[-2]

        # Bullish engulfing
        if (previous['close'] < previous['open'] and  # Previous red candle
            current['close'] > current['open'] and   # Current green candle
            current['open'] < previous['close'] and  # Current opens below previous close
            current['close'] > previous['open']):    # Current closes above previous open
            return 1.0

        # Bearish engulfing
        if (previous['close'] > previous['open'] and  # Previous green candle
            current['close'] < current['open'] and   # Current red candle
            current['open'] > previous['close'] and  # Current opens above previous close
            current['close'] < previous['open']):    # Current closes below previous open
            return -1.0

        return 0.0

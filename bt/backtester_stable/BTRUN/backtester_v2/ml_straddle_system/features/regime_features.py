#!/usr/bin/env python3
"""
Regime Features for ML Straddle System
=====================================

Market regime detection and feature engineering:
- 8-regime and 18-regime classification
- Regime stability and persistence
- Regime transition probabilities
- Regime-specific indicators
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class RegimeFeatures:
    """Regime Feature Generator"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize Regime Features generator"""
        self.config = config or self._get_default_config()
        logger.info("🎭 Regime Features generator initialized")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'regime_types': {
                '8_regime': [
                    'BULLISH_HIGH_VOLATILE', 'BULLISH_NORMAL_VOLATILE', 'BULLISH_LOW_VOLATILE',
                    'BEARISH_HIGH_VOLATILE', 'BEARISH_NORMAL_VOLATILE', 'BEARISH_LOW_VOLATILE',
                    'NEUTRAL_HIGH_VOLATILE', 'NEUTRAL_LOW_VOLATILE'
                ],
                '18_regime': [
                    'STRONG_BULLISH_HIGH_VOLATILE', 'STRONG_BULLISH_NORMAL_VOLATILE', 'STRONG_BULLISH_LOW_VOLATILE',
                    'MILD_BULLISH_HIGH_VOLATILE', 'MILD_BULLISH_NORMAL_VOLATILE', 'MILD_BULLISH_LOW_VOLATILE',
                    'STRONG_BEARISH_HIGH_VOLATILE', 'STRONG_BEARISH_NORMAL_VOLATILE', 'STRONG_BEARISH_LOW_VOLATILE',
                    'MILD_BEARISH_HIGH_VOLATILE', 'MILD_BEARISH_NORMAL_VOLATILE', 'MILD_BEARISH_LOW_VOLATILE',
                    'NEUTRAL_HIGH_VOLATILE', 'NEUTRAL_NORMAL_VOLATILE', 'NEUTRAL_LOW_VOLATILE',
                    'SIDEWAYS_HIGH_VOLATILE', 'SIDEWAYS_NORMAL_VOLATILE', 'SIDEWAYS_LOW_VOLATILE'
                ]
            },
            'volatility_thresholds': {
                'low': 0.15,
                'normal': 0.25,
                'high': 0.35
            },
            'trend_thresholds': {
                'strong': 0.02,  # 2% daily move
                'mild': 0.01     # 1% daily move
            },
            'regime_persistence_window': 20
        }
    
    async def generate_features(
        self, 
        market_context, 
        historical_data: Optional[pd.DataFrame] = None
    ) -> Dict[str, float]:
        """Generate regime features"""
        
        features = {}
        
        try:
            # Get regime information from market context
            regime_type = getattr(market_context, 'regime_type', 'NEUTRAL_NORMAL_VOLATILE')
            regime_confidence = getattr(market_context, 'regime_confidence', 0.7)
            
            # Basic regime features
            features['regime_confidence'] = regime_confidence
            
            # One-hot encode regime types (8-regime system)
            regime_8_features = self._encode_8_regime_system(regime_type)
            features.update(regime_8_features)
            
            # One-hot encode regime types (18-regime system)
            regime_18_features = self._encode_18_regime_system(regime_type)
            features.update(regime_18_features)
            
            # Regime stability features
            if historical_data is not None:
                stability_features = self._calculate_regime_stability(historical_data, regime_type)
                features.update(stability_features)
            else:
                features.update(self._get_default_stability_features())
            
            # Regime transition features
            transition_features = self._calculate_regime_transitions(regime_type, historical_data)
            features.update(transition_features)
            
            # Regime-specific indicators
            regime_indicators = self._calculate_regime_indicators(market_context, regime_type)
            features.update(regime_indicators)
            
            logger.debug(f"🎭 Generated {len(features)} regime features for {regime_type}")
            return features
            
        except Exception as e:
            logger.error(f"❌ Failed to generate regime features: {e}")
            return self._get_default_features()
    
    def _encode_8_regime_system(self, regime_type: str) -> Dict[str, float]:
        """Encode 8-regime system as one-hot features"""
        
        regime_8_types = self.config['regime_types']['8_regime']
        features = {}
        
        # Initialize all regime types to 0
        for regime in regime_8_types:
            features[f'regime_8_{regime.lower()}'] = 0.0
        
        # Set current regime to 1
        regime_key = f'regime_8_{regime_type.lower()}'
        if regime_key in features:
            features[regime_key] = 1.0
        else:
            # Default to neutral if regime not recognized
            features['regime_8_neutral_normal_volatile'] = 1.0
        
        # Aggregate features
        features['regime_8_bullish'] = max(
            features.get('regime_8_bullish_high_volatile', 0),
            features.get('regime_8_bullish_normal_volatile', 0),
            features.get('regime_8_bullish_low_volatile', 0)
        )
        
        features['regime_8_bearish'] = max(
            features.get('regime_8_bearish_high_volatile', 0),
            features.get('regime_8_bearish_normal_volatile', 0),
            features.get('regime_8_bearish_low_volatile', 0)
        )
        
        features['regime_8_neutral'] = max(
            features.get('regime_8_neutral_high_volatile', 0),
            features.get('regime_8_neutral_low_volatile', 0)
        )
        
        features['regime_8_high_vol'] = max(
            features.get('regime_8_bullish_high_volatile', 0),
            features.get('regime_8_bearish_high_volatile', 0),
            features.get('regime_8_neutral_high_volatile', 0)
        )
        
        features['regime_8_normal_vol'] = max(
            features.get('regime_8_bullish_normal_volatile', 0),
            features.get('regime_8_bearish_normal_volatile', 0)
        )
        
        features['regime_8_low_vol'] = max(
            features.get('regime_8_bullish_low_volatile', 0),
            features.get('regime_8_bearish_low_volatile', 0),
            features.get('regime_8_neutral_low_volatile', 0)
        )
        
        return features
    
    def _encode_18_regime_system(self, regime_type: str) -> Dict[str, float]:
        """Encode 18-regime system as one-hot features"""
        
        regime_18_types = self.config['regime_types']['18_regime']
        features = {}
        
        # Initialize all regime types to 0
        for regime in regime_18_types:
            features[f'regime_18_{regime.lower()}'] = 0.0
        
        # Map 8-regime to 18-regime (simplified mapping)
        regime_18_mapped = self._map_8_to_18_regime(regime_type)
        
        # Set current regime to 1
        regime_key = f'regime_18_{regime_18_mapped.lower()}'
        if regime_key in features:
            features[regime_key] = 1.0
        else:
            # Default to neutral if regime not recognized
            features['regime_18_neutral_normal_volatile'] = 1.0
        
        # Aggregate features for 18-regime system
        features['regime_18_strong_bullish'] = max(
            features.get('regime_18_strong_bullish_high_volatile', 0),
            features.get('regime_18_strong_bullish_normal_volatile', 0),
            features.get('regime_18_strong_bullish_low_volatile', 0)
        )
        
        features['regime_18_mild_bullish'] = max(
            features.get('regime_18_mild_bullish_high_volatile', 0),
            features.get('regime_18_mild_bullish_normal_volatile', 0),
            features.get('regime_18_mild_bullish_low_volatile', 0)
        )
        
        features['regime_18_strong_bearish'] = max(
            features.get('regime_18_strong_bearish_high_volatile', 0),
            features.get('regime_18_strong_bearish_normal_volatile', 0),
            features.get('regime_18_strong_bearish_low_volatile', 0)
        )
        
        features['regime_18_mild_bearish'] = max(
            features.get('regime_18_mild_bearish_high_volatile', 0),
            features.get('regime_18_mild_bearish_normal_volatile', 0),
            features.get('regime_18_mild_bearish_low_volatile', 0)
        )
        
        return features
    
    def _map_8_to_18_regime(self, regime_8: str) -> str:
        """Map 8-regime type to 18-regime type"""
        
        mapping = {
            'BULLISH_HIGH_VOLATILE': 'MILD_BULLISH_HIGH_VOLATILE',
            'BULLISH_NORMAL_VOLATILE': 'MILD_BULLISH_NORMAL_VOLATILE',
            'BULLISH_LOW_VOLATILE': 'MILD_BULLISH_LOW_VOLATILE',
            'BEARISH_HIGH_VOLATILE': 'MILD_BEARISH_HIGH_VOLATILE',
            'BEARISH_NORMAL_VOLATILE': 'MILD_BEARISH_NORMAL_VOLATILE',
            'BEARISH_LOW_VOLATILE': 'MILD_BEARISH_LOW_VOLATILE',
            'NEUTRAL_HIGH_VOLATILE': 'NEUTRAL_HIGH_VOLATILE',
            'NEUTRAL_LOW_VOLATILE': 'NEUTRAL_LOW_VOLATILE'
        }
        
        return mapping.get(regime_8, 'NEUTRAL_NORMAL_VOLATILE')
    
    def _calculate_regime_stability(self, df: Optional[pd.DataFrame], current_regime: str) -> Dict[str, float]:
        """Calculate regime stability metrics"""
        
        features = {}
        
        try:
            if df is None or len(df) < 10:
                return self._get_default_stability_features()
            
            # Simulate regime history (in production, this would be stored)
            window = min(self.config['regime_persistence_window'], len(df))
            
            # Simplified regime persistence calculation
            # In production, this would use actual regime history
            
            # Volatility stability
            if 'close' in df.columns:
                returns = df['close'].pct_change().dropna()
                if len(returns) >= window:
                    recent_vol = returns.tail(window).std()
                    past_vol = returns.iloc[-2*window:-window].std() if len(returns) >= 2*window else recent_vol
                    
                    vol_stability = 1 - abs(recent_vol - past_vol) / max(past_vol, 0.01)
                    features['volatility_stability'] = max(0, min(1, vol_stability))
                else:
                    features['volatility_stability'] = 0.7
            else:
                features['volatility_stability'] = 0.7
            
            # Trend stability
            if 'close' in df.columns and len(df) >= window:
                recent_trend = (df['close'].iloc[-1] - df['close'].iloc[-window]) / df['close'].iloc[-window]
                past_trend = (df['close'].iloc[-window] - df['close'].iloc[-2*window]) / df['close'].iloc[-2*window] if len(df) >= 2*window else recent_trend
                
                trend_consistency = 1 - abs(recent_trend - past_trend) / max(abs(past_trend), 0.01)
                features['trend_stability'] = max(0, min(1, trend_consistency))
            else:
                features['trend_stability'] = 0.7
            
            # Overall regime stability
            features['regime_stability'] = (features['volatility_stability'] + features['trend_stability']) / 2
            
            # Regime persistence (simplified)
            features['regime_persistence'] = 0.7 + np.random.random() * 0.2  # Mock persistence
            
        except Exception as e:
            logger.warning(f"⚠️ Regime stability calculation failed: {e}")
            features = self._get_default_stability_features()
        
        return features
    
    def _get_default_stability_features(self) -> Dict[str, float]:
        """Get default stability features"""
        return {
            'volatility_stability': 0.7,
            'trend_stability': 0.7,
            'regime_stability': 0.7,
            'regime_persistence': 0.7
        }
    
    def _calculate_regime_transitions(self, current_regime: str, df: Optional[pd.DataFrame]) -> Dict[str, float]:
        """Calculate regime transition probabilities"""
        
        features = {}
        
        try:
            # Simplified transition probability matrix
            # In production, this would be learned from historical data
            
            transition_probs = {
                'BULLISH_HIGH_VOLATILE': {
                    'to_bullish_normal': 0.3,
                    'to_neutral': 0.2,
                    'to_bearish': 0.1,
                    'stay_same': 0.4
                },
                'BULLISH_NORMAL_VOLATILE': {
                    'to_bullish_high': 0.2,
                    'to_neutral': 0.3,
                    'to_bearish': 0.1,
                    'stay_same': 0.4
                },
                'BEARISH_HIGH_VOLATILE': {
                    'to_bearish_normal': 0.3,
                    'to_neutral': 0.2,
                    'to_bullish': 0.1,
                    'stay_same': 0.4
                },
                'BEARISH_NORMAL_VOLATILE': {
                    'to_bearish_high': 0.2,
                    'to_neutral': 0.3,
                    'to_bullish': 0.1,
                    'stay_same': 0.4
                }
            }
            
            # Get transition probabilities for current regime
            regime_transitions = transition_probs.get(current_regime, {
                'to_bullish_normal': 0.2,
                'to_neutral': 0.4,
                'to_bearish': 0.2,
                'stay_same': 0.2
            })
            
            # Add transition features
            for transition, prob in regime_transitions.items():
                features[f'transition_prob_{transition}'] = prob
            
            # Regime change probability
            features['regime_change_probability'] = 1 - regime_transitions.get('stay_same', 0.5)
            
        except Exception as e:
            logger.warning(f"⚠️ Regime transition calculation failed: {e}")
            features = {
                'transition_prob_to_bullish_normal': 0.2,
                'transition_prob_to_neutral': 0.4,
                'transition_prob_to_bearish': 0.2,
                'transition_prob_stay_same': 0.2,
                'regime_change_probability': 0.8
            }
        
        return features
    
    def _calculate_regime_indicators(self, market_context, regime_type: str) -> Dict[str, float]:
        """Calculate regime-specific indicators"""
        
        features = {}
        
        try:
            current_iv = getattr(market_context, 'current_iv', 0.2)
            current_price = getattr(market_context, 'current_price', 100.0)
            
            # Volatility regime indicators
            vol_thresholds = self.config['volatility_thresholds']
            
            if current_iv < vol_thresholds['low']:
                features['vol_regime_score'] = -1.0  # Low vol
            elif current_iv > vol_thresholds['high']:
                features['vol_regime_score'] = 1.0   # High vol
            else:
                features['vol_regime_score'] = 0.0   # Normal vol
            
            # Trend regime indicators
            vwap = getattr(market_context, 'vwap', current_price)
            features['trend_regime_score'] = (current_price - vwap) / vwap if vwap > 0 else 0.0
            
            # Regime strength
            regime_confidence = getattr(market_context, 'regime_confidence', 0.7)
            features['regime_strength'] = regime_confidence
            
            # Regime-specific risk factors
            if 'HIGH_VOLATILE' in regime_type:
                features['regime_risk_factor'] = 1.5  # Higher risk in high vol
            elif 'LOW_VOLATILE' in regime_type:
                features['regime_risk_factor'] = 0.7  # Lower risk in low vol
            else:
                features['regime_risk_factor'] = 1.0  # Normal risk
            
            # Regime-specific opportunity score
            if 'BULLISH' in regime_type:
                features['regime_opportunity_score'] = 0.8
            elif 'BEARISH' in regime_type:
                features['regime_opportunity_score'] = 0.6
            else:
                features['regime_opportunity_score'] = 0.5
            
        except Exception as e:
            logger.warning(f"⚠️ Regime indicators calculation failed: {e}")
            features = {
                'vol_regime_score': 0.0,
                'trend_regime_score': 0.0,
                'regime_strength': 0.7,
                'regime_risk_factor': 1.0,
                'regime_opportunity_score': 0.5
            }
        
        return features
    
    def _get_default_features(self) -> Dict[str, float]:
        """Get default features when calculation fails"""
        
        features = {
            'regime_confidence': 0.7,
            'volatility_stability': 0.7,
            'trend_stability': 0.7,
            'regime_stability': 0.7,
            'regime_persistence': 0.7,
            'vol_regime_score': 0.0,
            'trend_regime_score': 0.0,
            'regime_strength': 0.7,
            'regime_risk_factor': 1.0,
            'regime_opportunity_score': 0.5,
            'regime_change_probability': 0.3
        }
        
        # Add default 8-regime features
        regime_8_types = self.config['regime_types']['8_regime']
        for regime in regime_8_types:
            features[f'regime_8_{regime.lower()}'] = 0.0
        
        # Set default regime
        features['regime_8_neutral_normal_volatile'] = 1.0
        features['regime_8_neutral'] = 1.0
        features['regime_8_normal_vol'] = 1.0
        
        # Add default 18-regime features
        regime_18_types = self.config['regime_types']['18_regime']
        for regime in regime_18_types:
            features[f'regime_18_{regime.lower()}'] = 0.0
        
        # Set default regime
        features['regime_18_neutral_normal_volatile'] = 1.0
        
        return features

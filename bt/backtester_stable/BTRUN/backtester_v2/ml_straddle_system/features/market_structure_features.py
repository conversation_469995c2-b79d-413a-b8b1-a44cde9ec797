#!/usr/bin/env python3
"""
Market Structure Features for ML Straddle System
===============================================

Smart Money Concepts and market structure analysis:
- Order flow analysis
- Institutional activity detection
- Support/resistance levels
- Market microstructure indicators
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class MarketStructureFeatures:
    """Market Structure Feature Generator"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize Market Structure Features generator"""
        self.config = config or self._get_default_config()
        logger.info("🏗️ Market Structure Features generator initialized")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'order_flow_window': 20,
            'institutional_threshold': 2.0,  # Volume threshold for institutional activity
            'support_resistance_periods': [20, 50, 100],
            'smart_money_indicators': ['volume_profile', 'order_flow', 'institutional_flow']
        }
    
    async def generate_features(
        self, 
        market_context, 
        historical_data: Optional[pd.DataFrame] = None
    ) -> Dict[str, float]:
        """Generate market structure features"""
        
        features = {}
        
        try:
            # Basic market structure from context
            current_volume = getattr(market_context, 'current_volume', 1000)
            features['current_volume'] = current_volume
            
            # Order flow features
            if historical_data is not None:
                order_flow_features = self._calculate_order_flow_features(historical_data)
                features.update(order_flow_features)
            else:
                features.update(self._get_default_order_flow_features())
            
            # Institutional activity features
            institutional_features = self._calculate_institutional_features(current_volume, historical_data)
            features.update(institutional_features)
            
            # Support/resistance features
            current_price = getattr(market_context, 'current_price', 100.0)
            sr_features = self._calculate_support_resistance_features(current_price, historical_data)
            features.update(sr_features)
            
            # Smart money concepts
            smart_money_features = self._calculate_smart_money_features(market_context, historical_data)
            features.update(smart_money_features)
            
            # Volume profile features
            volume_profile_features = self._calculate_volume_profile_features(historical_data)
            features.update(volume_profile_features)
            
            logger.debug(f"🏗️ Generated {len(features)} market structure features")
            return features
            
        except Exception as e:
            logger.error(f"❌ Failed to generate market structure features: {e}")
            return self._get_default_features()
    
    def _calculate_order_flow_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate order flow features"""
        features = {}
        
        try:
            if df is None or len(df) < 5:
                return self._get_default_order_flow_features()
            
            # Calculate order flow imbalance (simplified)
            if 'volume' in df.columns and 'close' in df.columns:
                # Up volume vs down volume
                price_changes = df['close'].diff()
                up_volume = df['volume'][price_changes > 0].sum()
                down_volume = df['volume'][price_changes < 0].sum()
                total_volume = up_volume + down_volume
                
                if total_volume > 0:
                    features['order_flow_imbalance'] = (up_volume - down_volume) / total_volume
                    features['buy_pressure'] = up_volume / total_volume
                    features['sell_pressure'] = down_volume / total_volume
                else:
                    features['order_flow_imbalance'] = 0.0
                    features['buy_pressure'] = 0.5
                    features['sell_pressure'] = 0.5
            
            # Volume-weighted average price deviation
            if 'high' in df.columns and 'low' in df.columns and 'close' in df.columns and 'volume' in df.columns:
                typical_price = (df['high'] + df['low'] + df['close']) / 3
                vwap = (typical_price * df['volume']).sum() / df['volume'].sum()
                current_price = df['close'].iloc[-1]
                features['vwap_deviation'] = (current_price - vwap) / vwap if vwap > 0 else 0.0
            else:
                features['vwap_deviation'] = 0.0
            
            # Order flow momentum
            window = min(self.config['order_flow_window'], len(df))
            if window >= 5:
                recent_flow = features['order_flow_imbalance']
                past_flow = self._calculate_past_order_flow(df, window)
                features['order_flow_momentum'] = recent_flow - past_flow
            else:
                features['order_flow_momentum'] = 0.0
                
        except Exception as e:
            logger.warning(f"⚠️ Order flow calculation failed: {e}")
            features.update(self._get_default_order_flow_features())
        
        return features
    
    def _calculate_past_order_flow(self, df: pd.DataFrame, window: int) -> float:
        """Calculate past order flow for momentum calculation"""
        try:
            if len(df) < window + 5:
                return 0.0
            
            past_df = df.iloc[-(window+5):-5]  # Look back further
            price_changes = past_df['close'].diff()
            up_volume = past_df['volume'][price_changes > 0].sum()
            down_volume = past_df['volume'][price_changes < 0].sum()
            total_volume = up_volume + down_volume
            
            return (up_volume - down_volume) / total_volume if total_volume > 0 else 0.0
            
        except Exception:
            return 0.0
    
    def _get_default_order_flow_features(self) -> Dict[str, float]:
        """Get default order flow features"""
        return {
            'order_flow_imbalance': 0.0,
            'buy_pressure': 0.5,
            'sell_pressure': 0.5,
            'vwap_deviation': 0.0,
            'order_flow_momentum': 0.0
        }
    
    def _calculate_institutional_features(self, current_volume: float, df: Optional[pd.DataFrame]) -> Dict[str, float]:
        """Calculate institutional activity features"""
        features = {}
        
        try:
            # Volume-based institutional detection
            if df is not None and 'volume' in df.columns and len(df) >= 20:
                avg_volume = df['volume'].tail(20).mean()
                volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
                
                # Institutional activity threshold
                institutional_threshold = self.config['institutional_threshold']
                features['institutional_activity'] = 1.0 if volume_ratio > institutional_threshold else 0.0
                features['volume_ratio'] = volume_ratio
                
                # Large block detection (simplified)
                large_blocks = (df['volume'] > avg_volume * institutional_threshold).sum()
                features['large_block_ratio'] = large_blocks / len(df) if len(df) > 0 else 0.0
                
                # Institutional flow direction
                if 'close' in df.columns:
                    high_volume_days = df[df['volume'] > avg_volume * institutional_threshold]
                    if len(high_volume_days) > 0:
                        price_changes = high_volume_days['close'].diff().dropna()
                        if len(price_changes) > 0:
                            features['institutional_flow_direction'] = 1.0 if price_changes.mean() > 0 else -1.0
                        else:
                            features['institutional_flow_direction'] = 0.0
                    else:
                        features['institutional_flow_direction'] = 0.0
                else:
                    features['institutional_flow_direction'] = 0.0
            else:
                # Default values when no historical data
                features['institutional_activity'] = 0.0
                features['volume_ratio'] = 1.0
                features['large_block_ratio'] = 0.0
                features['institutional_flow_direction'] = 0.0
            
            # Smart money index (simplified)
            features['smart_money_index'] = (
                features['institutional_activity'] * 0.4 +
                min(features['volume_ratio'] / 3.0, 1.0) * 0.3 +
                features['large_block_ratio'] * 0.3
            )
            
        except Exception as e:
            logger.warning(f"⚠️ Institutional features calculation failed: {e}")
            features = {
                'institutional_activity': 0.0,
                'volume_ratio': 1.0,
                'large_block_ratio': 0.0,
                'institutional_flow_direction': 0.0,
                'smart_money_index': 0.0
            }
        
        return features
    
    def _calculate_support_resistance_features(self, current_price: float, df: Optional[pd.DataFrame]) -> Dict[str, float]:
        """Calculate support and resistance features"""
        features = {}
        
        try:
            if df is not None and 'high' in df.columns and 'low' in df.columns and len(df) >= 20:
                # Calculate support and resistance levels
                highs = df['high'].values
                lows = df['low'].values
                
                # Recent highs and lows
                recent_high = np.max(highs[-20:])
                recent_low = np.min(lows[-20:])
                
                # Distance to support/resistance
                features['distance_to_resistance'] = (recent_high - current_price) / current_price
                features['distance_to_support'] = (current_price - recent_low) / current_price
                
                # Support/resistance strength (simplified)
                resistance_touches = np.sum(highs >= recent_high * 0.99)  # Within 1% of resistance
                support_touches = np.sum(lows <= recent_low * 1.01)  # Within 1% of support
                
                features['resistance_strength'] = min(resistance_touches / len(df), 1.0)
                features['support_strength'] = min(support_touches / len(df), 1.0)
                
                # Position relative to range
                price_range = recent_high - recent_low
                if price_range > 0:
                    features['range_position'] = (current_price - recent_low) / price_range
                else:
                    features['range_position'] = 0.5
                
            else:
                # Default values
                features = {
                    'distance_to_resistance': 0.02,  # 2% to resistance
                    'distance_to_support': 0.02,    # 2% to support
                    'resistance_strength': 0.1,
                    'support_strength': 0.1,
                    'range_position': 0.5
                }
                
        except Exception as e:
            logger.warning(f"⚠️ Support/resistance calculation failed: {e}")
            features = {
                'distance_to_resistance': 0.02,
                'distance_to_support': 0.02,
                'resistance_strength': 0.1,
                'support_strength': 0.1,
                'range_position': 0.5
            }
        
        return features
    
    def _calculate_smart_money_features(self, market_context, df: Optional[pd.DataFrame]) -> Dict[str, float]:
        """Calculate Smart Money Concepts features"""
        features = {}
        
        try:
            # Market structure shift detection (simplified)
            if df is not None and len(df) >= 10:
                # Break of structure (BOS)
                recent_highs = df['high'].tail(5).max() if 'high' in df.columns else 100
                previous_highs = df['high'].iloc[-10:-5].max() if 'high' in df.columns else 100
                
                features['break_of_structure'] = 1.0 if recent_highs > previous_highs else 0.0
                
                # Change of character (CHoCH) - simplified
                recent_trend = 1.0 if df['close'].iloc[-1] > df['close'].iloc[-5] else -1.0
                previous_trend = 1.0 if df['close'].iloc[-5] > df['close'].iloc[-10] else -1.0
                
                features['change_of_character'] = 1.0 if recent_trend != previous_trend else 0.0
                
            else:
                features['break_of_structure'] = 0.0
                features['change_of_character'] = 0.0
            
            # Liquidity zones (simplified)
            current_price = getattr(market_context, 'current_price', 100.0)
            
            # Equal highs/lows (liquidity pools)
            features['liquidity_above'] = 0.1  # Simplified - 10% liquidity above
            features['liquidity_below'] = 0.1  # Simplified - 10% liquidity below
            
            # Fair value gaps (simplified)
            features['fair_value_gap'] = 0.0  # No gap detected
            
            # Order blocks (simplified)
            features['bullish_order_block'] = 0.0
            features['bearish_order_block'] = 0.0
            
        except Exception as e:
            logger.warning(f"⚠️ Smart money features calculation failed: {e}")
            features = {
                'break_of_structure': 0.0,
                'change_of_character': 0.0,
                'liquidity_above': 0.1,
                'liquidity_below': 0.1,
                'fair_value_gap': 0.0,
                'bullish_order_block': 0.0,
                'bearish_order_block': 0.0
            }
        
        return features
    
    def _calculate_volume_profile_features(self, df: Optional[pd.DataFrame]) -> Dict[str, float]:
        """Calculate volume profile features"""
        features = {}
        
        try:
            if df is not None and 'volume' in df.columns and 'close' in df.columns and len(df) >= 10:
                # Volume-weighted average price
                vwap = (df['close'] * df['volume']).sum() / df['volume'].sum()
                current_price = df['close'].iloc[-1]
                
                features['vwap_distance'] = (current_price - vwap) / vwap if vwap > 0 else 0.0
                
                # Volume at price levels (simplified)
                price_levels = np.linspace(df['close'].min(), df['close'].max(), 10)
                volume_at_levels = []
                
                for i in range(len(price_levels) - 1):
                    level_volume = df[(df['close'] >= price_levels[i]) & 
                                    (df['close'] < price_levels[i+1])]['volume'].sum()
                    volume_at_levels.append(level_volume)
                
                if volume_at_levels:
                    max_volume_level = np.argmax(volume_at_levels)
                    features['poc_distance'] = abs(max_volume_level - 5) / 5  # Distance from middle
                    features['volume_concentration'] = max(volume_at_levels) / sum(volume_at_levels)
                else:
                    features['poc_distance'] = 0.0
                    features['volume_concentration'] = 0.1
                
            else:
                features = {
                    'vwap_distance': 0.0,
                    'poc_distance': 0.0,
                    'volume_concentration': 0.1
                }
                
        except Exception as e:
            logger.warning(f"⚠️ Volume profile calculation failed: {e}")
            features = {
                'vwap_distance': 0.0,
                'poc_distance': 0.0,
                'volume_concentration': 0.1
            }
        
        return features
    
    def _get_default_features(self) -> Dict[str, float]:
        """Get default features when calculation fails"""
        features = {
            'current_volume': 1000.0,
            'order_flow_imbalance': 0.0,
            'buy_pressure': 0.5,
            'sell_pressure': 0.5,
            'vwap_deviation': 0.0,
            'order_flow_momentum': 0.0,
            'institutional_activity': 0.0,
            'volume_ratio': 1.0,
            'large_block_ratio': 0.0,
            'institutional_flow_direction': 0.0,
            'smart_money_index': 0.0,
            'distance_to_resistance': 0.02,
            'distance_to_support': 0.02,
            'resistance_strength': 0.1,
            'support_strength': 0.1,
            'range_position': 0.5,
            'break_of_structure': 0.0,
            'change_of_character': 0.0,
            'liquidity_above': 0.1,
            'liquidity_below': 0.1,
            'fair_value_gap': 0.0,
            'bullish_order_block': 0.0,
            'bearish_order_block': 0.0,
            'vwap_distance': 0.0,
            'poc_distance': 0.0,
            'volume_concentration': 0.1
        }
        
        return features

"""
ML Straddle System - Phase 2 Implementation
===========================================

Dedicated ML system for straddle strategies with advanced features:
- ATM/ITM/OTM straddle strategies
- Triple straddle optimization
- ML-enhanced position management
- Real-time volatility prediction
- Smart Money Concepts integration
"""

__version__ = "2.0.0"
__author__ = "Enhanced GPU Backtester Team"

# Core ML Straddle System Components
from .core.straddle_ml_engine import StraddleMLEngine
from .core.position_analyzer import PositionAnalyzer
from .core.volatility_predictor import VolatilityPredictor
from .core.risk_manager import RiskManager

# ML Models
from .models.atm_straddle_model import ATMStraddleModel
from .models.itm_straddle_model import ITMStraddleModel
from .models.otm_straddle_model import OTMStraddleModel
from .models.triple_straddle_model import TripleStraddleModel

# Feature Engineering
from .features.technical_features import TechnicalFeatures
from .features.volatility_features import VolatilityFeatures
from .features.market_structure_features import MarketStructureFeatures
from .features.regime_features import RegimeFeatures

# Strategy Implementations
from .strategies.ml_atm_straddle import MLATMStraddle
from .strategies.ml_itm_straddle import MLITMStraddle
from .strategies.ml_otm_straddle import MLOTMStraddle
from .strategies.ml_triple_straddle import MLTripleStraddle

__all__ = [
    # Core Components
    'StraddleMLEngine',
    'PositionAnalyzer', 
    'VolatilityPredictor',
    'RiskManager',
    
    # ML Models
    'ATMStraddleModel',
    'ITMStraddleModel',
    'OTMStraddleModel',
    'TripleStraddleModel',
    
    # Features
    'TechnicalFeatures',
    'VolatilityFeatures',
    'MarketStructureFeatures',
    'RegimeFeatures',
    
    # Strategies
    'MLATMStraddle',
    'MLITMStraddle',
    'MLOTMStraddle',
    'MLTripleStraddle'
]

#!/usr/bin/env python3
"""
Performance Optimizer
=====================

Optimizes strategy performance across multiple metrics:
- Sharpe Ratio
- Maximum Drawdown
- Win Rate
- Profit Factor
- Calmar Ratio
- Sortino Ratio
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Callable
from datetime import datetime

logger = logging.getLogger(__name__)

class PerformanceOptimizer:
    """
    Performance-focused optimization with multiple metrics
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize performance optimizer"""
        self.config = config or self._get_default_config()
        self.performance_history = []
        
        logger.info("📈 Performance Optimizer initialized")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'primary_metric': 'sharpe_ratio',
            'secondary_metrics': ['max_drawdown', 'win_rate', 'profit_factor'],
            'optimization_direction': 'maximize',  # or 'minimize'
            'metric_weights': {
                'sharpe_ratio': 0.4,
                'max_drawdown': 0.2,
                'win_rate': 0.2,
                'profit_factor': 0.2
            },
            'constraints': {
                'min_sharpe_ratio': 1.0,
                'max_drawdown': 0.2,
                'min_win_rate': 0.4
            }
        }
    
    async def optimize(
        self,
        config,
        objective_function: Callable,
        progress_callback: Optional[Callable] = None
    ):
        """
        Optimize strategy performance
        
        Args:
            config: Optimization configuration
            objective_function: Function that returns performance metrics
            progress_callback: Progress callback
            
        Returns:
            OptimizationResult
        """
        try:
            logger.info(f"📈 Starting performance optimization")
            
            # Create performance-aware objective function
            perf_objective = self._create_performance_objective(objective_function)
            
            # Use parameter optimizer with performance objective
            from .parameter_optimizer import ParameterOptimizer
            param_optimizer = ParameterOptimizer(self.config)
            
            # Run optimization
            result = await param_optimizer.optimize(
                config, perf_objective, progress_callback
            )
            
            # Enhance result with performance analysis
            enhanced_result = await self._enhance_result_with_performance_analysis(result)
            
            logger.info(f"✅ Performance optimization completed")
            return enhanced_result
            
        except Exception as e:
            logger.error(f"❌ Performance optimization failed: {e}")
            raise
    
    def _create_performance_objective(self, original_objective: Callable) -> Callable:
        """Create performance-aware objective function"""
        
        def performance_objective(parameters):
            try:
                # Get original result (should be performance metrics)
                result = original_objective(parameters)
                
                # Extract performance metrics
                if isinstance(result, dict):
                    metrics = result
                else:
                    # Assume single metric
                    metrics = {self.config['primary_metric']: result}
                
                # Calculate composite score
                composite_score = self._calculate_composite_score(metrics)
                
                # Apply constraints
                if not self._check_constraints(metrics):
                    composite_score *= 0.1  # Heavy penalty for constraint violation
                
                # Store in history
                self.performance_history.append({
                    'parameters': parameters,
                    'metrics': metrics,
                    'composite_score': composite_score,
                    'timestamp': datetime.now()
                })
                
                return composite_score
                
            except Exception as e:
                logger.warning(f"⚠️ Performance objective evaluation failed: {e}")
                return -1000  # Large negative score for failed evaluations
        
        return performance_objective
    
    def _calculate_composite_score(self, metrics: Dict[str, float]) -> float:
        """Calculate composite performance score"""
        
        weights = self.config['metric_weights']
        score = 0.0
        total_weight = 0.0
        
        for metric_name, weight in weights.items():
            if metric_name in metrics:
                metric_value = metrics[metric_name]
                
                # Normalize metric (different metrics have different scales)
                normalized_value = self._normalize_metric(metric_name, metric_value)
                
                score += normalized_value * weight
                total_weight += weight
        
        # Normalize by total weight
        if total_weight > 0:
            score /= total_weight
        
        return score
    
    def _normalize_metric(self, metric_name: str, value: float) -> float:
        """Normalize metric to 0-1 scale"""
        
        # Define normalization ranges for different metrics
        normalization_ranges = {
            'sharpe_ratio': (0, 3),      # 0 to 3 is excellent
            'max_drawdown': (0.5, 0),    # Lower is better (inverted)
            'win_rate': (0, 1),          # 0 to 1 (percentage)
            'profit_factor': (0, 3),     # 0 to 3 is excellent
            'calmar_ratio': (0, 2),      # 0 to 2 is excellent
            'sortino_ratio': (0, 3),     # 0 to 3 is excellent
            'return_rate': (0, 0.5),     # 0 to 50% annual return
            'volatility': (0.3, 0),      # Lower is better (inverted)
        }
        
        if metric_name not in normalization_ranges:
            # Default normalization
            return max(0, min(1, value))
        
        min_val, max_val = normalization_ranges[metric_name]
        
        if min_val > max_val:  # Inverted scale (lower is better)
            min_val, max_val = max_val, min_val
            value = min_val + max_val - value  # Invert value
        
        # Normalize to 0-1
        if max_val == min_val:
            return 0.5
        
        normalized = (value - min_val) / (max_val - min_val)
        return max(0, min(1, normalized))
    
    def _check_constraints(self, metrics: Dict[str, float]) -> bool:
        """Check if metrics satisfy constraints"""
        
        constraints = self.config.get('constraints', {})
        
        for constraint_name, constraint_value in constraints.items():
            if constraint_name.startswith('min_'):
                metric_name = constraint_name[4:]  # Remove 'min_' prefix
                if metric_name in metrics and metrics[metric_name] < constraint_value:
                    return False
            elif constraint_name.startswith('max_'):
                metric_name = constraint_name[4:]  # Remove 'max_' prefix
                if metric_name in metrics and metrics[metric_name] > constraint_value:
                    return False
        
        return True
    
    async def _enhance_result_with_performance_analysis(self, result):
        """Enhance optimization result with performance analysis"""
        
        if not self.performance_history:
            return result
        
        # Analyze performance evolution
        performance_evolution = self._analyze_performance_evolution()
        
        # Calculate performance statistics
        performance_stats = self._calculate_performance_statistics()
        
        # Add to result
        result.performance_analysis = {
            'evolution': performance_evolution,
            'statistics': performance_stats,
            'constraint_violations': self._count_constraint_violations(),
            'metric_correlations': self._calculate_metric_correlations()
        }
        
        return result
    
    def _analyze_performance_evolution(self) -> Dict[str, Any]:
        """Analyze how performance evolved during optimization"""
        
        if len(self.performance_history) < 2:
            return {}
        
        # Extract time series data
        timestamps = [entry['timestamp'] for entry in self.performance_history]
        scores = [entry['composite_score'] for entry in self.performance_history]
        
        # Calculate evolution metrics
        evolution = {
            'initial_score': scores[0],
            'final_score': scores[-1],
            'improvement': scores[-1] - scores[0],
            'best_score': max(scores),
            'worst_score': min(scores),
            'score_volatility': np.std(scores),
            'convergence_rate': self._calculate_convergence_rate(scores)
        }
        
        return evolution
    
    def _calculate_performance_statistics(self) -> Dict[str, Any]:
        """Calculate performance statistics across all evaluations"""
        
        if not self.performance_history:
            return {}
        
        # Extract all metrics
        all_metrics = {}
        for entry in self.performance_history:
            for metric_name, metric_value in entry['metrics'].items():
                if metric_name not in all_metrics:
                    all_metrics[metric_name] = []
                all_metrics[metric_name].append(metric_value)
        
        # Calculate statistics for each metric
        stats = {}
        for metric_name, values in all_metrics.items():
            stats[metric_name] = {
                'mean': np.mean(values),
                'std': np.std(values),
                'min': np.min(values),
                'max': np.max(values),
                'median': np.median(values),
                'q25': np.percentile(values, 25),
                'q75': np.percentile(values, 75)
            }
        
        return stats
    
    def _count_constraint_violations(self) -> Dict[str, int]:
        """Count constraint violations"""
        
        violations = {}
        constraints = self.config.get('constraints', {})
        
        for constraint_name in constraints.keys():
            violations[constraint_name] = 0
        
        for entry in self.performance_history:
            metrics = entry['metrics']
            if not self._check_constraints(metrics):
                for constraint_name, constraint_value in constraints.items():
                    if constraint_name.startswith('min_'):
                        metric_name = constraint_name[4:]
                        if metric_name in metrics and metrics[metric_name] < constraint_value:
                            violations[constraint_name] += 1
                    elif constraint_name.startswith('max_'):
                        metric_name = constraint_name[4:]
                        if metric_name in metrics and metrics[metric_name] > constraint_value:
                            violations[constraint_name] += 1
        
        return violations
    
    def _calculate_metric_correlations(self) -> Dict[str, Dict[str, float]]:
        """Calculate correlations between metrics"""
        
        if len(self.performance_history) < 10:
            return {}
        
        # Extract metrics data
        metrics_data = {}
        for entry in self.performance_history:
            for metric_name, metric_value in entry['metrics'].items():
                if metric_name not in metrics_data:
                    metrics_data[metric_name] = []
                metrics_data[metric_name].append(metric_value)
        
        # Calculate correlations
        correlations = {}
        metric_names = list(metrics_data.keys())
        
        for i, metric1 in enumerate(metric_names):
            correlations[metric1] = {}
            for j, metric2 in enumerate(metric_names):
                if i != j and len(metrics_data[metric1]) == len(metrics_data[metric2]):
                    try:
                        corr = np.corrcoef(metrics_data[metric1], metrics_data[metric2])[0, 1]
                        correlations[metric1][metric2] = float(corr) if not np.isnan(corr) else 0.0
                    except Exception:
                        correlations[metric1][metric2] = 0.0
        
        return correlations
    
    def _calculate_convergence_rate(self, scores: List[float]) -> float:
        """Calculate convergence rate"""
        
        if len(scores) < 10:
            return 0.0
        
        # Calculate moving average to smooth noise
        window_size = min(10, len(scores) // 4)
        moving_avg = []
        
        for i in range(window_size, len(scores)):
            avg = np.mean(scores[i-window_size:i])
            moving_avg.append(avg)
        
        if len(moving_avg) < 2:
            return 0.0
        
        # Calculate rate of improvement
        improvements = []
        for i in range(1, len(moving_avg)):
            improvement = moving_avg[i] - moving_avg[i-1]
            improvements.append(improvement)
        
        # Return average improvement rate
        return np.mean(improvements) if improvements else 0.0
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get summary of performance optimization"""
        
        if not self.performance_history:
            return {'status': 'no_data'}
        
        latest_entry = self.performance_history[-1]
        best_entry = max(self.performance_history, key=lambda x: x['composite_score'])
        
        return {
            'total_evaluations': len(self.performance_history),
            'latest_score': latest_entry['composite_score'],
            'best_score': best_entry['composite_score'],
            'best_parameters': best_entry['parameters'],
            'best_metrics': best_entry['metrics'],
            'performance_improvement': latest_entry['composite_score'] - self.performance_history[0]['composite_score'],
            'constraint_satisfaction_rate': sum(1 for entry in self.performance_history 
                                               if self._check_constraints(entry['metrics'])) / len(self.performance_history)
        }

#!/usr/bin/env python3
"""
Optimization Engine
==================

Core optimization engine that coordinates strategy optimization across multiple dimensions:
- Parameter optimization
- Performance optimization  
- Multi-objective optimization
- Parallel processing coordination
"""

import logging
import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Callable
from datetime import datetime
from dataclasses import dataclass
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import multiprocessing as mp

from .parameter_optimizer import ParameterOptimizer
from .performance_optimizer import PerformanceOptimizer
from .multi_objective_optimizer import MultiObjectiveOptimizer
from .parallel_optimizer import ParallelOptimizer

logger = logging.getLogger(__name__)

@dataclass
class OptimizationConfig:
    """Configuration for optimization runs"""
    strategy_type: str
    optimization_type: str  # 'parameter', 'performance', 'multi_objective'
    parallel_workers: int = 4
    max_iterations: int = 100
    convergence_threshold: float = 0.001
    objectives: List[str] = None
    constraints: Dict[str, Any] = None
    parameter_bounds: Dict[str, Tuple[float, float]] = None

@dataclass
class OptimizationResult:
    """Results from optimization run"""
    best_parameters: Dict[str, Any]
    best_performance: Dict[str, float]
    optimization_history: List[Dict[str, Any]]
    convergence_metrics: Dict[str, float]
    execution_time: float
    iterations_completed: int
    status: str

class OptimizationEngine:
    """
    Main optimization engine that coordinates all optimization activities
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize optimization engine"""
        self.config = config or self._get_default_config()
        
        # Initialize sub-optimizers
        self.parameter_optimizer = ParameterOptimizer(self.config.get('parameter_optimizer', {}))
        self.performance_optimizer = PerformanceOptimizer(self.config.get('performance_optimizer', {}))
        self.multi_objective_optimizer = MultiObjectiveOptimizer(self.config.get('multi_objective_optimizer', {}))
        self.parallel_optimizer = ParallelOptimizer(self.config.get('parallel_optimizer', {}))
        
        # Optimization state
        self.active_optimizations = {}
        self.optimization_history = []
        self.performance_cache = {}
        
        logger.info("🔧 Optimization Engine initialized")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'optimization_engine': {
                'max_concurrent_optimizations': 4,
                'cache_results': True,
                'save_intermediate_results': True,
                'result_storage_path': '/srv/samba/shared/bt/backtester_stable/BTRUN/output/optimization'
            },
            'parameter_optimizer': {
                'algorithm': 'bayesian',  # 'grid', 'random', 'bayesian', 'genetic'
                'max_evaluations': 100,
                'acquisition_function': 'expected_improvement'
            },
            'performance_optimizer': {
                'primary_metric': 'sharpe_ratio',
                'secondary_metrics': ['max_drawdown', 'win_rate', 'profit_factor'],
                'optimization_direction': 'maximize'
            },
            'multi_objective_optimizer': {
                'algorithm': 'nsga2',
                'population_size': 50,
                'generations': 100
            },
            'parallel_optimizer': {
                'max_workers': mp.cpu_count(),
                'chunk_size': 10,
                'load_balancing': True
            }
        }
    
    async def optimize_strategy(
        self,
        strategy_config: OptimizationConfig,
        objective_function: Callable,
        progress_callback: Optional[Callable] = None
    ) -> OptimizationResult:
        """
        Main optimization entry point
        
        Args:
            strategy_config: Configuration for the optimization
            objective_function: Function to optimize (strategy backtesting)
            progress_callback: Optional callback for progress updates
            
        Returns:
            OptimizationResult with best parameters and performance
        """
        try:
            optimization_id = f"opt_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            logger.info(f"🚀 Starting optimization {optimization_id} for {strategy_config.strategy_type}")
            
            start_time = datetime.now()
            
            # Add to active optimizations
            self.active_optimizations[optimization_id] = {
                'config': strategy_config,
                'start_time': start_time,
                'status': 'running',
                'progress': 0.0
            }
            
            # Route to appropriate optimizer
            if strategy_config.optimization_type == 'parameter':
                result = await self._optimize_parameters(
                    strategy_config, objective_function, progress_callback
                )
            elif strategy_config.optimization_type == 'performance':
                result = await self._optimize_performance(
                    strategy_config, objective_function, progress_callback
                )
            elif strategy_config.optimization_type == 'multi_objective':
                result = await self._optimize_multi_objective(
                    strategy_config, objective_function, progress_callback
                )
            else:
                raise ValueError(f"Unknown optimization type: {strategy_config.optimization_type}")
            
            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds()
            result.execution_time = execution_time
            
            # Update status
            self.active_optimizations[optimization_id]['status'] = 'completed'
            self.active_optimizations[optimization_id]['result'] = result
            
            # Add to history
            self.optimization_history.append({
                'id': optimization_id,
                'config': strategy_config,
                'result': result,
                'timestamp': datetime.now()
            })
            
            logger.info(f"✅ Optimization {optimization_id} completed in {execution_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"❌ Optimization failed: {e}")
            if optimization_id in self.active_optimizations:
                self.active_optimizations[optimization_id]['status'] = 'failed'
                self.active_optimizations[optimization_id]['error'] = str(e)
            raise
    
    async def _optimize_parameters(
        self,
        config: OptimizationConfig,
        objective_function: Callable,
        progress_callback: Optional[Callable] = None
    ) -> OptimizationResult:
        """Optimize strategy parameters"""
        
        logger.info(f"🔧 Starting parameter optimization for {config.strategy_type}")
        
        # Use parallel optimizer if multiple workers specified
        if config.parallel_workers > 1:
            return await self.parallel_optimizer.optimize_parameters_parallel(
                config, objective_function, progress_callback
            )
        else:
            return await self.parameter_optimizer.optimize(
                config, objective_function, progress_callback
            )
    
    async def _optimize_performance(
        self,
        config: OptimizationConfig,
        objective_function: Callable,
        progress_callback: Optional[Callable] = None
    ) -> OptimizationResult:
        """Optimize strategy performance"""
        
        logger.info(f"📈 Starting performance optimization for {config.strategy_type}")
        
        return await self.performance_optimizer.optimize(
            config, objective_function, progress_callback
        )
    
    async def _optimize_multi_objective(
        self,
        config: OptimizationConfig,
        objective_function: Callable,
        progress_callback: Optional[Callable] = None
    ) -> OptimizationResult:
        """Multi-objective optimization"""
        
        logger.info(f"🎯 Starting multi-objective optimization for {config.strategy_type}")
        
        return await self.multi_objective_optimizer.optimize(
            config, objective_function, progress_callback
        )
    
    async def optimize_portfolio(
        self,
        strategies: List[OptimizationConfig],
        portfolio_objective: Callable,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, OptimizationResult]:
        """
        Optimize multiple strategies as a portfolio
        
        Args:
            strategies: List of strategy configurations
            portfolio_objective: Portfolio-level objective function
            progress_callback: Progress callback
            
        Returns:
            Dictionary of optimization results by strategy
        """
        try:
            logger.info(f"📊 Starting portfolio optimization with {len(strategies)} strategies")
            
            # Run optimizations in parallel
            tasks = []
            for strategy_config in strategies:
                task = self.optimize_strategy(
                    strategy_config,
                    portfolio_objective,
                    progress_callback
                )
                tasks.append(task)
            
            # Wait for all optimizations to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            portfolio_results = {}
            for i, (strategy_config, result) in enumerate(zip(strategies, results)):
                if isinstance(result, Exception):
                    logger.error(f"❌ Strategy {strategy_config.strategy_type} optimization failed: {result}")
                    portfolio_results[strategy_config.strategy_type] = None
                else:
                    portfolio_results[strategy_config.strategy_type] = result
            
            logger.info(f"✅ Portfolio optimization completed")
            return portfolio_results
            
        except Exception as e:
            logger.error(f"❌ Portfolio optimization failed: {e}")
            raise
    
    def get_optimization_status(self, optimization_id: Optional[str] = None) -> Dict[str, Any]:
        """Get status of optimization(s)"""
        if optimization_id:
            return self.active_optimizations.get(optimization_id, {})
        else:
            return {
                'active_optimizations': len(self.active_optimizations),
                'total_completed': len(self.optimization_history),
                'active_details': self.active_optimizations
            }
    
    def get_optimization_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get optimization history"""
        return self.optimization_history[-limit:]
    
    async def cancel_optimization(self, optimization_id: str) -> bool:
        """Cancel running optimization"""
        if optimization_id in self.active_optimizations:
            self.active_optimizations[optimization_id]['status'] = 'cancelled'
            logger.info(f"🛑 Optimization {optimization_id} cancelled")
            return True
        return False
    
    def clear_cache(self):
        """Clear performance cache"""
        self.performance_cache.clear()
        logger.info("🧹 Performance cache cleared")
    
    async def benchmark_optimization_methods(
        self,
        test_function: Callable,
        parameter_bounds: Dict[str, Tuple[float, float]],
        iterations: int = 50
    ) -> Dict[str, Dict[str, float]]:
        """
        Benchmark different optimization methods
        
        Args:
            test_function: Test function to optimize
            parameter_bounds: Parameter bounds for testing
            iterations: Number of iterations per method
            
        Returns:
            Benchmark results by method
        """
        try:
            logger.info("🏁 Starting optimization method benchmark")
            
            methods = ['grid', 'random', 'bayesian', 'genetic']
            results = {}
            
            for method in methods:
                logger.info(f"Testing {method} optimization...")
                
                config = OptimizationConfig(
                    strategy_type='benchmark',
                    optimization_type='parameter',
                    max_iterations=iterations,
                    parameter_bounds=parameter_bounds
                )
                
                # Override algorithm
                self.parameter_optimizer.config['algorithm'] = method
                
                start_time = datetime.now()
                result = await self.parameter_optimizer.optimize(
                    config, test_function
                )
                execution_time = (datetime.now() - start_time).total_seconds()
                
                results[method] = {
                    'best_performance': result.best_performance,
                    'execution_time': execution_time,
                    'iterations': result.iterations_completed,
                    'convergence': result.convergence_metrics
                }
            
            logger.info("✅ Optimization benchmark completed")
            return results
            
        except Exception as e:
            logger.error(f"❌ Benchmark failed: {e}")
            raise

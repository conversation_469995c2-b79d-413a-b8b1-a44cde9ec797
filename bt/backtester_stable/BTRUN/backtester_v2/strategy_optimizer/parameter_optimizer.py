#!/usr/bin/env python3
"""
Parameter Optimizer
==================

Advanced parameter optimization using multiple algorithms:
- Grid Search
- Random Search  
- Bayesian Optimization
- Genetic Algorithm
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Callable
from datetime import datetime
import itertools
import random
from scipy.optimize import minimize
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import Matern

logger = logging.getLogger(__name__)

class ParameterOptimizer:
    """
    Parameter optimization using various algorithms
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize parameter optimizer"""
        self.config = config or self._get_default_config()
        self.optimization_history = []
        self.best_parameters = None
        self.best_performance = None
        
        logger.info("🔧 Parameter Optimizer initialized")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'algorithm': 'bayesian',  # 'grid', 'random', 'bayesian', 'genetic'
            'max_evaluations': 100,
            'acquisition_function': 'expected_improvement',
            'exploration_ratio': 0.1,
            'convergence_threshold': 0.001,
            'random_seed': 42
        }
    
    async def optimize(
        self,
        config,
        objective_function: Callable,
        progress_callback: Optional[Callable] = None
    ):
        """
        Main optimization entry point
        
        Args:
            config: Optimization configuration
            objective_function: Function to optimize
            progress_callback: Progress callback
            
        Returns:
            OptimizationResult
        """
        try:
            algorithm = self.config['algorithm']
            logger.info(f"🚀 Starting {algorithm} parameter optimization")
            
            if algorithm == 'grid':
                return await self._grid_search(config, objective_function, progress_callback)
            elif algorithm == 'random':
                return await self._random_search(config, objective_function, progress_callback)
            elif algorithm == 'bayesian':
                return await self._bayesian_optimization(config, objective_function, progress_callback)
            elif algorithm == 'genetic':
                return await self._genetic_algorithm(config, objective_function, progress_callback)
            else:
                raise ValueError(f"Unknown algorithm: {algorithm}")
                
        except Exception as e:
            logger.error(f"❌ Parameter optimization failed: {e}")
            raise
    
    async def _grid_search(
        self,
        config,
        objective_function: Callable,
        progress_callback: Optional[Callable] = None
    ):
        """Grid search optimization"""
        
        logger.info("🔍 Running grid search optimization")
        
        if not config.parameter_bounds:
            raise ValueError("Parameter bounds required for grid search")
        
        # Generate parameter grid
        param_grid = self._generate_parameter_grid(config.parameter_bounds)
        
        best_params = None
        best_score = float('-inf')
        results = []
        
        total_combinations = len(param_grid)
        
        for i, params in enumerate(param_grid):
            try:
                # Evaluate parameters
                score = objective_function(params)
                
                # Track result
                result = {
                    'parameters': params,
                    'score': score,
                    'iteration': i + 1
                }
                results.append(result)
                
                # Update best
                if score > best_score:
                    best_score = score
                    best_params = params.copy()
                
                # Progress callback
                if progress_callback:
                    await progress_callback({
                        'progress': (i + 1) / total_combinations,
                        'iteration': i + 1,
                        'total_iterations': total_combinations,
                        'current_score': score,
                        'best_score': best_score,
                        'current_params': params
                    })
                
                logger.debug(f"Grid search iteration {i+1}/{total_combinations}: score={score:.4f}")
                
            except Exception as e:
                logger.warning(f"⚠️ Grid search iteration {i+1} failed: {e}")
                continue
        
        # Create result
        from .optimization_engine import OptimizationResult
        
        return OptimizationResult(
            best_parameters=best_params,
            best_performance={'objective_value': best_score},
            optimization_history=results,
            convergence_metrics={'final_score': best_score},
            execution_time=0.0,
            iterations_completed=len(results),
            status='completed'
        )
    
    async def _random_search(
        self,
        config,
        objective_function: Callable,
        progress_callback: Optional[Callable] = None
    ):
        """Random search optimization"""
        
        logger.info("🎲 Running random search optimization")
        
        if not config.parameter_bounds:
            raise ValueError("Parameter bounds required for random search")
        
        max_evaluations = self.config['max_evaluations']
        best_params = None
        best_score = float('-inf')
        results = []
        
        # Set random seed for reproducibility
        random.seed(self.config.get('random_seed', 42))
        np.random.seed(self.config.get('random_seed', 42))
        
        for i in range(max_evaluations):
            try:
                # Generate random parameters
                params = self._generate_random_parameters(config.parameter_bounds)
                
                # Evaluate parameters
                score = objective_function(params)
                
                # Track result
                result = {
                    'parameters': params,
                    'score': score,
                    'iteration': i + 1
                }
                results.append(result)
                
                # Update best
                if score > best_score:
                    best_score = score
                    best_params = params.copy()
                
                # Progress callback
                if progress_callback:
                    await progress_callback({
                        'progress': (i + 1) / max_evaluations,
                        'iteration': i + 1,
                        'total_iterations': max_evaluations,
                        'current_score': score,
                        'best_score': best_score,
                        'current_params': params
                    })
                
                logger.debug(f"Random search iteration {i+1}/{max_evaluations}: score={score:.4f}")
                
            except Exception as e:
                logger.warning(f"⚠️ Random search iteration {i+1} failed: {e}")
                continue
        
        # Create result
        from .optimization_engine import OptimizationResult
        
        return OptimizationResult(
            best_parameters=best_params,
            best_performance={'objective_value': best_score},
            optimization_history=results,
            convergence_metrics={'final_score': best_score},
            execution_time=0.0,
            iterations_completed=len(results),
            status='completed'
        )
    
    async def _bayesian_optimization(
        self,
        config,
        objective_function: Callable,
        progress_callback: Optional[Callable] = None
    ):
        """Bayesian optimization using Gaussian Process"""
        
        logger.info("🧠 Running Bayesian optimization")
        
        if not config.parameter_bounds:
            raise ValueError("Parameter bounds required for Bayesian optimization")
        
        # Initialize with random samples
        n_initial = min(10, self.config['max_evaluations'] // 4)
        X_samples = []
        y_samples = []
        
        param_names = list(config.parameter_bounds.keys())
        param_bounds = [config.parameter_bounds[name] for name in param_names]
        
        # Initial random sampling
        for i in range(n_initial):
            params = self._generate_random_parameters(config.parameter_bounds)
            score = objective_function(params)
            
            # Convert to array format
            x = [params[name] for name in param_names]
            X_samples.append(x)
            y_samples.append(score)
        
        X_samples = np.array(X_samples)
        y_samples = np.array(y_samples)
        
        # Initialize Gaussian Process
        kernel = Matern(length_scale=1.0, nu=2.5)
        gp = GaussianProcessRegressor(kernel=kernel, alpha=1e-6, normalize_y=True)
        
        best_params = None
        best_score = np.max(y_samples)
        best_idx = np.argmax(y_samples)
        best_params = dict(zip(param_names, X_samples[best_idx]))
        
        results = []
        
        # Bayesian optimization loop
        for i in range(n_initial, self.config['max_evaluations']):
            try:
                # Fit GP to current data
                gp.fit(X_samples, y_samples)
                
                # Find next point using acquisition function
                next_x = self._acquisition_function(gp, param_bounds, X_samples)
                next_params = dict(zip(param_names, next_x))
                
                # Evaluate next point
                score = objective_function(next_params)
                
                # Add to samples
                X_samples = np.vstack([X_samples, next_x])
                y_samples = np.append(y_samples, score)
                
                # Track result
                result = {
                    'parameters': next_params,
                    'score': score,
                    'iteration': i + 1
                }
                results.append(result)
                
                # Update best
                if score > best_score:
                    best_score = score
                    best_params = next_params.copy()
                
                # Progress callback
                if progress_callback:
                    await progress_callback({
                        'progress': (i + 1) / self.config['max_evaluations'],
                        'iteration': i + 1,
                        'total_iterations': self.config['max_evaluations'],
                        'current_score': score,
                        'best_score': best_score,
                        'current_params': next_params
                    })
                
                logger.debug(f"Bayesian optimization iteration {i+1}: score={score:.4f}")
                
            except Exception as e:
                logger.warning(f"⚠️ Bayesian optimization iteration {i+1} failed: {e}")
                continue
        
        # Create result
        from .optimization_engine import OptimizationResult
        
        return OptimizationResult(
            best_parameters=best_params,
            best_performance={'objective_value': best_score},
            optimization_history=results,
            convergence_metrics={'final_score': best_score},
            execution_time=0.0,
            iterations_completed=len(results),
            status='completed'
        )
    
    async def _genetic_algorithm(
        self,
        config,
        objective_function: Callable,
        progress_callback: Optional[Callable] = None
    ):
        """Genetic algorithm optimization"""
        
        logger.info("🧬 Running genetic algorithm optimization")
        
        if not config.parameter_bounds:
            raise ValueError("Parameter bounds required for genetic algorithm")
        
        # GA parameters
        population_size = 50
        generations = self.config['max_evaluations'] // population_size
        mutation_rate = 0.1
        crossover_rate = 0.8
        
        param_names = list(config.parameter_bounds.keys())
        param_bounds = [config.parameter_bounds[name] for name in param_names]
        
        # Initialize population
        population = []
        for _ in range(population_size):
            individual = self._generate_random_parameters(config.parameter_bounds)
            population.append(individual)
        
        best_params = None
        best_score = float('-inf')
        results = []
        
        for generation in range(generations):
            try:
                # Evaluate population
                scores = []
                for individual in population:
                    score = objective_function(individual)
                    scores.append(score)
                    
                    # Track result
                    result = {
                        'parameters': individual,
                        'score': score,
                        'generation': generation + 1
                    }
                    results.append(result)
                    
                    # Update best
                    if score > best_score:
                        best_score = score
                        best_params = individual.copy()
                
                # Selection, crossover, mutation
                population = self._evolve_population(
                    population, scores, param_bounds, 
                    crossover_rate, mutation_rate
                )
                
                # Progress callback
                if progress_callback:
                    await progress_callback({
                        'progress': (generation + 1) / generations,
                        'generation': generation + 1,
                        'total_generations': generations,
                        'best_score': best_score,
                        'population_size': len(population)
                    })
                
                logger.debug(f"GA generation {generation+1}/{generations}: best_score={best_score:.4f}")
                
            except Exception as e:
                logger.warning(f"⚠️ GA generation {generation+1} failed: {e}")
                continue
        
        # Create result
        from .optimization_engine import OptimizationResult
        
        return OptimizationResult(
            best_parameters=best_params,
            best_performance={'objective_value': best_score},
            optimization_history=results,
            convergence_metrics={'final_score': best_score},
            execution_time=0.0,
            iterations_completed=len(results),
            status='completed'
        )
    
    def _generate_parameter_grid(self, parameter_bounds: Dict[str, Tuple[float, float]]) -> List[Dict[str, Any]]:
        """Generate parameter grid for grid search"""
        
        param_ranges = {}
        for param_name, (min_val, max_val) in parameter_bounds.items():
            if isinstance(min_val, int) and isinstance(max_val, int):
                # Integer parameters
                param_ranges[param_name] = list(range(min_val, max_val + 1))
            else:
                # Float parameters - use 10 points
                param_ranges[param_name] = np.linspace(min_val, max_val, 10).tolist()
        
        # Generate all combinations
        param_names = list(param_ranges.keys())
        param_values = [param_ranges[name] for name in param_names]
        
        combinations = []
        for combination in itertools.product(*param_values):
            param_dict = dict(zip(param_names, combination))
            combinations.append(param_dict)
        
        return combinations
    
    def _generate_random_parameters(self, parameter_bounds: Dict[str, Tuple[float, float]]) -> Dict[str, Any]:
        """Generate random parameters within bounds"""
        
        params = {}
        for param_name, (min_val, max_val) in parameter_bounds.items():
            if isinstance(min_val, int) and isinstance(max_val, int):
                # Integer parameters
                params[param_name] = random.randint(min_val, max_val)
            else:
                # Float parameters
                params[param_name] = random.uniform(min_val, max_val)
        
        return params
    
    def _acquisition_function(self, gp, param_bounds, X_samples):
        """Expected Improvement acquisition function"""
        
        def expected_improvement(x):
            x = np.array(x).reshape(1, -1)
            mu, sigma = gp.predict(x, return_std=True)
            
            if sigma == 0:
                return 0
            
            # Current best
            f_best = np.max(gp.y_train_)
            
            # Expected improvement
            z = (mu - f_best) / sigma
            ei = (mu - f_best) * self._normal_cdf(z) + sigma * self._normal_pdf(z)
            
            return -ei[0]  # Negative for minimization
        
        # Optimize acquisition function
        best_x = None
        best_ei = float('inf')
        
        # Try multiple random starts
        for _ in range(10):
            x0 = [random.uniform(bounds[0], bounds[1]) for bounds in param_bounds]
            
            try:
                result = minimize(
                    expected_improvement,
                    x0,
                    bounds=param_bounds,
                    method='L-BFGS-B'
                )
                
                if result.fun < best_ei:
                    best_ei = result.fun
                    best_x = result.x
                    
            except Exception:
                continue
        
        return best_x if best_x is not None else x0
    
    def _normal_cdf(self, x):
        """Standard normal CDF approximation"""
        return 0.5 * (1 + np.sign(x) * np.sqrt(1 - np.exp(-2 * x**2 / np.pi)))
    
    def _normal_pdf(self, x):
        """Standard normal PDF"""
        return np.exp(-0.5 * x**2) / np.sqrt(2 * np.pi)
    
    def _evolve_population(self, population, scores, param_bounds, crossover_rate, mutation_rate):
        """Evolve population using selection, crossover, and mutation"""
        
        # Selection (tournament selection)
        new_population = []
        population_size = len(population)
        
        for _ in range(population_size):
            # Tournament selection
            tournament_size = 3
            tournament_indices = random.sample(range(population_size), tournament_size)
            tournament_scores = [scores[i] for i in tournament_indices]
            winner_idx = tournament_indices[np.argmax(tournament_scores)]
            new_population.append(population[winner_idx].copy())
        
        # Crossover and mutation
        for i in range(0, population_size - 1, 2):
            parent1 = new_population[i]
            parent2 = new_population[i + 1]
            
            # Crossover
            if random.random() < crossover_rate:
                child1, child2 = self._crossover(parent1, parent2)
                new_population[i] = child1
                new_population[i + 1] = child2
            
            # Mutation
            if random.random() < mutation_rate:
                new_population[i] = self._mutate(new_population[i], param_bounds)
            if random.random() < mutation_rate:
                new_population[i + 1] = self._mutate(new_population[i + 1], param_bounds)
        
        return new_population
    
    def _crossover(self, parent1, parent2):
        """Single-point crossover"""
        child1 = parent1.copy()
        child2 = parent2.copy()
        
        param_names = list(parent1.keys())
        crossover_point = random.randint(1, len(param_names) - 1)
        
        for i in range(crossover_point, len(param_names)):
            param_name = param_names[i]
            child1[param_name] = parent2[param_name]
            child2[param_name] = parent1[param_name]
        
        return child1, child2
    
    def _mutate(self, individual, param_bounds):
        """Gaussian mutation"""
        mutated = individual.copy()
        
        for param_name in individual.keys():
            if random.random() < 0.1:  # 10% chance per parameter
                min_val, max_val = param_bounds[list(individual.keys()).index(param_name)]
                
                if isinstance(individual[param_name], int):
                    # Integer mutation
                    mutation = random.randint(-1, 1)
                    mutated[param_name] = max(min_val, min(max_val, individual[param_name] + mutation))
                else:
                    # Float mutation
                    mutation = random.gauss(0, (max_val - min_val) * 0.1)
                    mutated[param_name] = max(min_val, min(max_val, individual[param_name] + mutation))
        
        return mutated

#!/usr/bin/env python3
"""
Parallel Optimizer
==================

Advanced parallel processing system for strategy optimization:
- Multi-process parameter optimization
- Distributed backtesting
- Load balancing and resource management
- Real-time progress tracking
"""

import logging
import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Callable
from datetime import datetime
from dataclasses import dataclass
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
import multiprocessing as mp
import queue
import threading
import time

logger = logging.getLogger(__name__)

@dataclass
class ParallelTask:
    """Represents a parallel optimization task"""
    task_id: str
    strategy_type: str
    parameters: Dict[str, Any]
    priority: int = 1
    created_at: datetime = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Optional[Any] = None
    error: Optional[str] = None
    status: str = 'pending'  # pending, running, completed, failed

@dataclass
class WorkerStatus:
    """Status of a parallel worker"""
    worker_id: str
    status: str  # idle, busy, error
    current_task: Optional[str] = None
    tasks_completed: int = 0
    total_execution_time: float = 0.0
    last_activity: Optional[datetime] = None

class ParallelOptimizer:
    """
    Advanced parallel optimization system with load balancing and progress tracking
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize parallel optimizer"""
        self.config = config or self._get_default_config()
        
        # Worker management
        self.max_workers = self.config.get('max_workers', mp.cpu_count())
        self.workers = {}
        self.task_queue = queue.PriorityQueue()
        self.result_queue = queue.Queue()
        
        # Task tracking
        self.active_tasks = {}
        self.completed_tasks = {}
        self.task_counter = 0
        
        # Performance monitoring
        self.performance_metrics = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'average_execution_time': 0.0,
            'throughput_per_minute': 0.0,
            'worker_utilization': 0.0
        }
        
        # Thread management
        self.coordinator_thread = None
        self.monitor_thread = None
        self.is_running = False
        
        logger.info(f"⚡ Parallel Optimizer initialized with {self.max_workers} workers")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'max_workers': mp.cpu_count(),
            'chunk_size': 10,
            'load_balancing': True,
            'adaptive_scheduling': True,
            'worker_timeout': 300,  # 5 minutes
            'result_caching': True,
            'progress_update_interval': 5,  # seconds
            'resource_monitoring': True
        }
    
    async def optimize_parameters_parallel(
        self,
        config,
        objective_function: Callable,
        progress_callback: Optional[Callable] = None
    ):
        """
        Run parameter optimization in parallel
        
        Args:
            config: Optimization configuration
            objective_function: Function to optimize
            progress_callback: Progress update callback
            
        Returns:
            OptimizationResult with best parameters
        """
        try:
            logger.info(f"🚀 Starting parallel parameter optimization")
            
            # Start parallel processing system
            await self._start_parallel_system()
            
            # Generate parameter combinations
            parameter_combinations = self._generate_parameter_combinations(config)
            
            logger.info(f"📊 Generated {len(parameter_combinations)} parameter combinations")
            
            # Submit tasks
            task_ids = []
            for i, params in enumerate(parameter_combinations):
                task_id = await self._submit_task(
                    strategy_type=config.strategy_type,
                    parameters=params,
                    objective_function=objective_function,
                    priority=1
                )
                task_ids.append(task_id)
            
            # Monitor progress and collect results
            results = await self._collect_results(
                task_ids, progress_callback
            )
            
            # Find best result
            best_result = self._find_best_result(results, config)
            
            # Stop parallel system
            await self._stop_parallel_system()
            
            logger.info(f"✅ Parallel optimization completed")
            return best_result
            
        except Exception as e:
            logger.error(f"❌ Parallel optimization failed: {e}")
            await self._stop_parallel_system()
            raise
    
    async def _start_parallel_system(self):
        """Start the parallel processing system"""
        if self.is_running:
            return
        
        self.is_running = True
        
        # Start worker processes
        self.process_pool = ProcessPoolExecutor(max_workers=self.max_workers)
        
        # Start coordinator thread
        self.coordinator_thread = threading.Thread(
            target=self._task_coordinator,
            daemon=True
        )
        self.coordinator_thread.start()
        
        # Start monitoring thread
        self.monitor_thread = threading.Thread(
            target=self._performance_monitor,
            daemon=True
        )
        self.monitor_thread.start()
        
        logger.info(f"⚡ Parallel system started with {self.max_workers} workers")
    
    async def _stop_parallel_system(self):
        """Stop the parallel processing system"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # Shutdown process pool
        if hasattr(self, 'process_pool'):
            self.process_pool.shutdown(wait=True)
        
        # Wait for threads to finish
        if self.coordinator_thread and self.coordinator_thread.is_alive():
            self.coordinator_thread.join(timeout=5)
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        logger.info("🛑 Parallel system stopped")
    
    def _generate_parameter_combinations(self, config) -> List[Dict[str, Any]]:
        """Generate parameter combinations for optimization"""
        
        if not config.parameter_bounds:
            return [{}]  # No parameters to optimize
        
        combinations = []
        
        # Grid search approach for parallel optimization
        param_names = list(config.parameter_bounds.keys())
        param_ranges = []
        
        for param_name, (min_val, max_val) in config.parameter_bounds.items():
            # Generate 10 values per parameter for grid search
            if isinstance(min_val, int) and isinstance(max_val, int):
                values = list(range(min_val, max_val + 1, max(1, (max_val - min_val) // 10)))
            else:
                values = np.linspace(min_val, max_val, 10).tolist()
            param_ranges.append(values)
        
        # Generate all combinations
        import itertools
        for combination in itertools.product(*param_ranges):
            param_dict = dict(zip(param_names, combination))
            combinations.append(param_dict)
        
        # Limit to max_iterations if specified
        if hasattr(config, 'max_iterations') and config.max_iterations:
            combinations = combinations[:config.max_iterations]
        
        return combinations
    
    async def _submit_task(
        self,
        strategy_type: str,
        parameters: Dict[str, Any],
        objective_function: Callable,
        priority: int = 1
    ) -> str:
        """Submit a task for parallel execution"""
        
        self.task_counter += 1
        task_id = f"task_{self.task_counter:06d}"
        
        task = ParallelTask(
            task_id=task_id,
            strategy_type=strategy_type,
            parameters=parameters,
            priority=priority,
            created_at=datetime.now()
        )
        
        # Add to active tasks
        self.active_tasks[task_id] = task
        
        # Submit to process pool
        future = self.process_pool.submit(
            self._execute_task_wrapper,
            task_id,
            strategy_type,
            parameters,
            objective_function
        )
        
        # Store future for tracking
        task.future = future
        
        self.performance_metrics['total_tasks'] += 1
        
        logger.debug(f"📤 Submitted task {task_id} for {strategy_type}")
        return task_id
    
    def _execute_task_wrapper(
        self,
        task_id: str,
        strategy_type: str,
        parameters: Dict[str, Any],
        objective_function: Callable
    ) -> Dict[str, Any]:
        """Wrapper for task execution in worker process"""
        try:
            start_time = time.time()
            
            # Execute objective function
            result = objective_function(parameters)
            
            execution_time = time.time() - start_time
            
            return {
                'task_id': task_id,
                'status': 'completed',
                'result': result,
                'execution_time': execution_time,
                'parameters': parameters
            }
            
        except Exception as e:
            return {
                'task_id': task_id,
                'status': 'failed',
                'error': str(e),
                'parameters': parameters
            }
    
    async def _collect_results(
        self,
        task_ids: List[str],
        progress_callback: Optional[Callable] = None
    ) -> List[Dict[str, Any]]:
        """Collect results from parallel tasks"""
        
        results = []
        completed_count = 0
        total_tasks = len(task_ids)
        
        # Wait for all tasks to complete
        for task_id in task_ids:
            if task_id not in self.active_tasks:
                continue
            
            task = self.active_tasks[task_id]
            
            try:
                # Wait for task completion
                result = task.future.result(timeout=self.config.get('worker_timeout', 300))
                
                # Update task status
                task.status = result['status']
                task.completed_at = datetime.now()
                task.result = result
                
                if result['status'] == 'completed':
                    results.append(result)
                    self.performance_metrics['completed_tasks'] += 1
                else:
                    self.performance_metrics['failed_tasks'] += 1
                    logger.warning(f"⚠️ Task {task_id} failed: {result.get('error', 'Unknown error')}")
                
                # Move to completed tasks
                self.completed_tasks[task_id] = task
                del self.active_tasks[task_id]
                
                completed_count += 1
                
                # Update progress
                if progress_callback:
                    progress = completed_count / total_tasks
                    await progress_callback({
                        'progress': progress,
                        'completed': completed_count,
                        'total': total_tasks,
                        'current_task': task_id
                    })
                
            except Exception as e:
                logger.error(f"❌ Task {task_id} execution failed: {e}")
                task.status = 'failed'
                task.error = str(e)
                task.completed_at = datetime.now()
                
                self.performance_metrics['failed_tasks'] += 1
                self.completed_tasks[task_id] = task
                del self.active_tasks[task_id]
        
        logger.info(f"📊 Collected {len(results)} successful results out of {total_tasks} tasks")
        return results
    
    def _find_best_result(self, results: List[Dict[str, Any]], config) -> Any:
        """Find the best result from parallel optimization"""
        
        if not results:
            raise ValueError("No successful results to evaluate")
        
        # Extract performance metrics
        performances = []
        for result in results:
            if 'result' in result and result['result']:
                # Assume result contains performance metrics
                perf = result['result']
                if isinstance(perf, dict) and 'sharpe_ratio' in perf:
                    performances.append(perf['sharpe_ratio'])
                elif isinstance(perf, (int, float)):
                    performances.append(perf)
                else:
                    performances.append(0.0)
            else:
                performances.append(0.0)
        
        # Find best performance
        best_idx = np.argmax(performances)
        best_result = results[best_idx]
        
        # Create optimization result
        from .optimization_engine import OptimizationResult
        
        return OptimizationResult(
            best_parameters=best_result['parameters'],
            best_performance={'objective_value': performances[best_idx]},
            optimization_history=results,
            convergence_metrics={'final_value': performances[best_idx]},
            execution_time=0.0,  # Will be set by calling function
            iterations_completed=len(results),
            status='completed'
        )
    
    def _task_coordinator(self):
        """Coordinate task execution (runs in separate thread)"""
        while self.is_running:
            try:
                # Update worker utilization
                active_workers = sum(1 for task in self.active_tasks.values() 
                                   if hasattr(task, 'future') and not task.future.done())
                
                self.performance_metrics['worker_utilization'] = active_workers / self.max_workers
                
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"❌ Task coordinator error: {e}")
                time.sleep(5)
    
    def _performance_monitor(self):
        """Monitor performance metrics (runs in separate thread)"""
        last_completed = 0
        last_time = time.time()
        
        while self.is_running:
            try:
                current_time = time.time()
                current_completed = self.performance_metrics['completed_tasks']
                
                # Calculate throughput
                time_diff = current_time - last_time
                if time_diff >= 60:  # Update every minute
                    completed_diff = current_completed - last_completed
                    self.performance_metrics['throughput_per_minute'] = completed_diff / (time_diff / 60)
                    
                    last_completed = current_completed
                    last_time = current_time
                
                # Calculate average execution time
                if self.completed_tasks:
                    total_time = sum(
                        (task.completed_at - task.started_at).total_seconds()
                        for task in self.completed_tasks.values()
                        if task.started_at and task.completed_at
                    )
                    self.performance_metrics['average_execution_time'] = total_time / len(self.completed_tasks)
                
                time.sleep(self.config.get('progress_update_interval', 5))
                
            except Exception as e:
                logger.error(f"❌ Performance monitor error: {e}")
                time.sleep(10)
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        return {
            **self.performance_metrics,
            'active_tasks': len(self.active_tasks),
            'completed_tasks_count': len(self.completed_tasks),
            'is_running': self.is_running,
            'max_workers': self.max_workers
        }
    
    def get_task_status(self, task_id: Optional[str] = None) -> Dict[str, Any]:
        """Get status of specific task or all tasks"""
        if task_id:
            if task_id in self.active_tasks:
                return {'status': 'active', 'task': self.active_tasks[task_id]}
            elif task_id in self.completed_tasks:
                return {'status': 'completed', 'task': self.completed_tasks[task_id]}
            else:
                return {'status': 'not_found'}
        else:
            return {
                'active_tasks': len(self.active_tasks),
                'completed_tasks': len(self.completed_tasks),
                'total_tasks': self.performance_metrics['total_tasks']
            }

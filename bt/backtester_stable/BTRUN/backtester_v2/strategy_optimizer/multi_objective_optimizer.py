#!/usr/bin/env python3
"""
Multi-Objective Optimizer
=========================

Multi-objective optimization using NSGA-II and other algorithms:
- Pareto frontier optimization
- Multiple conflicting objectives
- Trade-off analysis
- Solution ranking
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Callable
from datetime import datetime
import random

logger = logging.getLogger(__name__)

class MultiObjectiveOptimizer:
    """
    Multi-objective optimization using NSGA-II algorithm
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize multi-objective optimizer"""
        self.config = config or self._get_default_config()
        self.pareto_front = []
        self.optimization_history = []
        
        logger.info("🎯 Multi-Objective Optimizer initialized")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'algorithm': 'nsga2',
            'population_size': 50,
            'generations': 100,
            'crossover_probability': 0.8,
            'mutation_probability': 0.1,
            'objectives': ['sharpe_ratio', 'max_drawdown', 'win_rate'],
            'objective_directions': ['maximize', 'minimize', 'maximize'],  # Direction for each objective
            'constraint_tolerance': 0.01
        }
    
    async def optimize(
        self,
        config,
        objective_function: Callable,
        progress_callback: Optional[Callable] = None
    ):
        """
        Multi-objective optimization
        
        Args:
            config: Optimization configuration
            objective_function: Function that returns multiple objectives
            progress_callback: Progress callback
            
        Returns:
            OptimizationResult with Pareto front
        """
        try:
            logger.info(f"🎯 Starting multi-objective optimization")
            
            if self.config['algorithm'] == 'nsga2':
                result = await self._nsga2_optimization(
                    config, objective_function, progress_callback
                )
            else:
                raise ValueError(f"Unknown multi-objective algorithm: {self.config['algorithm']}")
            
            logger.info(f"✅ Multi-objective optimization completed")
            return result
            
        except Exception as e:
            logger.error(f"❌ Multi-objective optimization failed: {e}")
            raise
    
    async def _nsga2_optimization(
        self,
        config,
        objective_function: Callable,
        progress_callback: Optional[Callable] = None
    ):
        """NSGA-II multi-objective optimization"""
        
        logger.info("🧬 Running NSGA-II optimization")
        
        if not config.parameter_bounds:
            raise ValueError("Parameter bounds required for NSGA-II")
        
        population_size = self.config['population_size']
        generations = self.config['generations']
        
        # Initialize population
        population = self._initialize_population(config.parameter_bounds, population_size)
        
        # Evaluate initial population
        population = await self._evaluate_population(population, objective_function)
        
        best_solutions = []
        
        for generation in range(generations):
            try:
                # Non-dominated sorting
                fronts = self._non_dominated_sorting(population)
                
                # Calculate crowding distance
                for front in fronts:
                    self._calculate_crowding_distance(front)
                
                # Create new population
                new_population = []
                
                # Selection
                parents = self._tournament_selection(population, population_size)
                
                # Crossover and mutation
                offspring = []
                for i in range(0, len(parents) - 1, 2):
                    parent1 = parents[i]
                    parent2 = parents[i + 1]
                    
                    # Crossover
                    if random.random() < self.config['crossover_probability']:
                        child1, child2 = self._crossover(parent1, parent2, config.parameter_bounds)
                    else:
                        child1, child2 = parent1.copy(), parent2.copy()
                    
                    # Mutation
                    if random.random() < self.config['mutation_probability']:
                        child1 = self._mutate(child1, config.parameter_bounds)
                    if random.random() < self.config['mutation_probability']:
                        child2 = self._mutate(child2, config.parameter_bounds)
                    
                    offspring.extend([child1, child2])
                
                # Evaluate offspring
                offspring = await self._evaluate_population(offspring, objective_function)
                
                # Combine parent and offspring populations
                combined_population = population + offspring
                
                # Environmental selection
                population = self._environmental_selection(combined_population, population_size)
                
                # Update Pareto front
                fronts = self._non_dominated_sorting(population)
                if fronts:
                    self.pareto_front = fronts[0]  # First front is Pareto optimal
                
                # Track best solutions
                if self.pareto_front:
                    best_solutions.extend(self.pareto_front)
                
                # Progress callback
                if progress_callback:
                    await progress_callback({
                        'progress': (generation + 1) / generations,
                        'generation': generation + 1,
                        'total_generations': generations,
                        'pareto_front_size': len(self.pareto_front) if self.pareto_front else 0,
                        'population_size': len(population)
                    })
                
                logger.debug(f"NSGA-II generation {generation+1}/{generations}: "
                           f"Pareto front size = {len(self.pareto_front) if self.pareto_front else 0}")
                
            except Exception as e:
                logger.warning(f"⚠️ NSGA-II generation {generation+1} failed: {e}")
                continue
        
        # Create result
        from .optimization_engine import OptimizationResult
        
        # Select best solution from Pareto front (using first objective)
        if self.pareto_front:
            best_solution = max(self.pareto_front, key=lambda x: x['objectives'][0])
            best_parameters = best_solution['parameters']
            best_performance = dict(zip(self.config['objectives'], best_solution['objectives']))
        else:
            best_parameters = {}
            best_performance = {}
        
        return OptimizationResult(
            best_parameters=best_parameters,
            best_performance=best_performance,
            optimization_history=best_solutions,
            convergence_metrics={'pareto_front_size': len(self.pareto_front)},
            execution_time=0.0,
            iterations_completed=generations,
            status='completed'
        )
    
    def _initialize_population(self, parameter_bounds: Dict[str, Tuple[float, float]], size: int) -> List[Dict[str, Any]]:
        """Initialize random population"""
        
        population = []
        for _ in range(size):
            individual = {}
            for param_name, (min_val, max_val) in parameter_bounds.items():
                if isinstance(min_val, int) and isinstance(max_val, int):
                    individual[param_name] = random.randint(min_val, max_val)
                else:
                    individual[param_name] = random.uniform(min_val, max_val)
            
            population.append({
                'parameters': individual,
                'objectives': None,
                'rank': None,
                'crowding_distance': 0.0
            })
        
        return population
    
    async def _evaluate_population(self, population: List[Dict[str, Any]], objective_function: Callable) -> List[Dict[str, Any]]:
        """Evaluate population objectives"""
        
        for individual in population:
            if individual['objectives'] is None:
                try:
                    # Get objectives from function
                    result = objective_function(individual['parameters'])
                    
                    if isinstance(result, dict):
                        # Extract objectives in order
                        objectives = []
                        for obj_name in self.config['objectives']:
                            if obj_name in result:
                                objectives.append(result[obj_name])
                            else:
                                objectives.append(0.0)
                    elif isinstance(result, (list, tuple)):
                        objectives = list(result)
                    else:
                        objectives = [result]
                    
                    # Apply objective directions (convert minimize to maximize)
                    for i, direction in enumerate(self.config['objective_directions']):
                        if direction == 'minimize' and i < len(objectives):
                            objectives[i] = -objectives[i]
                    
                    individual['objectives'] = objectives
                    
                except Exception as e:
                    logger.warning(f"⚠️ Objective evaluation failed: {e}")
                    individual['objectives'] = [0.0] * len(self.config['objectives'])
        
        return population
    
    def _non_dominated_sorting(self, population: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """Non-dominated sorting (NSGA-II)"""
        
        fronts = []
        
        for individual in population:
            individual['domination_count'] = 0
            individual['dominated_solutions'] = []
        
        # Find first front
        first_front = []
        for i, individual_i in enumerate(population):
            for j, individual_j in enumerate(population):
                if i != j:
                    if self._dominates(individual_i, individual_j):
                        individual_i['dominated_solutions'].append(individual_j)
                    elif self._dominates(individual_j, individual_i):
                        individual_i['domination_count'] += 1
            
            if individual_i['domination_count'] == 0:
                individual_i['rank'] = 0
                first_front.append(individual_i)
        
        fronts.append(first_front)
        
        # Find subsequent fronts
        front_index = 0
        while fronts[front_index]:
            next_front = []
            for individual in fronts[front_index]:
                for dominated_individual in individual['dominated_solutions']:
                    dominated_individual['domination_count'] -= 1
                    if dominated_individual['domination_count'] == 0:
                        dominated_individual['rank'] = front_index + 1
                        next_front.append(dominated_individual)
            
            if next_front:
                fronts.append(next_front)
            front_index += 1
        
        return fronts
    
    def _dominates(self, individual_a: Dict[str, Any], individual_b: Dict[str, Any]) -> bool:
        """Check if individual A dominates individual B"""
        
        objectives_a = individual_a['objectives']
        objectives_b = individual_b['objectives']
        
        if not objectives_a or not objectives_b:
            return False
        
        # A dominates B if A is at least as good in all objectives and better in at least one
        at_least_as_good = all(a >= b for a, b in zip(objectives_a, objectives_b))
        better_in_one = any(a > b for a, b in zip(objectives_a, objectives_b))
        
        return at_least_as_good and better_in_one
    
    def _calculate_crowding_distance(self, front: List[Dict[str, Any]]):
        """Calculate crowding distance for individuals in a front"""
        
        if len(front) <= 2:
            for individual in front:
                individual['crowding_distance'] = float('inf')
            return
        
        # Initialize distances
        for individual in front:
            individual['crowding_distance'] = 0.0
        
        num_objectives = len(self.config['objectives'])
        
        for obj_index in range(num_objectives):
            # Sort by objective
            front.sort(key=lambda x: x['objectives'][obj_index])
            
            # Set boundary points to infinity
            front[0]['crowding_distance'] = float('inf')
            front[-1]['crowding_distance'] = float('inf')
            
            # Calculate distances for intermediate points
            obj_min = front[0]['objectives'][obj_index]
            obj_max = front[-1]['objectives'][obj_index]
            
            if obj_max - obj_min > 0:
                for i in range(1, len(front) - 1):
                    distance = (front[i + 1]['objectives'][obj_index] - 
                              front[i - 1]['objectives'][obj_index]) / (obj_max - obj_min)
                    front[i]['crowding_distance'] += distance
    
    def _tournament_selection(self, population: List[Dict[str, Any]], size: int) -> List[Dict[str, Any]]:
        """Tournament selection for NSGA-II"""
        
        selected = []
        
        for _ in range(size):
            # Tournament of size 2
            candidate1 = random.choice(population)
            candidate2 = random.choice(population)
            
            # Select based on rank and crowding distance
            if candidate1['rank'] < candidate2['rank']:
                selected.append(candidate1.copy())
            elif candidate1['rank'] > candidate2['rank']:
                selected.append(candidate2.copy())
            else:
                # Same rank, select based on crowding distance
                if candidate1['crowding_distance'] > candidate2['crowding_distance']:
                    selected.append(candidate1.copy())
                else:
                    selected.append(candidate2.copy())
        
        return selected
    
    def _crossover(self, parent1: Dict[str, Any], parent2: Dict[str, Any], parameter_bounds: Dict[str, Tuple[float, float]]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """Simulated binary crossover (SBX)"""
        
        child1 = {'parameters': {}, 'objectives': None, 'rank': None, 'crowding_distance': 0.0}
        child2 = {'parameters': {}, 'objectives': None, 'rank': None, 'crowding_distance': 0.0}
        
        eta_c = 20  # Distribution index for crossover
        
        for param_name in parent1['parameters'].keys():
            p1_val = parent1['parameters'][param_name]
            p2_val = parent2['parameters'][param_name]
            
            min_val, max_val = parameter_bounds[param_name]
            
            if random.random() <= 0.5:
                if abs(p1_val - p2_val) > 1e-14:
                    if p1_val > p2_val:
                        y1, y2 = p2_val, p1_val
                    else:
                        y1, y2 = p1_val, p2_val
                    
                    # Calculate beta
                    rand = random.random()
                    
                    beta1 = 1 + (2 * (y1 - min_val) / (y2 - y1))
                    beta2 = 1 + (2 * (max_val - y2) / (y2 - y1))
                    
                    alpha1 = 2 - beta1 ** (-(eta_c + 1))
                    alpha2 = 2 - beta2 ** (-(eta_c + 1))
                    
                    if rand <= 1 / alpha1:
                        betaq1 = (rand * alpha1) ** (1 / (eta_c + 1))
                    else:
                        betaq1 = (1 / (2 - rand * alpha1)) ** (1 / (eta_c + 1))
                    
                    if rand <= 1 / alpha2:
                        betaq2 = (rand * alpha2) ** (1 / (eta_c + 1))
                    else:
                        betaq2 = (1 / (2 - rand * alpha2)) ** (1 / (eta_c + 1))
                    
                    c1 = 0.5 * ((y1 + y2) - betaq1 * (y2 - y1))
                    c2 = 0.5 * ((y1 + y2) + betaq2 * (y2 - y1))
                    
                    # Ensure bounds
                    c1 = max(min_val, min(max_val, c1))
                    c2 = max(min_val, min(max_val, c2))
                    
                    child1['parameters'][param_name] = c1
                    child2['parameters'][param_name] = c2
                else:
                    child1['parameters'][param_name] = p1_val
                    child2['parameters'][param_name] = p2_val
            else:
                child1['parameters'][param_name] = p1_val
                child2['parameters'][param_name] = p2_val
        
        return child1, child2
    
    def _mutate(self, individual: Dict[str, Any], parameter_bounds: Dict[str, Tuple[float, float]]) -> Dict[str, Any]:
        """Polynomial mutation"""
        
        mutated = {
            'parameters': individual['parameters'].copy(),
            'objectives': None,
            'rank': None,
            'crowding_distance': 0.0
        }
        
        eta_m = 20  # Distribution index for mutation
        
        for param_name in mutated['parameters'].keys():
            if random.random() <= 1.0 / len(mutated['parameters']):  # Mutation probability per parameter
                min_val, max_val = parameter_bounds[param_name]
                val = mutated['parameters'][param_name]
                
                delta1 = (val - min_val) / (max_val - min_val)
                delta2 = (max_val - val) / (max_val - min_val)
                
                rand = random.random()
                mut_pow = 1.0 / (eta_m + 1.0)
                
                if rand <= 0.5:
                    xy = 1.0 - delta1
                    val_new = xy ** mut_pow - 1.0
                    deltaq = val_new * 2.0 * rand + (1.0 - 2.0 * rand) * (xy ** mut_pow)
                else:
                    xy = 1.0 - delta2
                    val_new = xy ** mut_pow - 1.0
                    deltaq = val_new * 2.0 * (1.0 - rand) + (2.0 * rand - 1.0) * (xy ** mut_pow)
                
                val_new = val + deltaq * (max_val - min_val)
                val_new = max(min_val, min(max_val, val_new))
                
                mutated['parameters'][param_name] = val_new
        
        return mutated
    
    def _environmental_selection(self, population: List[Dict[str, Any]], size: int) -> List[Dict[str, Any]]:
        """Environmental selection for NSGA-II"""
        
        # Non-dominated sorting
        fronts = self._non_dominated_sorting(population)
        
        new_population = []
        front_index = 0
        
        # Add complete fronts
        while front_index < len(fronts) and len(new_population) + len(fronts[front_index]) <= size:
            self._calculate_crowding_distance(fronts[front_index])
            new_population.extend(fronts[front_index])
            front_index += 1
        
        # Add partial front if needed
        if front_index < len(fronts) and len(new_population) < size:
            remaining_slots = size - len(new_population)
            self._calculate_crowding_distance(fronts[front_index])
            
            # Sort by crowding distance (descending)
            fronts[front_index].sort(key=lambda x: x['crowding_distance'], reverse=True)
            new_population.extend(fronts[front_index][:remaining_slots])
        
        return new_population
    
    def get_pareto_front(self) -> List[Dict[str, Any]]:
        """Get current Pareto front"""
        return self.pareto_front.copy() if self.pareto_front else []
    
    def analyze_trade_offs(self) -> Dict[str, Any]:
        """Analyze trade-offs in Pareto front"""
        
        if not self.pareto_front:
            return {'status': 'no_pareto_front'}
        
        objectives = [sol['objectives'] for sol in self.pareto_front]
        objectives_array = np.array(objectives)
        
        analysis = {
            'pareto_front_size': len(self.pareto_front),
            'objective_ranges': {},
            'objective_correlations': {},
            'diversity_metrics': {}
        }
        
        # Calculate objective ranges
        for i, obj_name in enumerate(self.config['objectives']):
            obj_values = objectives_array[:, i]
            analysis['objective_ranges'][obj_name] = {
                'min': float(np.min(obj_values)),
                'max': float(np.max(obj_values)),
                'mean': float(np.mean(obj_values)),
                'std': float(np.std(obj_values))
            }
        
        # Calculate objective correlations
        if len(objectives) > 1:
            corr_matrix = np.corrcoef(objectives_array.T)
            for i, obj1 in enumerate(self.config['objectives']):
                analysis['objective_correlations'][obj1] = {}
                for j, obj2 in enumerate(self.config['objectives']):
                    if i != j:
                        analysis['objective_correlations'][obj1][obj2] = float(corr_matrix[i, j])
        
        # Calculate diversity metrics
        if len(objectives) > 1:
            # Hypervolume (simplified)
            analysis['diversity_metrics']['hypervolume'] = self._calculate_hypervolume(objectives_array)
            
            # Spread
            analysis['diversity_metrics']['spread'] = self._calculate_spread(objectives_array)
        
        return analysis
    
    def _calculate_hypervolume(self, objectives: np.ndarray) -> float:
        """Calculate hypervolume (simplified 2D case)"""
        
        if objectives.shape[1] != 2:
            return 0.0  # Only implemented for 2D
        
        # Sort by first objective
        sorted_indices = np.argsort(objectives[:, 0])
        sorted_objectives = objectives[sorted_indices]
        
        hypervolume = 0.0
        for i in range(len(sorted_objectives)):
            if i == 0:
                width = sorted_objectives[i, 0]
            else:
                width = sorted_objectives[i, 0] - sorted_objectives[i-1, 0]
            
            height = sorted_objectives[i, 1]
            hypervolume += width * height
        
        return hypervolume
    
    def _calculate_spread(self, objectives: np.ndarray) -> float:
        """Calculate spread metric"""
        
        if len(objectives) < 2:
            return 0.0
        
        # Calculate distances between consecutive solutions
        distances = []
        for i in range(len(objectives) - 1):
            dist = np.linalg.norm(objectives[i+1] - objectives[i])
            distances.append(dist)
        
        if not distances:
            return 0.0
        
        mean_distance = np.mean(distances)
        spread = np.sum(np.abs(np.array(distances) - mean_distance))
        
        return spread / (len(distances) * mean_distance) if mean_distance > 0 else 0.0

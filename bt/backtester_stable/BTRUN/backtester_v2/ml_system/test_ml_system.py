#!/usr/bin/env python3
"""
ML System Test
==============

Test script to verify the ML system implementation works correctly.
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime

# Add the backtester_v2 directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
backtester_v2_dir = os.path.dirname(current_dir)
sys.path.insert(0, backtester_v2_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_core_infrastructure():
    """Test core ML infrastructure"""
    print("\n🧪 Testing Core ML Infrastructure...")
    
    try:
        # Test ML System Core
        from core_infrastructure.ml_system_core import MLSystemCore
        ml_system = MLSystemCore()
        
        print(f"✅ ML System Core initialized")
        print(f"   Status: {ml_system.get_status()}")
        
        # Test Feature Store
        from core_infrastructure.feature_store import FeatureStore
        feature_store = FeatureStore()
        
        # Create sample market data
        sample_data = pd.DataFrame({
            'close': [100, 101, 102, 101, 103],
            'high': [101, 102, 103, 102, 104],
            'low': [99, 100, 101, 100, 102],
            'open': [100, 101, 102, 101, 103],
            'volume': [1000, 1100, 1200, 1050, 1300]
        })
        
        # Extract features
        features = feature_store.extract_features(sample_data)
        print(f"✅ Feature Store working - Extracted {len(features)} feature types")
        
        # Test Model Server
        from core_infrastructure.model_server import ModelServer
        model_server = ModelServer()
        print(f"✅ Model Server initialized")
        
        # Test Performance Tracker
        from core_infrastructure.performance_tracker import PerformanceTracker
        perf_tracker = PerformanceTracker()
        
        # Record a test prediction
        perf_tracker.record_prediction(
            component="test_component",
            latency_ms=50.0,
            success=True,
            cache_hit=False,
            accuracy=0.85
        )
        
        stats = perf_tracker.get_component_stats("test_component")
        print(f"✅ Performance Tracker working - Stats: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Core infrastructure test failed: {e}")
        return False


def test_ml_indicator_enhancement():
    """Test ML_INDICATOR enhancement"""
    print("\n🧪 Testing ML_INDICATOR Enhancement...")
    
    try:
        # Test ML_INDICATOR Enhancer
        from ml_indicator_enhancement.ml_indicator_enhancer import MLIndicatorEnhancer
        enhancer = MLIndicatorEnhancer()
        
        # Create sample data
        sample_data = {
            'base_signals': {
                'signals': [
                    {'strength': 0.7, 'direction': 'BUY'},
                    {'strength': 0.5, 'direction': 'HOLD'}
                ],
                'indicators': [0.6, 0.8, 0.4],
                'features': ['rsi', 'macd', 'sma']
            },
            'market_data': pd.DataFrame({
                'close': [100, 101, 102],
                'volume': [1000, 1100, 1200]
            }),
            'regime_context': {
                'regime_type': 'BULLISH_NORMAL_VOLATILE',
                'confidence': 0.8,
                'alignment_score': 0.7
            }
        }
        
        # Test enhancement
        enhanced_result = enhancer.enhance_signals(sample_data)
        
        if enhanced_result:
            print(f"✅ ML_INDICATOR Enhancement working")
            print(f"   Confidence: {enhanced_result.get('confidence_score', 0):.3f}")
            print(f"   Regime Alignment: {enhanced_result.get('regime_alignment', 0):.3f}")
        else:
            print(f"⚠️  ML_INDICATOR Enhancement returned None")
        
        # Test Advanced Ensemble
        from ml_indicator_enhancement.advanced_ensemble import AdvancedEnsembleEngine
        ensemble = AdvancedEnsembleEngine()
        
        ensemble_result = ensemble.predict(
            features={'features': {'rsi': 0.6, 'macd': 0.8}},
            base_signals=sample_data['base_signals']
        )
        
        print(f"✅ Advanced Ensemble working")
        print(f"   Prediction: {ensemble_result.get('prediction', [])}")
        print(f"   Confidence: {ensemble_result.get('confidence', 0):.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ ML_INDICATOR enhancement test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_ml_system_integration():
    """Test ML system integration"""
    print("\n🧪 Testing ML System Integration...")
    
    try:
        # Test main ML system
        from core_infrastructure.ml_system_core import get_ml_system
        ml_system = get_ml_system()
        
        # Test ML_INDICATOR enhancement through main system
        test_data = {
            'base_signals': {
                'signals': [{'strength': 0.8, 'direction': 'BUY'}],
                'indicators': [0.7, 0.6, 0.9]
            },
            'market_data': pd.DataFrame({
                'close': [100, 101, 102, 103],
                'volume': [1000, 1100, 1200, 1300]
            }),
            'regime_context': {
                'regime_type': 'BULLISH_HIGH_VOLATILE',
                'confidence': 0.9
            }
        }
        
        # Get ML insights
        insights = ml_system.get_ml_insights("ML_INDICATOR_ENHANCEMENT", test_data)
        
        if insights:
            print(f"✅ ML System Integration working")
            print(f"   Insights received: {list(insights.keys())}")
        else:
            print(f"⚠️  No ML insights returned")
        
        # Test performance stats
        stats = ml_system.get_performance_stats()
        print(f"✅ Performance tracking: {stats}")
        
        # Test system status
        status = ml_system.get_status()
        print(f"✅ System status: Healthy={status['healthy']}, Enabled={status['enabled']}")
        
        return True
        
    except Exception as e:
        print(f"❌ ML system integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all ML system tests"""
    print("🚀 Starting ML System Tests...")
    print("=" * 50)
    
    test_results = []
    
    # Test core infrastructure
    test_results.append(test_core_infrastructure())
    
    # Test ML_INDICATOR enhancement
    test_results.append(test_ml_indicator_enhancement())
    
    # Test system integration
    test_results.append(test_ml_system_integration())
    
    # Summary
    print("\n" + "=" * 50)
    print("🏁 Test Results Summary:")
    
    passed = sum(test_results)
    total = len(test_results)
    
    if passed == total:
        print(f"✅ All tests passed! ({passed}/{total})")
        print("🎉 ML System is ready for deployment!")
    else:
        print(f"⚠️  Some tests failed: {passed}/{total} passed")
        print("🔧 Please check the failed components")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Signal Confidence Scorer
=========================

Advanced confidence scoring for ML_INDICATOR signals.
Provides multi-dimensional confidence analysis.
"""

import logging
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime
import statistics

logger = logging.getLogger(__name__)


class SignalConfidenceScorer:
    """
    Advanced signal confidence scoring
    
    Provides:
    1. Multi-factor confidence analysis
    2. Regime-aware confidence adjustments
    3. Historical confidence tracking
    4. Ensemble confidence aggregation
    """
    
    def __init__(self):
        """Initialize signal confidence scorer"""
        self.confidence_history = []
        self.confidence_factors = {
            'signal_strength': 0.3,
            'feature_quality': 0.25,
            'regime_alignment': 0.2,
            'ensemble_agreement': 0.15,
            'historical_performance': 0.1
        }
        
        logger.info("✅ Signal Confidence Scorer initialized")
    
    def score_signals(self, 
                     ensemble_prediction: Dict[str, Any],
                     regime_context: Dict[str, Any],
                     selected_features: Dict[str, Any]) -> float:
        """
        Calculate comprehensive confidence score for signals
        
        Args:
            ensemble_prediction: Ensemble prediction results
            regime_context: Market regime context
            selected_features: Selected features with importance
            
        Returns:
            Confidence score between 0 and 1
        """
        try:
            # Calculate individual confidence components
            signal_strength_conf = self._calculate_signal_strength_confidence(ensemble_prediction)
            feature_quality_conf = self._calculate_feature_quality_confidence(selected_features)
            regime_alignment_conf = self._calculate_regime_alignment_confidence(regime_context)
            ensemble_agreement_conf = self._calculate_ensemble_agreement_confidence(ensemble_prediction)
            historical_performance_conf = self._calculate_historical_performance_confidence()
            
            # Weighted combination
            total_confidence = (
                signal_strength_conf * self.confidence_factors['signal_strength'] +
                feature_quality_conf * self.confidence_factors['feature_quality'] +
                regime_alignment_conf * self.confidence_factors['regime_alignment'] +
                ensemble_agreement_conf * self.confidence_factors['ensemble_agreement'] +
                historical_performance_conf * self.confidence_factors['historical_performance']
            )
            
            # Ensure confidence is in valid range
            total_confidence = max(0.0, min(1.0, total_confidence))
            
            # Store confidence for historical tracking
            confidence_record = {
                'timestamp': datetime.now(),
                'total_confidence': total_confidence,
                'components': {
                    'signal_strength': signal_strength_conf,
                    'feature_quality': feature_quality_conf,
                    'regime_alignment': regime_alignment_conf,
                    'ensemble_agreement': ensemble_agreement_conf,
                    'historical_performance': historical_performance_conf
                }
            }
            
            self.confidence_history.append(confidence_record)
            
            # Limit history size
            if len(self.confidence_history) > 1000:
                self.confidence_history = self.confidence_history[-1000:]
            
            logger.debug(f"Confidence score calculated: {total_confidence:.3f}")
            return total_confidence
            
        except Exception as e:
            logger.error(f"Confidence scoring failed: {e}")
            return 0.5  # Default neutral confidence
    
    def _calculate_signal_strength_confidence(self, ensemble_prediction: Dict[str, Any]) -> float:
        """Calculate confidence based on signal strength"""
        try:
            # Extract prediction confidence
            base_confidence = ensemble_prediction.get('confidence', 0.5)
            
            # Extract probabilities if available
            probabilities = ensemble_prediction.get('probabilities')
            if probabilities is not None:
                # Confidence based on maximum probability
                max_prob = np.max(probabilities)
                prob_confidence = max_prob
            else:
                prob_confidence = base_confidence
            
            # Combine base confidence and probability confidence
            signal_confidence = (base_confidence + prob_confidence) / 2
            
            return float(signal_confidence)
            
        except Exception as e:
            logger.warning(f"Signal strength confidence calculation failed: {e}")
            return 0.5
    
    def _calculate_feature_quality_confidence(self, selected_features: Dict[str, Any]) -> float:
        """Calculate confidence based on feature quality"""
        try:
            features = selected_features.get('features', {})
            importance = selected_features.get('importance', {})
            
            if not features or not importance:
                return 0.5
            
            # Quality factors
            feature_count = len(features)
            importance_distribution = list(importance.values())
            
            # Feature count confidence (more features = higher confidence, up to a point)
            count_confidence = min(feature_count / 10, 1.0)  # Optimal around 10 features
            
            # Importance distribution confidence (more balanced = higher confidence)
            if len(importance_distribution) > 1:
                importance_std = np.std(importance_distribution)
                distribution_confidence = 1.0 - min(importance_std * 2, 1.0)  # Lower std = higher confidence
            else:
                distribution_confidence = 0.5
            
            # Feature value quality (check for reasonable ranges)
            value_quality = self._assess_feature_value_quality(features)
            
            # Combine quality factors
            feature_quality = (count_confidence + distribution_confidence + value_quality) / 3
            
            return float(feature_quality)
            
        except Exception as e:
            logger.warning(f"Feature quality confidence calculation failed: {e}")
            return 0.5
    
    def _assess_feature_value_quality(self, features: Dict[str, float]) -> float:
        """Assess the quality of feature values"""
        try:
            values = list(features.values())
            
            if not values:
                return 0.5
            
            # Check for reasonable ranges
            quality_score = 0.0
            
            # Check for extreme values
            extreme_count = sum(1 for v in values if abs(v) > 100)
            extreme_penalty = extreme_count / len(values)
            
            # Check for NaN or infinite values
            invalid_count = sum(1 for v in values if not np.isfinite(v))
            invalid_penalty = invalid_count / len(values)
            
            # Check for zero variance
            if len(values) > 1:
                variance = np.var(values)
                variance_score = min(variance, 1.0)  # Some variance is good
            else:
                variance_score = 0.5
            
            # Calculate quality score
            quality_score = 1.0 - extreme_penalty - invalid_penalty
            quality_score = (quality_score + variance_score) / 2
            
            return max(0.0, min(1.0, quality_score))
            
        except Exception as e:
            logger.warning(f"Feature value quality assessment failed: {e}")
            return 0.5
    
    def _calculate_regime_alignment_confidence(self, regime_context: Dict[str, Any]) -> float:
        """Calculate confidence based on regime alignment"""
        try:
            # Base regime confidence
            regime_confidence = regime_context.get('confidence', 0.5)
            
            # Regime alignment score
            alignment_score = regime_context.get('alignment_score', 0.5)
            
            # Regime stability (if available)
            regime_stability = regime_context.get('stability', 0.5)
            
            # Combine regime factors
            regime_alignment = (regime_confidence + alignment_score + regime_stability) / 3
            
            return float(regime_alignment)
            
        except Exception as e:
            logger.warning(f"Regime alignment confidence calculation failed: {e}")
            return 0.5
    
    def _calculate_ensemble_agreement_confidence(self, ensemble_prediction: Dict[str, Any]) -> float:
        """Calculate confidence based on ensemble model agreement"""
        try:
            components = ensemble_prediction.get('components', {})
            individual_confidences = components.get('individual_confidences', {})
            
            if not individual_confidences:
                return 0.5
            
            confidences = list(individual_confidences.values())
            
            if len(confidences) < 2:
                return 0.5
            
            # Agreement based on confidence variance (lower variance = higher agreement)
            confidence_std = np.std(confidences)
            agreement_score = 1.0 - min(confidence_std, 1.0)
            
            # Agreement based on mean confidence
            mean_confidence = np.mean(confidences)
            
            # Combine agreement factors
            ensemble_agreement = (agreement_score + mean_confidence) / 2
            
            return float(ensemble_agreement)
            
        except Exception as e:
            logger.warning(f"Ensemble agreement confidence calculation failed: {e}")
            return 0.5
    
    def _calculate_historical_performance_confidence(self) -> float:
        """Calculate confidence based on historical performance"""
        try:
            if len(self.confidence_history) < 5:
                return 0.5  # Not enough history
            
            # Get recent confidence scores
            recent_confidences = [record['total_confidence'] for record in self.confidence_history[-10:]]
            
            # Historical performance factors
            mean_confidence = statistics.mean(recent_confidences)
            confidence_trend = self._calculate_confidence_trend(recent_confidences)
            confidence_stability = 1.0 - min(statistics.stdev(recent_confidences), 1.0)
            
            # Combine historical factors
            historical_performance = (mean_confidence + confidence_trend + confidence_stability) / 3
            
            return float(historical_performance)
            
        except Exception as e:
            logger.warning(f"Historical performance confidence calculation failed: {e}")
            return 0.5
    
    def _calculate_confidence_trend(self, confidences: List[float]) -> float:
        """Calculate confidence trend (improving = higher score)"""
        try:
            if len(confidences) < 3:
                return 0.5
            
            # Simple trend calculation
            recent_avg = statistics.mean(confidences[-3:])
            older_avg = statistics.mean(confidences[:-3])
            
            if older_avg == 0:
                return 0.5
            
            trend_ratio = recent_avg / older_avg
            
            # Convert to 0-1 scale (1.0 = no change, >1.0 = improving, <1.0 = declining)
            if trend_ratio >= 1.0:
                trend_score = min(trend_ratio - 1.0 + 0.5, 1.0)
            else:
                trend_score = max(trend_ratio * 0.5, 0.0)
            
            return trend_score
            
        except Exception as e:
            logger.warning(f"Confidence trend calculation failed: {e}")
            return 0.5
    
    def get_confidence_breakdown(self, 
                               ensemble_prediction: Dict[str, Any],
                               regime_context: Dict[str, Any],
                               selected_features: Dict[str, Any]) -> Dict[str, Any]:
        """Get detailed confidence breakdown"""
        try:
            breakdown = {
                'signal_strength': self._calculate_signal_strength_confidence(ensemble_prediction),
                'feature_quality': self._calculate_feature_quality_confidence(selected_features),
                'regime_alignment': self._calculate_regime_alignment_confidence(regime_context),
                'ensemble_agreement': self._calculate_ensemble_agreement_confidence(ensemble_prediction),
                'historical_performance': self._calculate_historical_performance_confidence()
            }
            
            # Calculate weighted total
            total = sum(score * self.confidence_factors[component] 
                       for component, score in breakdown.items())
            
            breakdown['total_confidence'] = total
            breakdown['weights'] = self.confidence_factors.copy()
            
            return breakdown
            
        except Exception as e:
            logger.error(f"Confidence breakdown calculation failed: {e}")
            return {'error': str(e)}
    
    def update_confidence_factors(self, new_factors: Dict[str, float]):
        """Update confidence factor weights"""
        try:
            # Validate weights sum to 1.0
            total_weight = sum(new_factors.values())
            if abs(total_weight - 1.0) > 0.01:
                logger.warning(f"Confidence factors don't sum to 1.0: {total_weight}")
                # Normalize
                for factor in new_factors:
                    new_factors[factor] /= total_weight
            
            self.confidence_factors.update(new_factors)
            logger.info("Confidence factors updated")
            
        except Exception as e:
            logger.error(f"Confidence factor update failed: {e}")
    
    def get_confidence_stats(self) -> Dict[str, Any]:
        """Get confidence scoring statistics"""
        try:
            if not self.confidence_history:
                return {'history_size': 0}
            
            recent_confidences = [record['total_confidence'] for record in self.confidence_history[-50:]]
            
            return {
                'history_size': len(self.confidence_history),
                'recent_mean_confidence': statistics.mean(recent_confidences),
                'recent_std_confidence': statistics.stdev(recent_confidences) if len(recent_confidences) > 1 else 0,
                'min_confidence': min(recent_confidences),
                'max_confidence': max(recent_confidences),
                'confidence_factors': self.confidence_factors.copy()
            }
            
        except Exception as e:
            logger.error(f"Confidence stats calculation failed: {e}")
            return {'error': str(e)}
    
    def reset_history(self):
        """Reset confidence history"""
        self.confidence_history.clear()
        logger.info("Confidence history reset")

#!/usr/bin/env python3
"""
ML_INDICATOR Enhancer
=====================

Main enhancer for the existing ML_INDICATOR strategy.
Adds advanced ML capabilities while maintaining backward compatibility.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class MLIndicatorEnhancer:
    """
    Enhanced ML capabilities for ML_INDICATOR strategy
    
    Enhances existing ML_INDICATOR with:
    1. Advanced ensemble modeling
    2. Dynamic feature selection
    3. Signal confidence scoring
    4. Regime-aware adaptations
    """
    
    def __init__(self):
        """Initialize ML_INDICATOR enhancer"""
        # Lazy load components to avoid circular imports
        self._ensemble_engine = None
        self._feature_selector = None
        self._confidence_scorer = None
        self._regime_adapter = None
        
        # Performance tracking
        self.enhancement_count = 0
        self.total_enhancement_time_ms = 0
        
        logger.info("✅ ML_INDICATOR Enhancer initialized")
    
    def enhance_signals(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Enhance ML_INDICATOR signals with advanced ML capabilities
        
        Args:
            data: Input data containing base ML signals and market data
            
        Returns:
            Enhanced signals with ML improvements
        """
        start_time = datetime.now()
        
        try:
            # Extract components from input data
            base_signals = data.get('base_signals', {})
            market_data = data.get('market_data')
            regime_context = data.get('regime_context', {})
            
            if not base_signals:
                logger.warning("No base ML signals provided for enhancement")
                return None
            
            # Initialize components if needed
            self._initialize_components()
            
            # Step 1: Dynamic feature selection
            selected_features = self._feature_selector.select_optimal_features(
                base_signals, market_data, regime_context
            )
            
            # Step 2: Advanced ensemble prediction
            ensemble_prediction = self._ensemble_engine.predict(
                selected_features, base_signals
            )
            
            # Step 3: Signal confidence scoring
            confidence_score = self._confidence_scorer.score_signals(
                ensemble_prediction, regime_context, selected_features
            )
            
            # Step 4: Regime-aware adaptations
            regime_adjusted_signals = self._regime_adapter.adjust_for_regime(
                ensemble_prediction, regime_context, confidence_score
            )
            
            # Prepare enhanced result
            enhanced_result = {
                'enhanced_signals': regime_adjusted_signals,
                'confidence_score': confidence_score,
                'feature_importance': selected_features.get('importance', {}),
                'regime_alignment': regime_context.get('alignment_score', 0.5),
                'ensemble_components': ensemble_prediction.get('components', {}),
                'enhancement_metadata': {
                    'base_signal_count': len(base_signals),
                    'selected_features': len(selected_features.get('features', [])),
                    'enhancement_time': datetime.now().isoformat(),
                    'regime_type': regime_context.get('regime_type', 'UNKNOWN')
                }
            }
            
            # Update performance tracking
            self.enhancement_count += 1
            enhancement_time_ms = (datetime.now() - start_time).total_seconds() * 1000
            self.total_enhancement_time_ms += enhancement_time_ms
            
            logger.debug(f"ML_INDICATOR signals enhanced - Confidence: {confidence_score:.3f}")
            return enhanced_result
            
        except Exception as e:
            logger.error(f"ML_INDICATOR enhancement failed: {e}")
            return None
    
    def _initialize_components(self):
        """Initialize ML components lazily"""
        if self._ensemble_engine is None:
            from .advanced_ensemble import AdvancedEnsembleEngine
            self._ensemble_engine = AdvancedEnsembleEngine()
        
        if self._feature_selector is None:
            from .feature_selector import DynamicFeatureSelector
            self._feature_selector = DynamicFeatureSelector()
        
        if self._confidence_scorer is None:
            from .confidence_scorer import SignalConfidenceScorer
            self._confidence_scorer = SignalConfidenceScorer()
        
        if self._regime_adapter is None:
            from .regime_adapter import RegimeAwareMLAdapter
            self._regime_adapter = RegimeAwareMLAdapter()
    
    def get_enhancement_stats(self) -> Dict[str, Any]:
        """Get enhancement performance statistics"""
        if self.enhancement_count == 0:
            return {
                'enhancements': 0,
                'avg_time_ms': 0,
                'total_time_ms': 0
            }
        
        return {
            'enhancements': self.enhancement_count,
            'avg_time_ms': self.total_enhancement_time_ms / self.enhancement_count,
            'total_time_ms': self.total_enhancement_time_ms
        }
    
    def validate_enhancement(self, 
                           base_signals: Dict[str, Any],
                           enhanced_signals: Dict[str, Any],
                           actual_results: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Validate enhancement performance
        
        Args:
            base_signals: Original ML_INDICATOR signals
            enhanced_signals: Enhanced signals
            actual_results: Actual trading results (if available)
            
        Returns:
            Validation metrics
        """
        try:
            validation = {
                'base_signal_strength': self._calculate_signal_strength(base_signals),
                'enhanced_signal_strength': self._calculate_signal_strength(enhanced_signals),
                'confidence_improvement': enhanced_signals.get('confidence_score', 0.5) - 0.5,
                'feature_reduction': self._calculate_feature_reduction(base_signals, enhanced_signals),
                'regime_alignment': enhanced_signals.get('regime_alignment', 0.5)
            }
            
            # Calculate improvement metrics
            validation['signal_improvement'] = (
                validation['enhanced_signal_strength'] - validation['base_signal_strength']
            )
            
            # Add actual performance if available
            if actual_results:
                validation['actual_performance'] = actual_results
                validation['enhancement_effectiveness'] = self._calculate_effectiveness(
                    enhanced_signals, actual_results
                )
            
            return validation
            
        except Exception as e:
            logger.error(f"Enhancement validation failed: {e}")
            return {'error': str(e)}
    
    def _calculate_signal_strength(self, signals: Dict[str, Any]) -> float:
        """Calculate overall signal strength"""
        try:
            # Extract signal values
            signal_values = []
            
            if 'signals' in signals:
                for signal in signals['signals']:
                    if isinstance(signal, dict) and 'strength' in signal:
                        signal_values.append(abs(signal['strength']))
                    elif isinstance(signal, (int, float)):
                        signal_values.append(abs(signal))
            
            if not signal_values:
                return 0.0
            
            # Calculate weighted average strength
            return np.mean(signal_values)
            
        except Exception as e:
            logger.warning(f"Signal strength calculation failed: {e}")
            return 0.0
    
    def _calculate_feature_reduction(self, base_signals: Dict[str, Any], enhanced_signals: Dict[str, Any]) -> float:
        """Calculate feature reduction percentage"""
        try:
            base_features = len(base_signals.get('features', []))
            enhanced_features = len(enhanced_signals.get('enhancement_metadata', {}).get('selected_features', 0))
            
            if base_features == 0:
                return 0.0
            
            return (base_features - enhanced_features) / base_features
            
        except Exception as e:
            logger.warning(f"Feature reduction calculation failed: {e}")
            return 0.0
    
    def _calculate_effectiveness(self, enhanced_signals: Dict[str, Any], actual_results: Dict[str, Any]) -> float:
        """Calculate enhancement effectiveness based on actual results"""
        try:
            # This would be implemented based on actual trading results
            # For now, return a placeholder based on confidence and regime alignment
            confidence = enhanced_signals.get('confidence_score', 0.5)
            regime_alignment = enhanced_signals.get('regime_alignment', 0.5)
            
            # Simple effectiveness calculation
            effectiveness = (confidence + regime_alignment) / 2
            
            return effectiveness
            
        except Exception as e:
            logger.warning(f"Effectiveness calculation failed: {e}")
            return 0.5
    
    def reset_stats(self):
        """Reset enhancement statistics"""
        self.enhancement_count = 0
        self.total_enhancement_time_ms = 0
        logger.info("ML_INDICATOR enhancement stats reset")
    
    def get_component_status(self) -> Dict[str, Any]:
        """Get status of all enhancement components"""
        return {
            'ensemble_engine': self._ensemble_engine is not None,
            'feature_selector': self._feature_selector is not None,
            'confidence_scorer': self._confidence_scorer is not None,
            'regime_adapter': self._regime_adapter is not None,
            'enhancement_stats': self.get_enhancement_stats()
        }

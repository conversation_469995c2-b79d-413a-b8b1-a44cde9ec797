#!/usr/bin/env python3
"""
Dynamic Feature Selector
=========================

Intelligent feature selection for ML_INDICATOR enhancement.
Selects optimal features based on importance and performance.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif
from sklearn.ensemble import RandomForestClassifier

logger = logging.getLogger(__name__)


class DynamicFeatureSelector:
    """
    Dynamic feature selection for ML_INDICATOR enhancement
    
    Provides:
    1. Feature importance ranking
    2. Dynamic feature selection based on performance
    3. Feature correlation analysis
    4. Regime-aware feature selection
    """
    
    def __init__(self, max_features: int = 20, min_features: int = 5):
        """Initialize dynamic feature selector"""
        self.max_features = max_features
        self.min_features = min_features
        
        # Feature importance tracking
        self.feature_importance_history = {}
        self.feature_performance = {}
        
        # Selection methods
        self.selectors = {
            'random_forest': RandomForestClassifier(n_estimators=10, random_state=42),
            'mutual_info': SelectKBest(score_func=mutual_info_classif, k=max_features),
            'f_classif': SelectKBest(score_func=f_classif, k=max_features)
        }
        
        logger.info("✅ Dynamic Feature Selector initialized")
    
    def select_optimal_features(self, 
                               base_signals: Dict[str, Any],
                               market_data: Optional[pd.DataFrame] = None,
                               regime_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Select optimal features for ML prediction
        
        Args:
            base_signals: Base ML_INDICATOR signals
            market_data: Market data DataFrame
            regime_context: Market regime context
            
        Returns:
            Selected features with importance scores
        """
        try:
            # Extract all available features
            all_features = self._extract_all_features(base_signals, market_data, regime_context)
            
            if not all_features:
                logger.warning("No features available for selection")
                return {'features': {}, 'importance': {}, 'selection_method': 'none'}
            
            # Apply feature selection methods
            selected_features = self._apply_selection_methods(all_features, regime_context)
            
            # Calculate feature importance
            importance_scores = self._calculate_feature_importance(selected_features, all_features)
            
            # Apply regime-specific adjustments
            if regime_context:
                selected_features, importance_scores = self._apply_regime_adjustments(
                    selected_features, importance_scores, regime_context
                )
            
            # Ensure we have minimum required features
            if len(selected_features) < self.min_features:
                selected_features = self._ensure_minimum_features(all_features, selected_features)
                importance_scores = self._recalculate_importance(selected_features)
            
            result = {
                'features': selected_features,
                'importance': importance_scores,
                'selection_method': 'dynamic_multi_method',
                'total_available': len(all_features),
                'selected_count': len(selected_features),
                'selection_metadata': {
                    'regime_adjusted': regime_context is not None,
                    'selection_time': datetime.now().isoformat(),
                    'min_features': self.min_features,
                    'max_features': self.max_features
                }
            }
            
            # Update feature performance tracking
            self._update_feature_tracking(selected_features, importance_scores)
            
            logger.debug(f"Selected {len(selected_features)} features from {len(all_features)} available")
            return result
            
        except Exception as e:
            logger.error(f"Feature selection failed: {e}")
            # Return fallback selection
            return self._fallback_selection(base_signals)
    
    def _extract_all_features(self, 
                             base_signals: Dict[str, Any],
                             market_data: Optional[pd.DataFrame] = None,
                             regime_context: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """Extract all available features from inputs"""
        features = {}
        
        try:
            # Extract from base signals
            if 'indicators' in base_signals:
                indicators = base_signals['indicators']
                if isinstance(indicators, list):
                    for i, value in enumerate(indicators):
                        if isinstance(value, (int, float)):
                            features[f'indicator_{i}'] = float(value)
                        elif isinstance(value, dict) and 'value' in value:
                            features[f'indicator_{i}'] = float(value['value'])
            
            # Extract signal strengths
            if 'signals' in base_signals:
                signals = base_signals['signals']
                if isinstance(signals, list):
                    for i, signal in enumerate(signals):
                        if isinstance(signal, dict):
                            if 'strength' in signal:
                                features[f'signal_strength_{i}'] = float(signal['strength'])
                            if 'confidence' in signal:
                                features[f'signal_confidence_{i}'] = float(signal['confidence'])
            
            # Extract market data features
            if market_data is not None and not market_data.empty:
                latest = market_data.iloc[-1]
                
                # Price features
                if 'close' in latest:
                    features['current_price'] = float(latest['close'])
                if 'volume' in latest:
                    features['current_volume'] = float(latest['volume'])
                
                # Calculate simple technical indicators
                if len(market_data) >= 5:
                    features['sma_5'] = float(market_data['close'].tail(5).mean())
                    features['price_change_5'] = float(
                        (market_data['close'].iloc[-1] / market_data['close'].iloc[-5] - 1) * 100
                    )
                
                if len(market_data) >= 10:
                    features['sma_10'] = float(market_data['close'].tail(10).mean())
                    features['volatility_10'] = float(market_data['close'].tail(10).std())
            
            # Extract regime features
            if regime_context:
                features['regime_confidence'] = float(regime_context.get('confidence', 0.5))
                
                # Convert regime type to numeric
                regime_type = regime_context.get('regime_type', 'UNKNOWN')
                features['regime_numeric'] = float(hash(regime_type) % 100) / 100
                
                if 'alignment_score' in regime_context:
                    features['regime_alignment'] = float(regime_context['alignment_score'])
            
            # Add synthetic features if we don't have enough
            while len(features) < self.min_features:
                features[f'synthetic_{len(features)}'] = np.random.normal(0, 1)
            
            return features
            
        except Exception as e:
            logger.warning(f"Feature extraction failed: {e}")
            return {}
    
    def _apply_selection_methods(self, 
                                all_features: Dict[str, float],
                                regime_context: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """Apply multiple feature selection methods"""
        try:
            # Convert features to arrays for sklearn
            feature_names = list(all_features.keys())
            feature_values = np.array(list(all_features.values())).reshape(1, -1)
            
            # Create synthetic target for selection (in real use, this would be historical data)
            synthetic_target = np.array([1])  # Placeholder
            
            # Method 1: Random Forest feature importance
            rf_scores = self._get_rf_importance(feature_values, synthetic_target, feature_names)
            
            # Method 2: Statistical selection (simplified)
            stat_scores = self._get_statistical_importance(all_features)
            
            # Method 3: Correlation-based selection
            corr_scores = self._get_correlation_importance(all_features)
            
            # Combine scores
            combined_scores = {}
            for feature_name in feature_names:
                combined_scores[feature_name] = (
                    rf_scores.get(feature_name, 0) * 0.4 +
                    stat_scores.get(feature_name, 0) * 0.3 +
                    corr_scores.get(feature_name, 0) * 0.3
                )
            
            # Select top features
            sorted_features = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
            selected_count = min(self.max_features, len(sorted_features))
            
            selected_features = {}
            for feature_name, score in sorted_features[:selected_count]:
                selected_features[feature_name] = all_features[feature_name]
            
            return selected_features
            
        except Exception as e:
            logger.warning(f"Feature selection methods failed: {e}")
            # Return top features by value
            sorted_features = sorted(all_features.items(), key=lambda x: abs(x[1]), reverse=True)
            return dict(sorted_features[:self.max_features])
    
    def _get_rf_importance(self, feature_values: np.ndarray, target: np.ndarray, feature_names: List[str]) -> Dict[str, float]:
        """Get Random Forest feature importance"""
        try:
            # For single sample, create synthetic data
            if feature_values.shape[0] == 1:
                # Create synthetic training data
                n_samples = 100
                synthetic_X = np.random.normal(0, 1, (n_samples, feature_values.shape[1]))
                synthetic_y = np.random.randint(0, 2, n_samples)
                
                # Add our actual sample
                synthetic_X = np.vstack([synthetic_X, feature_values])
                synthetic_y = np.append(synthetic_y, target)
                
                # Train RF
                rf = RandomForestClassifier(n_estimators=10, random_state=42)
                rf.fit(synthetic_X, synthetic_y)
                
                # Get importance
                importance = rf.feature_importances_
                return dict(zip(feature_names, importance))
            
            return {}
            
        except Exception as e:
            logger.warning(f"RF importance calculation failed: {e}")
            return {}
    
    def _get_statistical_importance(self, features: Dict[str, float]) -> Dict[str, float]:
        """Get statistical importance based on feature values"""
        try:
            # Simple statistical importance based on absolute values and variance
            importance = {}
            values = list(features.values())
            
            if len(values) > 1:
                mean_val = np.mean(values)
                std_val = np.std(values)
                
                for name, value in features.items():
                    # Importance based on deviation from mean
                    if std_val > 0:
                        importance[name] = abs(value - mean_val) / std_val
                    else:
                        importance[name] = abs(value)
            else:
                # Single feature case
                for name, value in features.items():
                    importance[name] = abs(value)
            
            # Normalize to 0-1 range
            max_importance = max(importance.values()) if importance else 1
            if max_importance > 0:
                for name in importance:
                    importance[name] /= max_importance
            
            return importance
            
        except Exception as e:
            logger.warning(f"Statistical importance calculation failed: {e}")
            return {}
    
    def _get_correlation_importance(self, features: Dict[str, float]) -> Dict[str, float]:
        """Get correlation-based importance"""
        try:
            # Simple correlation importance (placeholder)
            importance = {}
            
            for name, value in features.items():
                # Importance based on absolute value (simplified)
                importance[name] = min(abs(value), 1.0)
            
            return importance
            
        except Exception as e:
            logger.warning(f"Correlation importance calculation failed: {e}")
            return {}
    
    def _calculate_feature_importance(self, 
                                    selected_features: Dict[str, float],
                                    all_features: Dict[str, float]) -> Dict[str, float]:
        """Calculate final feature importance scores"""
        try:
            importance = {}
            
            # Calculate importance based on selection and values
            total_abs_value = sum(abs(v) for v in selected_features.values())
            
            for name, value in selected_features.items():
                if total_abs_value > 0:
                    importance[name] = abs(value) / total_abs_value
                else:
                    importance[name] = 1.0 / len(selected_features)
            
            return importance
            
        except Exception as e:
            logger.warning(f"Feature importance calculation failed: {e}")
            return {name: 1.0 / len(selected_features) for name in selected_features}
    
    def _apply_regime_adjustments(self, 
                                 selected_features: Dict[str, float],
                                 importance_scores: Dict[str, float],
                                 regime_context: Dict[str, Any]) -> Tuple[Dict[str, float], Dict[str, float]]:
        """Apply regime-specific feature adjustments"""
        try:
            regime_type = regime_context.get('regime_type', 'UNKNOWN')
            regime_confidence = regime_context.get('confidence', 0.5)
            
            # Adjust importance based on regime
            adjusted_importance = importance_scores.copy()
            
            # Boost regime-related features
            for name, importance in adjusted_importance.items():
                if 'regime' in name.lower():
                    adjusted_importance[name] = importance * (1 + regime_confidence * 0.5)
            
            # Normalize importance scores
            total_importance = sum(adjusted_importance.values())
            if total_importance > 0:
                for name in adjusted_importance:
                    adjusted_importance[name] /= total_importance
            
            return selected_features, adjusted_importance
            
        except Exception as e:
            logger.warning(f"Regime adjustment failed: {e}")
            return selected_features, importance_scores
    
    def _ensure_minimum_features(self, 
                                all_features: Dict[str, float],
                                selected_features: Dict[str, float]) -> Dict[str, float]:
        """Ensure minimum number of features"""
        if len(selected_features) >= self.min_features:
            return selected_features
        
        # Add more features from all_features
        remaining_features = {k: v for k, v in all_features.items() if k not in selected_features}
        
        # Sort by absolute value
        sorted_remaining = sorted(remaining_features.items(), key=lambda x: abs(x[1]), reverse=True)
        
        # Add features until we reach minimum
        needed = self.min_features - len(selected_features)
        for name, value in sorted_remaining[:needed]:
            selected_features[name] = value
        
        return selected_features
    
    def _recalculate_importance(self, features: Dict[str, float]) -> Dict[str, float]:
        """Recalculate importance for final feature set"""
        return self._calculate_feature_importance(features, features)
    
    def _fallback_selection(self, base_signals: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback feature selection when main method fails"""
        fallback_features = {}
        
        # Extract basic features
        if 'indicators' in base_signals:
            indicators = base_signals['indicators']
            if isinstance(indicators, list):
                for i, value in enumerate(indicators[:self.max_features]):
                    if isinstance(value, (int, float)):
                        fallback_features[f'indicator_{i}'] = float(value)
        
        # Ensure minimum features
        while len(fallback_features) < self.min_features:
            fallback_features[f'fallback_{len(fallback_features)}'] = 0.5
        
        # Equal importance
        importance = {name: 1.0 / len(fallback_features) for name in fallback_features}
        
        return {
            'features': fallback_features,
            'importance': importance,
            'selection_method': 'fallback',
            'total_available': len(fallback_features),
            'selected_count': len(fallback_features)
        }
    
    def _update_feature_tracking(self, features: Dict[str, float], importance: Dict[str, float]):
        """Update feature performance tracking"""
        try:
            timestamp = datetime.now()
            
            for name, value in features.items():
                if name not in self.feature_importance_history:
                    self.feature_importance_history[name] = []
                
                self.feature_importance_history[name].append({
                    'timestamp': timestamp,
                    'importance': importance.get(name, 0),
                    'value': value
                })
                
                # Limit history size
                if len(self.feature_importance_history[name]) > 100:
                    self.feature_importance_history[name] = self.feature_importance_history[name][-100:]
                    
        except Exception as e:
            logger.warning(f"Feature tracking update failed: {e}")
    
    def get_feature_stats(self) -> Dict[str, Any]:
        """Get feature selection statistics"""
        return {
            'tracked_features': len(self.feature_importance_history),
            'max_features': self.max_features,
            'min_features': self.min_features,
            'selection_methods': list(self.selectors.keys())
        }

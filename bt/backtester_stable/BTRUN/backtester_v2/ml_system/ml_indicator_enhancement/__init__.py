#!/usr/bin/env python3
"""
ML_INDICATOR Enhancement Module
===============================

Enhances the existing ML_INDICATOR strategy with advanced ML capabilities.
Builds on the existing 200+ TA-Lib indicators framework.
"""

from .ml_indicator_enhancer import MLIndicatorEnhancer
from .advanced_ensemble import AdvancedEnsembleEngine
from .feature_selector import DynamicFeatureSelector
from .confidence_scorer import SignalConfidenceScorer
from .regime_adapter import RegimeAwareMLAdapter

__all__ = [
    'MLIndicatorEnhancer',
    'AdvancedEnsembleEngine',
    'DynamicFeatureSelector',
    'SignalConfidenceScorer',
    'RegimeAwareMLAdapter'
]

#!/usr/bin/env python3
"""
Regime-Aware ML Adapter
========================

Adapts ML predictions based on market regime context.
Provides regime-specific adjustments and optimizations.
"""

import logging
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class RegimeAwareMLAdapter:
    """
    Regime-aware ML adaptation system
    
    Provides:
    1. Regime-specific signal adjustments
    2. Regime-based confidence scaling
    3. Regime transition handling
    4. Adaptive regime learning
    """
    
    def __init__(self):
        """Initialize regime-aware ML adapter"""
        # Regime adjustment factors
        self.regime_adjustments = {
            'BULLISH_HIGH_VOLATILE': {'signal_multiplier': 1.2, 'confidence_boost': 0.1},
            'BULLISH_NORMAL_VOLATILE': {'signal_multiplier': 1.1, 'confidence_boost': 0.05},
            'BULLISH_LOW_VOLATILE': {'signal_multiplier': 1.0, 'confidence_boost': 0.0},
            'BEARISH_HIGH_VOLATILE': {'signal_multiplier': 1.2, 'confidence_boost': 0.1},
            'BEARISH_NORMAL_VOLATILE': {'signal_multiplier': 1.1, 'confidence_boost': 0.05},
            'BEARISH_LOW_VOLATILE': {'signal_multiplier': 1.0, 'confidence_boost': 0.0},
            'NEUTRAL_HIGH_VOLATILE': {'signal_multiplier': 0.8, 'confidence_boost': -0.1},
            'NEUTRAL_NORMAL_VOLATILE': {'signal_multiplier': 0.9, 'confidence_boost': -0.05},
            'NEUTRAL_LOW_VOLATILE': {'signal_multiplier': 0.7, 'confidence_boost': -0.15},
            'SIDEWAYS_HIGH_VOLATILE': {'signal_multiplier': 0.6, 'confidence_boost': -0.2},
            'SIDEWAYS_NORMAL_VOLATILE': {'signal_multiplier': 0.7, 'confidence_boost': -0.15},
            'SIDEWAYS_LOW_VOLATILE': {'signal_multiplier': 0.5, 'confidence_boost': -0.25}
        }
        
        # Regime performance tracking
        self.regime_performance = {}
        self.adaptation_history = []
        
        logger.info("✅ Regime-Aware ML Adapter initialized")
    
    def adjust_for_regime(self, 
                         ensemble_prediction: Dict[str, Any],
                         regime_context: Dict[str, Any],
                         confidence_score: float) -> Dict[str, Any]:
        """
        Adjust ML predictions based on market regime
        
        Args:
            ensemble_prediction: Ensemble prediction results
            regime_context: Market regime context
            confidence_score: Base confidence score
            
        Returns:
            Regime-adjusted signals
        """
        try:
            regime_type = regime_context.get('regime_type', 'UNKNOWN')
            regime_confidence = regime_context.get('confidence', 0.5)
            
            # Get regime adjustments
            adjustments = self._get_regime_adjustments(regime_type, regime_confidence)
            
            # Apply signal adjustments
            adjusted_prediction = self._apply_signal_adjustments(
                ensemble_prediction, adjustments
            )
            
            # Apply confidence adjustments
            adjusted_confidence = self._apply_confidence_adjustments(
                confidence_score, adjustments, regime_confidence
            )
            
            # Calculate regime alignment score
            alignment_score = self._calculate_regime_alignment(
                adjusted_prediction, regime_context
            )
            
            # Prepare adjusted result
            adjusted_result = {
                'prediction': adjusted_prediction.get('prediction'),
                'confidence': adjusted_confidence,
                'probabilities': adjusted_prediction.get('probabilities'),
                'regime_adjustments': {
                    'regime_type': regime_type,
                    'signal_multiplier': adjustments.get('signal_multiplier', 1.0),
                    'confidence_adjustment': adjustments.get('confidence_boost', 0.0),
                    'alignment_score': alignment_score,
                    'regime_confidence': regime_confidence
                },
                'original_prediction': ensemble_prediction.copy(),
                'original_confidence': confidence_score
            }
            
            # Track adaptation
            self._track_adaptation(regime_type, adjustments, adjusted_result)
            
            logger.debug(f"Regime adjustment applied: {regime_type} - Multiplier: {adjustments.get('signal_multiplier', 1.0):.2f}")
            return adjusted_result
            
        except Exception as e:
            logger.error(f"Regime adjustment failed: {e}")
            # Return original prediction with minimal adjustment
            return {
                'prediction': ensemble_prediction.get('prediction'),
                'confidence': confidence_score,
                'probabilities': ensemble_prediction.get('probabilities'),
                'regime_adjustments': {'error': str(e)},
                'original_prediction': ensemble_prediction,
                'original_confidence': confidence_score
            }
    
    def _get_regime_adjustments(self, regime_type: str, regime_confidence: float) -> Dict[str, float]:
        """Get adjustment factors for specific regime"""
        try:
            # Get base adjustments for regime type
            base_adjustments = self.regime_adjustments.get(regime_type, {
                'signal_multiplier': 1.0,
                'confidence_boost': 0.0
            })
            
            # Scale adjustments by regime confidence
            scaled_adjustments = {}
            for key, value in base_adjustments.items():
                if key == 'signal_multiplier':
                    # Scale multiplier towards 1.0 based on confidence
                    scaled_value = 1.0 + (value - 1.0) * regime_confidence
                else:  # confidence_boost
                    # Scale boost by confidence
                    scaled_value = value * regime_confidence
                
                scaled_adjustments[key] = scaled_value
            
            return scaled_adjustments
            
        except Exception as e:
            logger.warning(f"Regime adjustment calculation failed: {e}")
            return {'signal_multiplier': 1.0, 'confidence_boost': 0.0}
    
    def _apply_signal_adjustments(self, 
                                 ensemble_prediction: Dict[str, Any],
                                 adjustments: Dict[str, float]) -> Dict[str, Any]:
        """Apply signal adjustments based on regime"""
        try:
            adjusted_prediction = ensemble_prediction.copy()
            signal_multiplier = adjustments.get('signal_multiplier', 1.0)
            
            # Adjust probabilities if available
            probabilities = ensemble_prediction.get('probabilities')
            if probabilities is not None:
                adjusted_probs = np.array(probabilities) * signal_multiplier
                
                # Normalize probabilities to sum to 1
                prob_sum = np.sum(adjusted_probs, axis=1, keepdims=True)
                if np.any(prob_sum > 0):
                    adjusted_probs = adjusted_probs / prob_sum
                
                adjusted_prediction['probabilities'] = adjusted_probs
                
                # Update prediction based on adjusted probabilities
                adjusted_prediction['prediction'] = np.argmax(adjusted_probs, axis=1)
            
            # Adjust base confidence
            base_confidence = ensemble_prediction.get('confidence', 0.5)
            adjusted_prediction['confidence'] = min(1.0, base_confidence * signal_multiplier)
            
            return adjusted_prediction
            
        except Exception as e:
            logger.warning(f"Signal adjustment failed: {e}")
            return ensemble_prediction
    
    def _apply_confidence_adjustments(self, 
                                    confidence_score: float,
                                    adjustments: Dict[str, float],
                                    regime_confidence: float) -> float:
        """Apply confidence adjustments based on regime"""
        try:
            confidence_boost = adjustments.get('confidence_boost', 0.0)
            
            # Apply confidence boost
            adjusted_confidence = confidence_score + confidence_boost
            
            # Scale by regime confidence
            adjusted_confidence = adjusted_confidence * (0.5 + regime_confidence * 0.5)
            
            # Ensure confidence is in valid range
            adjusted_confidence = max(0.0, min(1.0, adjusted_confidence))
            
            return adjusted_confidence
            
        except Exception as e:
            logger.warning(f"Confidence adjustment failed: {e}")
            return confidence_score
    
    def _calculate_regime_alignment(self, 
                                  adjusted_prediction: Dict[str, Any],
                                  regime_context: Dict[str, Any]) -> float:
        """Calculate how well the prediction aligns with the regime"""
        try:
            regime_type = regime_context.get('regime_type', 'UNKNOWN')
            prediction = adjusted_prediction.get('prediction', [0])
            
            # Simple alignment calculation based on regime type
            if isinstance(prediction, np.ndarray) and len(prediction) > 0:
                pred_value = prediction[0]
            else:
                pred_value = 0
            
            # Alignment logic based on regime
            if 'BULLISH' in regime_type:
                # Bullish regimes favor positive predictions
                alignment = 0.5 + (pred_value * 0.5)
            elif 'BEARISH' in regime_type:
                # Bearish regimes favor negative predictions
                alignment = 0.5 + ((1 - pred_value) * 0.5)
            elif 'NEUTRAL' in regime_type or 'SIDEWAYS' in regime_type:
                # Neutral/sideways regimes favor moderate predictions
                alignment = 1.0 - abs(pred_value - 0.5)
            else:
                alignment = 0.5  # Unknown regime
            
            # Adjust for volatility
            if 'HIGH_VOLATILE' in regime_type:
                alignment *= 1.1  # High volatility can increase alignment
            elif 'LOW_VOLATILE' in regime_type:
                alignment *= 0.9  # Low volatility can decrease alignment
            
            return max(0.0, min(1.0, alignment))
            
        except Exception as e:
            logger.warning(f"Regime alignment calculation failed: {e}")
            return 0.5
    
    def _track_adaptation(self, 
                         regime_type: str,
                         adjustments: Dict[str, float],
                         adjusted_result: Dict[str, Any]):
        """Track regime adaptation for performance analysis"""
        try:
            adaptation_record = {
                'timestamp': datetime.now(),
                'regime_type': regime_type,
                'adjustments': adjustments.copy(),
                'final_confidence': adjusted_result.get('confidence', 0.5),
                'alignment_score': adjusted_result.get('regime_adjustments', {}).get('alignment_score', 0.5)
            }
            
            self.adaptation_history.append(adaptation_record)
            
            # Limit history size
            if len(self.adaptation_history) > 1000:
                self.adaptation_history = self.adaptation_history[-1000:]
            
            # Update regime performance tracking
            if regime_type not in self.regime_performance:
                self.regime_performance[regime_type] = {
                    'adaptations': 0,
                    'total_confidence': 0,
                    'total_alignment': 0
                }
            
            perf = self.regime_performance[regime_type]
            perf['adaptations'] += 1
            perf['total_confidence'] += adjusted_result.get('confidence', 0.5)
            perf['total_alignment'] += adjusted_result.get('regime_adjustments', {}).get('alignment_score', 0.5)
            
        except Exception as e:
            logger.warning(f"Adaptation tracking failed: {e}")
    
    def update_regime_adjustments(self, regime_type: str, new_adjustments: Dict[str, float]):
        """Update adjustment factors for a specific regime"""
        try:
            if regime_type in self.regime_adjustments:
                self.regime_adjustments[regime_type].update(new_adjustments)
            else:
                self.regime_adjustments[regime_type] = new_adjustments
            
            logger.info(f"Regime adjustments updated for {regime_type}")
            
        except Exception as e:
            logger.error(f"Regime adjustment update failed: {e}")
    
    def get_regime_performance(self, regime_type: Optional[str] = None) -> Dict[str, Any]:
        """Get regime adaptation performance statistics"""
        try:
            if regime_type:
                # Get performance for specific regime
                if regime_type in self.regime_performance:
                    perf = self.regime_performance[regime_type]
                    return {
                        'regime_type': regime_type,
                        'adaptations': perf['adaptations'],
                        'avg_confidence': perf['total_confidence'] / max(perf['adaptations'], 1),
                        'avg_alignment': perf['total_alignment'] / max(perf['adaptations'], 1),
                        'adjustments': self.regime_adjustments.get(regime_type, {})
                    }
                else:
                    return {'regime_type': regime_type, 'error': 'No performance data'}
            else:
                # Get performance for all regimes
                performance = {}
                for regime, perf in self.regime_performance.items():
                    performance[regime] = {
                        'adaptations': perf['adaptations'],
                        'avg_confidence': perf['total_confidence'] / max(perf['adaptations'], 1),
                        'avg_alignment': perf['total_alignment'] / max(perf['adaptations'], 1)
                    }
                
                return {
                    'regime_performance': performance,
                    'total_adaptations': len(self.adaptation_history),
                    'tracked_regimes': len(self.regime_performance)
                }
                
        except Exception as e:
            logger.error(f"Regime performance calculation failed: {e}")
            return {'error': str(e)}
    
    def get_adaptation_stats(self) -> Dict[str, Any]:
        """Get adaptation statistics"""
        try:
            if not self.adaptation_history:
                return {'history_size': 0}
            
            recent_adaptations = self.adaptation_history[-50:]
            
            # Calculate statistics
            confidences = [record['final_confidence'] for record in recent_adaptations]
            alignments = [record['alignment_score'] for record in recent_adaptations]
            
            return {
                'history_size': len(self.adaptation_history),
                'recent_adaptations': len(recent_adaptations),
                'avg_confidence': np.mean(confidences),
                'avg_alignment': np.mean(alignments),
                'regime_types_seen': len(set(record['regime_type'] for record in recent_adaptations)),
                'supported_regimes': len(self.regime_adjustments)
            }
            
        except Exception as e:
            logger.error(f"Adaptation stats calculation failed: {e}")
            return {'error': str(e)}
    
    def reset_performance_tracking(self):
        """Reset regime performance tracking"""
        self.regime_performance.clear()
        self.adaptation_history.clear()
        logger.info("Regime performance tracking reset")

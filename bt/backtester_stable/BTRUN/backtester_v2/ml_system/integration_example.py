#!/usr/bin/env python3
"""
ML System Integration Example
=============================

Example showing how to integrate the ML system with existing strategies.
Demonstrates ML_INDICATOR enhancement without changing existing code.
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the backtester_v2 directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
backtester_v2_dir = os.path.dirname(current_dir)
sys.path.insert(0, backtester_v2_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def create_sample_market_data():
    """Create sample market data for testing"""
    dates = pd.date_range(start='2024-01-01', periods=100, freq='1min')
    
    # Generate realistic price data
    np.random.seed(42)
    price = 100
    prices = []
    volumes = []
    
    for i in range(100):
        # Random walk with slight upward bias
        change = np.random.normal(0.001, 0.02)
        price = price * (1 + change)
        prices.append(price)
        
        # Random volume
        volume = np.random.randint(1000, 5000)
        volumes.append(volume)
    
    return pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': volumes
    })


def create_sample_ml_indicator_signals():
    """Create sample ML_INDICATOR signals"""
    return {
        'signals': [
            {'strength': 0.75, 'direction': 'BUY', 'confidence': 0.8},
            {'strength': 0.65, 'direction': 'HOLD', 'confidence': 0.6},
            {'strength': 0.55, 'direction': 'SELL', 'confidence': 0.7}
        ],
        'indicators': [
            {'name': 'RSI', 'value': 0.65},
            {'name': 'MACD', 'value': 0.72},
            {'name': 'SMA_CROSS', 'value': 0.58},
            {'name': 'BOLLINGER', 'value': 0.63},
            {'name': 'STOCH', 'value': 0.69}
        ],
        'features': ['rsi', 'macd', 'sma_cross', 'bollinger', 'stochastic'],
        'base_confidence': 0.68,
        'timestamp': datetime.now().isoformat()
    }


def create_sample_regime_context():
    """Create sample market regime context"""
    return {
        'regime_type': 'BULLISH_NORMAL_VOLATILE',
        'confidence': 0.85,
        'alignment_score': 0.72,
        'stability': 0.78,
        'duration_minutes': 45,
        'transition_probability': 0.15,
        'volatility_regime': 'NORMAL',
        'trend_regime': 'BULLISH'
    }


def demonstrate_ml_indicator_enhancement():
    """Demonstrate ML_INDICATOR enhancement"""
    print("\n🚀 ML_INDICATOR Enhancement Demonstration")
    print("=" * 50)
    
    try:
        # Import ML system
        from core_infrastructure.ml_system_core import get_ml_system
        ml_system = get_ml_system()
        
        # Create sample data
        market_data = create_sample_market_data()
        base_signals = create_sample_ml_indicator_signals()
        regime_context = create_sample_regime_context()
        
        print(f"📊 Sample Data Created:")
        print(f"   Market Data: {len(market_data)} rows")
        print(f"   Base Signals: {len(base_signals['signals'])} signals")
        print(f"   Indicators: {len(base_signals['indicators'])} indicators")
        print(f"   Regime: {regime_context['regime_type']}")
        
        # Prepare data for ML enhancement
        enhancement_data = {
            'base_signals': base_signals,
            'market_data': market_data,
            'regime_context': regime_context
        }
        
        # Get ML enhancement
        print(f"\n🧠 Applying ML Enhancement...")
        enhanced_result = ml_system.get_ml_insights("ML_INDICATOR_ENHANCEMENT", enhancement_data)
        
        if enhanced_result:
            print(f"✅ ML Enhancement Successful!")
            print(f"\n📈 Enhancement Results:")
            print(f"   Original Confidence: {base_signals['base_confidence']:.3f}")
            print(f"   Enhanced Confidence: {enhanced_result.get('confidence_score', 0):.3f}")
            print(f"   Regime Alignment: {enhanced_result.get('regime_alignment', 0):.3f}")
            print(f"   Features Selected: {enhanced_result.get('enhancement_metadata', {}).get('selected_features', 0)}")
            
            # Show feature importance
            feature_importance = enhanced_result.get('feature_importance', {})
            if feature_importance:
                print(f"\n🎯 Top Features by Importance:")
                sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
                for i, (feature, importance) in enumerate(sorted_features[:5]):
                    print(f"   {i+1}. {feature}: {importance:.3f}")
            
            # Show ensemble components
            ensemble_components = enhanced_result.get('ensemble_components', {})
            if ensemble_components:
                print(f"\n🤖 Ensemble Model Results:")
                model_weights = ensemble_components.get('model_weights', {})
                for model, weight in model_weights.items():
                    print(f"   {model}: {weight:.3f}")
            
            return True
        else:
            print(f"❌ ML Enhancement Failed")
            return False
            
    except Exception as e:
        print(f"❌ Enhancement demonstration failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def demonstrate_direct_component_usage():
    """Demonstrate direct usage of ML components"""
    print("\n🔧 Direct Component Usage Demonstration")
    print("=" * 50)
    
    try:
        # Import components directly
        from ml_indicator_enhancement.ml_indicator_enhancer import MLIndicatorEnhancer
        from ml_indicator_enhancement.advanced_ensemble import AdvancedEnsembleEngine
        from ml_indicator_enhancement.feature_selector import DynamicFeatureSelector
        
        # Create sample data
        base_signals = create_sample_ml_indicator_signals()
        market_data = create_sample_market_data()
        regime_context = create_sample_regime_context()
        
        print(f"🧪 Testing Individual Components:")
        
        # Test Feature Selector
        print(f"\n1. Dynamic Feature Selector:")
        feature_selector = DynamicFeatureSelector()
        selected_features = feature_selector.select_optimal_features(
            base_signals, market_data, regime_context
        )
        print(f"   Selected {selected_features['selected_count']} from {selected_features['total_available']} features")
        print(f"   Selection method: {selected_features['selection_method']}")
        
        # Test Ensemble Engine
        print(f"\n2. Advanced Ensemble Engine:")
        ensemble_engine = AdvancedEnsembleEngine()
        ensemble_result = ensemble_engine.predict(selected_features, base_signals)
        print(f"   Prediction: {ensemble_result.get('prediction', [])}")
        print(f"   Confidence: {ensemble_result.get('confidence', 0):.3f}")
        print(f"   Models used: {len(ensemble_result.get('ensemble_metadata', {}).get('models_used', []))}")
        
        # Test Full Enhancement
        print(f"\n3. Complete ML_INDICATOR Enhancement:")
        enhancer = MLIndicatorEnhancer()
        enhancement_data = {
            'base_signals': base_signals,
            'market_data': market_data,
            'regime_context': regime_context
        }
        
        enhanced_result = enhancer.enhance_signals(enhancement_data)
        if enhanced_result:
            print(f"   Enhancement successful!")
            print(f"   Final confidence: {enhanced_result.get('confidence_score', 0):.3f}")
            print(f"   Regime alignment: {enhanced_result.get('regime_alignment', 0):.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Direct component demonstration failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def demonstrate_performance_monitoring():
    """Demonstrate performance monitoring capabilities"""
    print("\n📊 Performance Monitoring Demonstration")
    print("=" * 50)
    
    try:
        from core_infrastructure.performance_tracker import PerformanceTracker
        from core_infrastructure.ml_system_core import get_ml_system
        
        # Get ML system and performance tracker
        ml_system = get_ml_system()
        
        # Simulate multiple predictions
        print(f"🔄 Simulating multiple ML predictions...")
        
        for i in range(5):
            # Create varied sample data
            market_data = create_sample_market_data()
            base_signals = create_sample_ml_indicator_signals()
            regime_context = create_sample_regime_context()
            
            # Vary the regime type
            regime_types = ['BULLISH_HIGH_VOLATILE', 'BEARISH_NORMAL_VOLATILE', 'NEUTRAL_LOW_VOLATILE']
            regime_context['regime_type'] = regime_types[i % len(regime_types)]
            
            enhancement_data = {
                'base_signals': base_signals,
                'market_data': market_data,
                'regime_context': regime_context
            }
            
            # Get ML insights
            result = ml_system.get_ml_insights("ML_INDICATOR_ENHANCEMENT", enhancement_data)
            print(f"   Prediction {i+1}: {'✅' if result else '❌'}")
        
        # Get performance statistics
        print(f"\n📈 Performance Statistics:")
        perf_stats = ml_system.get_performance_stats()
        print(f"   Total Predictions: {perf_stats['predictions']}")
        print(f"   Error Rate: {perf_stats['error_rate']:.1%}")
        print(f"   Average Latency: {perf_stats['avg_latency_ms']:.1f}ms")
        
        # Get system status
        print(f"\n🏥 System Health:")
        status = ml_system.get_status()
        print(f"   Enabled: {status['enabled']}")
        print(f"   Healthy: {status['healthy']}")
        print(f"   Components Loaded: {sum(status['components'].values())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance monitoring demonstration failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all demonstrations"""
    print("🎯 ML System Integration Examples")
    print("=" * 60)
    
    results = []
    
    # Run demonstrations
    results.append(demonstrate_ml_indicator_enhancement())
    results.append(demonstrate_direct_component_usage())
    results.append(demonstrate_performance_monitoring())
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 Demonstration Results:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ All demonstrations successful! ({passed}/{total})")
        print("\n🎉 Key Takeaways:")
        print("   • ML system enhances existing ML_INDICATOR without changes")
        print("   • Advanced ensemble modeling improves prediction accuracy")
        print("   • Regime-aware adjustments optimize for market conditions")
        print("   • Performance monitoring ensures production reliability")
        print("   • Fallback mechanisms maintain system stability")
        print("\n🚀 Ready for production deployment!")
    else:
        print(f"⚠️  Some demonstrations failed: {passed}/{total} successful")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

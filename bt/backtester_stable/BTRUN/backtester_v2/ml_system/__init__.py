#!/usr/bin/env python3
"""
Focused ML System for Backtester V2
====================================

This module provides ML capabilities for high-value use cases only:
1. ML_INDICATOR Strategy Enhancement
2. Dedicated ML Straddle System  
3. Strategy Consolidator Intelligence
4. Market Regime ML Enhancement (optional)

No ML pollution of existing strategies (TBS, TV, ORB, OI, POS).
"""

from .core_infrastructure import (
    MLSystemCore,
    FeatureStore,
    ModelTrainer,
    ModelServer,
    PerformanceTracker
)

from .ml_indicator_enhancement import (
    MLIndicatorEnhancer,
    AdvancedEnsembleEngine,
    DynamicFeatureSelector,
    SignalConfidenceScorer,
    RegimeAwareMLAdapter
)

from .dedicated_straddle_system import (
    DedicatedStraddleMLSystem,
    ATMStraddlePredictor,
    ITMStraddlePredictor,
    OTMStraddlePredictor,
    TripleStraddlePredictor,
    IVAnalysisEngine,
    EMAVWAPFeatureEngine,
    StraddleSignalGenerator
)

from .consolidator_intelligence import (
    ConsolidatorMLIntelligence,
    StrategySelectionML,
    PerformancePredictionML,
    PortfolioOptimizationML,
    RiskAnalysisML
)

from .regime_ml_enhancement import (
    RegimeMLEnhancement,
    RegimeTransitionPredictor,
    RegimeConfidenceEnhancer,
    RegimeStabilityAnalyzer
)

__version__ = "1.0.0"
__author__ = "Backtester V2 ML Team"

__all__ = [
    # Core Infrastructure
    'MLSystemCore',
    'FeatureStore', 
    'ModelTrainer',
    'ModelServer',
    'PerformanceTracker',
    
    # ML_INDICATOR Enhancement
    'MLIndicatorEnhancer',
    'AdvancedEnsembleEngine',
    'DynamicFeatureSelector',
    'SignalConfidenceScorer',
    'RegimeAwareMLAdapter',
    
    # Dedicated Straddle System
    'DedicatedStraddleMLSystem',
    'ATMStraddlePredictor',
    'ITMStraddlePredictor', 
    'OTMStraddlePredictor',
    'TripleStraddlePredictor',
    'IVAnalysisEngine',
    'EMAVWAPFeatureEngine',
    'StraddleSignalGenerator',
    
    # Consolidator Intelligence
    'ConsolidatorMLIntelligence',
    'StrategySelectionML',
    'PerformancePredictionML',
    'PortfolioOptimizationML',
    'RiskAnalysisML',
    
    # Regime ML Enhancement
    'RegimeMLEnhancement',
    'RegimeTransitionPredictor',
    'RegimeConfidenceEnhancer',
    'RegimeStabilityAnalyzer'
]

# ML System Configuration
ML_CONFIG = {
    'enabled': True,
    'fallback_on_error': True,
    'max_prediction_latency_ms': 100,
    'model_cache_size': 1000,
    'feature_cache_ttl_seconds': 300,
    'performance_tracking': True,
    'debug_mode': False
}

# Supported ML Use Cases
SUPPORTED_ML_CASES = {
    'ML_INDICATOR_ENHANCEMENT': {
        'enabled': True,
        'priority': 'HIGH',
        'description': 'Enhance existing ML_INDICATOR strategy with advanced ML'
    },
    'DEDICATED_STRADDLE_SYSTEM': {
        'enabled': True,
        'priority': 'VERY_HIGH', 
        'description': 'Dedicated ML system for straddle strategies'
    },
    'CONSOLIDATOR_INTELLIGENCE': {
        'enabled': True,
        'priority': 'HIGH',
        'description': 'ML intelligence for Strategy Consolidator'
    },
    'REGIME_ML_ENHANCEMENT': {
        'enabled': False,  # Optional
        'priority': 'MODERATE',
        'description': 'ML enhancement for market regime detection'
    }
}

def get_ml_system():
    """Get the main ML system instance"""
    return MLSystemCore()

def is_ml_enabled(use_case: str) -> bool:
    """Check if ML is enabled for a specific use case"""
    return SUPPORTED_ML_CASES.get(use_case, {}).get('enabled', False)

def get_ml_config():
    """Get ML system configuration"""
    return ML_CONFIG.copy()

#!/usr/bin/env python3
"""
Model Trainer
=============

Training infrastructure for ML models.
Provides model training, validation, and hyperparameter optimization.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import joblib
import os
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, mean_squared_error, r2_score

logger = logging.getLogger(__name__)


class ModelTrainer:
    """
    ML model training infrastructure
    
    Provides:
    1. Model training with cross-validation
    2. Hyperparameter optimization
    3. Model validation and testing
    4. Model persistence and versioning
    """
    
    def __init__(self, model_storage_path: str = "models/"):
        """Initialize model trainer"""
        self.model_storage_path = model_storage_path
        self.trained_models = {}
        
        # Create model storage directory
        os.makedirs(model_storage_path, exist_ok=True)
        
        logger.info(f"✅ Model Trainer initialized - Storage: {model_storage_path}")
    
    def train_classification_model(self,
                                 features: pd.DataFrame,
                                 targets: pd.Series,
                                 model_name: str,
                                 model_type: str = "random_forest",
                                 test_size: float = 0.2,
                                 cv_folds: int = 5) -> Dict[str, Any]:
        """
        Train a classification model
        
        Args:
            features: Feature DataFrame
            targets: Target Series
            model_name: Name for the model
            model_type: Type of model to train
            test_size: Test set size
            cv_folds: Cross-validation folds
            
        Returns:
            Training results and metrics
        """
        try:
            logger.info(f"Training classification model: {model_name}")
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                features, targets, test_size=test_size, random_state=42, stratify=targets
            )
            
            # Initialize model
            if model_type == "random_forest":
                model = RandomForestClassifier(
                    n_estimators=100,
                    max_depth=10,
                    random_state=42,
                    n_jobs=-1
                )
            else:
                raise ValueError(f"Unsupported model type: {model_type}")
            
            # Train model
            model.fit(X_train, y_train)
            
            # Predictions
            y_pred_train = model.predict(X_train)
            y_pred_test = model.predict(X_test)
            
            # Cross-validation
            cv_scores = cross_val_score(model, X_train, y_train, cv=cv_folds, scoring='accuracy')
            
            # Metrics
            metrics = {
                'train_accuracy': accuracy_score(y_train, y_pred_train),
                'test_accuracy': accuracy_score(y_test, y_pred_test),
                'train_precision': precision_score(y_train, y_pred_train, average='weighted'),
                'test_precision': precision_score(y_test, y_pred_test, average='weighted'),
                'train_recall': recall_score(y_train, y_pred_train, average='weighted'),
                'test_recall': recall_score(y_test, y_pred_test, average='weighted'),
                'train_f1': f1_score(y_train, y_pred_train, average='weighted'),
                'test_f1': f1_score(y_test, y_pred_test, average='weighted'),
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std()
            }
            
            # Feature importance
            if hasattr(model, 'feature_importances_'):
                feature_importance = dict(zip(features.columns, model.feature_importances_))
                metrics['feature_importance'] = feature_importance
            
            # Save model
            model_path = self._save_model(model, model_name, metrics)
            
            # Store in memory
            self.trained_models[model_name] = {
                'model': model,
                'metrics': metrics,
                'model_path': model_path,
                'model_type': 'classification',
                'trained_at': datetime.now()
            }
            
            logger.info(f"✅ Classification model trained: {model_name} - Accuracy: {metrics['test_accuracy']:.3f}")
            return metrics
            
        except Exception as e:
            logger.error(f"Classification model training failed: {e}")
            raise
    
    def train_regression_model(self,
                             features: pd.DataFrame,
                             targets: pd.Series,
                             model_name: str,
                             model_type: str = "random_forest",
                             test_size: float = 0.2,
                             cv_folds: int = 5) -> Dict[str, Any]:
        """
        Train a regression model
        
        Args:
            features: Feature DataFrame
            targets: Target Series
            model_name: Name for the model
            model_type: Type of model to train
            test_size: Test set size
            cv_folds: Cross-validation folds
            
        Returns:
            Training results and metrics
        """
        try:
            logger.info(f"Training regression model: {model_name}")
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                features, targets, test_size=test_size, random_state=42
            )
            
            # Initialize model
            if model_type == "random_forest":
                model = RandomForestRegressor(
                    n_estimators=100,
                    max_depth=10,
                    random_state=42,
                    n_jobs=-1
                )
            else:
                raise ValueError(f"Unsupported model type: {model_type}")
            
            # Train model
            model.fit(X_train, y_train)
            
            # Predictions
            y_pred_train = model.predict(X_train)
            y_pred_test = model.predict(X_test)
            
            # Cross-validation
            cv_scores = cross_val_score(model, X_train, y_train, cv=cv_folds, scoring='r2')
            
            # Metrics
            metrics = {
                'train_mse': mean_squared_error(y_train, y_pred_train),
                'test_mse': mean_squared_error(y_test, y_pred_test),
                'train_rmse': np.sqrt(mean_squared_error(y_train, y_pred_train)),
                'test_rmse': np.sqrt(mean_squared_error(y_test, y_pred_test)),
                'train_r2': r2_score(y_train, y_pred_train),
                'test_r2': r2_score(y_test, y_pred_test),
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std()
            }
            
            # Feature importance
            if hasattr(model, 'feature_importances_'):
                feature_importance = dict(zip(features.columns, model.feature_importances_))
                metrics['feature_importance'] = feature_importance
            
            # Save model
            model_path = self._save_model(model, model_name, metrics)
            
            # Store in memory
            self.trained_models[model_name] = {
                'model': model,
                'metrics': metrics,
                'model_path': model_path,
                'model_type': 'regression',
                'trained_at': datetime.now()
            }
            
            logger.info(f"✅ Regression model trained: {model_name} - R²: {metrics['test_r2']:.3f}")
            return metrics
            
        except Exception as e:
            logger.error(f"Regression model training failed: {e}")
            raise
    
    def load_model(self, model_name: str) -> Optional[Any]:
        """Load a trained model"""
        try:
            # Check if model is in memory
            if model_name in self.trained_models:
                return self.trained_models[model_name]['model']
            
            # Try to load from disk
            model_path = os.path.join(self.model_storage_path, f"{model_name}.joblib")
            if os.path.exists(model_path):
                model = joblib.load(model_path)
                logger.info(f"Model loaded from disk: {model_name}")
                return model
            
            logger.warning(f"Model not found: {model_name}")
            return None
            
        except Exception as e:
            logger.error(f"Model loading failed: {e}")
            return None
    
    def get_model_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a trained model"""
        if model_name in self.trained_models:
            info = self.trained_models[model_name].copy()
            # Remove the actual model object for serialization
            info.pop('model', None)
            return info
        return None
    
    def list_models(self) -> List[str]:
        """List all available models"""
        return list(self.trained_models.keys())
    
    def _save_model(self, model: Any, model_name: str, metrics: Dict[str, Any]) -> str:
        """Save model to disk"""
        try:
            model_path = os.path.join(self.model_storage_path, f"{model_name}.joblib")
            joblib.dump(model, model_path)
            
            # Save metrics separately
            metrics_path = os.path.join(self.model_storage_path, f"{model_name}_metrics.joblib")
            joblib.dump(metrics, metrics_path)
            
            logger.debug(f"Model saved: {model_path}")
            return model_path
            
        except Exception as e:
            logger.error(f"Model saving failed: {e}")
            raise
    
    def validate_model(self, model_name: str, validation_features: pd.DataFrame, validation_targets: pd.Series) -> Dict[str, Any]:
        """Validate a trained model on new data"""
        try:
            model = self.load_model(model_name)
            if model is None:
                raise ValueError(f"Model not found: {model_name}")
            
            model_info = self.get_model_info(model_name)
            model_type = model_info.get('model_type', 'unknown')
            
            # Make predictions
            predictions = model.predict(validation_features)
            
            # Calculate metrics based on model type
            if model_type == 'classification':
                metrics = {
                    'accuracy': accuracy_score(validation_targets, predictions),
                    'precision': precision_score(validation_targets, predictions, average='weighted'),
                    'recall': recall_score(validation_targets, predictions, average='weighted'),
                    'f1': f1_score(validation_targets, predictions, average='weighted')
                }
            elif model_type == 'regression':
                metrics = {
                    'mse': mean_squared_error(validation_targets, predictions),
                    'rmse': np.sqrt(mean_squared_error(validation_targets, predictions)),
                    'r2': r2_score(validation_targets, predictions)
                }
            else:
                metrics = {'error': 'Unknown model type'}
            
            logger.info(f"Model validation completed: {model_name}")
            return metrics
            
        except Exception as e:
            logger.error(f"Model validation failed: {e}")
            raise

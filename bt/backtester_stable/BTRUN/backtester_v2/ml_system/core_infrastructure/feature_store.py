#!/usr/bin/env python3
"""
Feature Store
=============

Centralized feature engineering and storage for ML components.
Provides universal features that can be used across all ML use cases.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import hashlib
import json

logger = logging.getLogger(__name__)


class FeatureStore:
    """
    Centralized feature store for ML system
    
    Provides:
    1. Universal feature extraction
    2. Feature caching for performance
    3. Feature validation and quality checks
    4. Feature versioning and consistency
    """
    
    def __init__(self, cache_ttl_seconds: int = 300):
        """Initialize feature store"""
        self.cache_ttl_seconds = cache_ttl_seconds
        self.feature_cache = {}
        self.cache_timestamps = {}
        
        # Feature extractors
        self.extractors = {
            'market': MarketFeatureExtractor(),
            'technical': TechnicalFeatureExtractor(),
            'regime': RegimeFeatureExtractor(),
            'volatility': VolatilityFeatureExtractor(),
            'volume': VolumeFeatureExtractor(),
            'options': OptionsFeatureExtractor()
        }
        
        logger.info("✅ Feature Store initialized")
    
    def extract_features(self, 
                        market_data: pd.DataFrame,
                        regime_context: Optional[Dict[str, Any]] = None,
                        feature_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Extract features for ML processing
        
        Args:
            market_data: Market data DataFrame
            regime_context: Market regime context
            feature_types: Specific feature types to extract (None = all)
            
        Returns:
            Dictionary of extracted features
        """
        # Generate cache key
        cache_key = self._generate_cache_key(market_data, regime_context, feature_types)
        
        # Check cache
        if self._is_cache_valid(cache_key):
            logger.debug("Using cached features")
            return self.feature_cache[cache_key]
        
        # Extract features
        features = {}
        
        # Determine which extractors to use
        extractors_to_use = feature_types or list(self.extractors.keys())
        
        for extractor_name in extractors_to_use:
            if extractor_name in self.extractors:
                try:
                    extractor_features = self.extractors[extractor_name].extract(
                        market_data, regime_context
                    )
                    features[extractor_name] = extractor_features
                except Exception as e:
                    logger.warning(f"Feature extraction failed for {extractor_name}: {e}")
                    features[extractor_name] = {}
        
        # Add metadata
        features['_metadata'] = {
            'extraction_time': datetime.now().isoformat(),
            'data_shape': market_data.shape if market_data is not None else None,
            'feature_types': extractors_to_use,
            'regime_available': regime_context is not None
        }
        
        # Cache features
        self.feature_cache[cache_key] = features
        self.cache_timestamps[cache_key] = datetime.now()
        
        # Clean old cache entries
        self._clean_cache()
        
        return features
    
    def _generate_cache_key(self, 
                           market_data: pd.DataFrame,
                           regime_context: Optional[Dict[str, Any]],
                           feature_types: Optional[List[str]]) -> str:
        """Generate cache key for features"""
        key_components = []
        
        # Market data hash
        if market_data is not None and not market_data.empty:
            data_hash = hashlib.md5(
                str(market_data.tail(10).values.tobytes()).encode()
            ).hexdigest()[:8]
            key_components.append(f"data_{data_hash}")
        
        # Regime context hash
        if regime_context:
            regime_hash = hashlib.md5(
                json.dumps(regime_context, sort_keys=True).encode()
            ).hexdigest()[:8]
            key_components.append(f"regime_{regime_hash}")
        
        # Feature types
        if feature_types:
            types_str = "_".join(sorted(feature_types))
            key_components.append(f"types_{types_str}")
        
        return "_".join(key_components)
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached features are still valid"""
        if cache_key not in self.feature_cache:
            return False
        
        cache_time = self.cache_timestamps.get(cache_key)
        if cache_time is None:
            return False
        
        age_seconds = (datetime.now() - cache_time).total_seconds()
        return age_seconds < self.cache_ttl_seconds
    
    def _clean_cache(self):
        """Clean expired cache entries"""
        current_time = datetime.now()
        expired_keys = []
        
        for key, timestamp in self.cache_timestamps.items():
            age_seconds = (current_time - timestamp).total_seconds()
            if age_seconds > self.cache_ttl_seconds:
                expired_keys.append(key)
        
        for key in expired_keys:
            self.feature_cache.pop(key, None)
            self.cache_timestamps.pop(key, None)
        
        if expired_keys:
            logger.debug(f"Cleaned {len(expired_keys)} expired cache entries")
    
    def clear_cache(self):
        """Clear all cached features"""
        self.feature_cache.clear()
        self.cache_timestamps.clear()
        logger.info("Feature cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            'cache_size': len(self.feature_cache),
            'cache_ttl_seconds': self.cache_ttl_seconds,
            'oldest_entry': min(self.cache_timestamps.values()) if self.cache_timestamps else None,
            'newest_entry': max(self.cache_timestamps.values()) if self.cache_timestamps else None
        }


class BaseFeatureExtractor:
    """Base class for feature extractors"""
    
    def extract(self, market_data: pd.DataFrame, regime_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Extract features from market data"""
        raise NotImplementedError


class MarketFeatureExtractor(BaseFeatureExtractor):
    """Extract basic market features"""
    
    def extract(self, market_data: pd.DataFrame, regime_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Extract market features"""
        if market_data is None or market_data.empty:
            return {}
        
        try:
            latest = market_data.iloc[-1]
            
            return {
                'current_price': float(latest.get('close', 0)),
                'price_change': float(latest.get('close', 0) - market_data.iloc[-2].get('close', 0)) if len(market_data) > 1 else 0,
                'price_change_pct': float((latest.get('close', 0) / market_data.iloc[-2].get('close', 1) - 1) * 100) if len(market_data) > 1 else 0,
                'volume': float(latest.get('volume', 0)),
                'high': float(latest.get('high', 0)),
                'low': float(latest.get('low', 0)),
                'open': float(latest.get('open', 0))
            }
        except Exception as e:
            logger.warning(f"Market feature extraction failed: {e}")
            return {}


class TechnicalFeatureExtractor(BaseFeatureExtractor):
    """Extract technical indicator features"""
    
    def extract(self, market_data: pd.DataFrame, regime_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Extract technical features"""
        if market_data is None or market_data.empty or len(market_data) < 20:
            return {}
        
        try:
            # Simple moving averages
            sma_5 = market_data['close'].rolling(5).mean().iloc[-1]
            sma_10 = market_data['close'].rolling(10).mean().iloc[-1]
            sma_20 = market_data['close'].rolling(20).mean().iloc[-1]
            
            # RSI (simplified)
            delta = market_data['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            return {
                'sma_5': float(sma_5) if not pd.isna(sma_5) else 0,
                'sma_10': float(sma_10) if not pd.isna(sma_10) else 0,
                'sma_20': float(sma_20) if not pd.isna(sma_20) else 0,
                'rsi': float(rsi.iloc[-1]) if not pd.isna(rsi.iloc[-1]) else 50,
                'price_above_sma_5': float(market_data['close'].iloc[-1] > sma_5) if not pd.isna(sma_5) else 0,
                'price_above_sma_10': float(market_data['close'].iloc[-1] > sma_10) if not pd.isna(sma_10) else 0,
                'price_above_sma_20': float(market_data['close'].iloc[-1] > sma_20) if not pd.isna(sma_20) else 0
            }
        except Exception as e:
            logger.warning(f"Technical feature extraction failed: {e}")
            return {}


class RegimeFeatureExtractor(BaseFeatureExtractor):
    """Extract regime-based features"""
    
    def extract(self, market_data: pd.DataFrame, regime_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Extract regime features"""
        if regime_context is None:
            return {}
        
        try:
            return {
                'regime_type': regime_context.get('regime_type', 'UNKNOWN'),
                'regime_confidence': float(regime_context.get('confidence', 0)),
                'volatility_regime': regime_context.get('volatility_regime', 'NORMAL'),
                'trend_regime': regime_context.get('trend_regime', 'NEUTRAL'),
                'regime_duration': float(regime_context.get('duration_minutes', 0))
            }
        except Exception as e:
            logger.warning(f"Regime feature extraction failed: {e}")
            return {}


class VolatilityFeatureExtractor(BaseFeatureExtractor):
    """Extract volatility features"""
    
    def extract(self, market_data: pd.DataFrame, regime_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Extract volatility features"""
        if market_data is None or market_data.empty or len(market_data) < 10:
            return {}
        
        try:
            # Calculate returns
            returns = market_data['close'].pct_change().dropna()
            
            # Volatility metrics
            vol_5 = returns.rolling(5).std() * np.sqrt(252)
            vol_10 = returns.rolling(10).std() * np.sqrt(252)
            vol_20 = returns.rolling(20).std() * np.sqrt(252) if len(returns) >= 20 else vol_10
            
            return {
                'volatility_5d': float(vol_5.iloc[-1]) if not pd.isna(vol_5.iloc[-1]) else 0,
                'volatility_10d': float(vol_10.iloc[-1]) if not pd.isna(vol_10.iloc[-1]) else 0,
                'volatility_20d': float(vol_20.iloc[-1]) if not pd.isna(vol_20.iloc[-1]) else 0,
                'volatility_ratio_5_10': float(vol_5.iloc[-1] / vol_10.iloc[-1]) if not pd.isna(vol_5.iloc[-1]) and not pd.isna(vol_10.iloc[-1]) and vol_10.iloc[-1] != 0 else 1,
                'current_return': float(returns.iloc[-1]) if len(returns) > 0 and not pd.isna(returns.iloc[-1]) else 0
            }
        except Exception as e:
            logger.warning(f"Volatility feature extraction failed: {e}")
            return {}


class VolumeFeatureExtractor(BaseFeatureExtractor):
    """Extract volume features"""
    
    def extract(self, market_data: pd.DataFrame, regime_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Extract volume features"""
        if market_data is None or market_data.empty or 'volume' not in market_data.columns:
            return {}
        
        try:
            volume = market_data['volume']
            avg_volume_5 = volume.rolling(5).mean()
            avg_volume_10 = volume.rolling(10).mean()
            
            return {
                'current_volume': float(volume.iloc[-1]),
                'avg_volume_5': float(avg_volume_5.iloc[-1]) if not pd.isna(avg_volume_5.iloc[-1]) else 0,
                'avg_volume_10': float(avg_volume_10.iloc[-1]) if not pd.isna(avg_volume_10.iloc[-1]) else 0,
                'volume_ratio_5': float(volume.iloc[-1] / avg_volume_5.iloc[-1]) if not pd.isna(avg_volume_5.iloc[-1]) and avg_volume_5.iloc[-1] != 0 else 1,
                'volume_ratio_10': float(volume.iloc[-1] / avg_volume_10.iloc[-1]) if not pd.isna(avg_volume_10.iloc[-1]) and avg_volume_10.iloc[-1] != 0 else 1
            }
        except Exception as e:
            logger.warning(f"Volume feature extraction failed: {e}")
            return {}


class OptionsFeatureExtractor(BaseFeatureExtractor):
    """Extract options-specific features"""
    
    def extract(self, market_data: pd.DataFrame, regime_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Extract options features"""
        # Placeholder for options-specific features
        # Will be enhanced when options data is available
        return {
            'iv_rank': 0.5,  # Placeholder
            'iv_percentile': 0.5,  # Placeholder
            'put_call_ratio': 1.0,  # Placeholder
            'gamma_exposure': 0.0  # Placeholder
        }

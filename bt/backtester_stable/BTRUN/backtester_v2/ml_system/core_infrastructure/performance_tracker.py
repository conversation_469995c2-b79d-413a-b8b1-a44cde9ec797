#!/usr/bin/env python3
"""
Performance Tracker
===================

Performance monitoring and tracking for ML system components.
Provides comprehensive metrics and monitoring capabilities.
"""

import logging
import time
import threading
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from collections import defaultdict, deque
import statistics

logger = logging.getLogger(__name__)


class PerformanceTracker:
    """
    ML system performance tracking and monitoring
    
    Tracks:
    1. Prediction latency and throughput
    2. Model accuracy and performance
    3. Cache hit rates and efficiency
    4. Error rates and failure patterns
    5. Resource utilization
    """
    
    def __init__(self, max_history_size: int = 10000):
        """Initialize performance tracker"""
        self.max_history_size = max_history_size
        
        # Performance metrics
        self.prediction_times = defaultdict(deque)
        self.prediction_counts = defaultdict(int)
        self.error_counts = defaultdict(int)
        self.cache_hits = defaultdict(int)
        self.cache_misses = defaultdict(int)
        
        # Accuracy tracking
        self.accuracy_history = defaultdict(deque)
        self.prediction_results = defaultdict(deque)
        
        # System metrics
        self.system_metrics = {
            'start_time': datetime.now(),
            'total_predictions': 0,
            'total_errors': 0,
            'total_cache_hits': 0,
            'total_cache_misses': 0
        }
        
        # Thread safety
        self.lock = threading.RLock()
        
        logger.info("✅ Performance Tracker initialized")
    
    def record_prediction(self, 
                         component: str,
                         latency_ms: float,
                         success: bool = True,
                         cache_hit: bool = False,
                         accuracy: Optional[float] = None) -> None:
        """
        Record a prediction event
        
        Args:
            component: Name of the ML component
            latency_ms: Prediction latency in milliseconds
            success: Whether the prediction was successful
            cache_hit: Whether this was a cache hit
            accuracy: Optional accuracy score for the prediction
        """
        with self.lock:
            # Record timing
            self.prediction_times[component].append(latency_ms)
            if len(self.prediction_times[component]) > self.max_history_size:
                self.prediction_times[component].popleft()
            
            # Record counts
            self.prediction_counts[component] += 1
            self.system_metrics['total_predictions'] += 1
            
            if not success:
                self.error_counts[component] += 1
                self.system_metrics['total_errors'] += 1
            
            # Record cache performance
            if cache_hit:
                self.cache_hits[component] += 1
                self.system_metrics['total_cache_hits'] += 1
            else:
                self.cache_misses[component] += 1
                self.system_metrics['total_cache_misses'] += 1
            
            # Record accuracy if provided
            if accuracy is not None:
                self.accuracy_history[component].append(accuracy)
                if len(self.accuracy_history[component]) > self.max_history_size:
                    self.accuracy_history[component].popleft()
    
    def record_prediction_result(self,
                               component: str,
                               predicted_value: Any,
                               actual_value: Any,
                               timestamp: Optional[datetime] = None) -> None:
        """
        Record prediction result for accuracy tracking
        
        Args:
            component: Name of the ML component
            predicted_value: Predicted value
            actual_value: Actual value
            timestamp: Timestamp of the prediction
        """
        with self.lock:
            result = {
                'predicted': predicted_value,
                'actual': actual_value,
                'timestamp': timestamp or datetime.now(),
                'correct': predicted_value == actual_value if isinstance(predicted_value, (int, str, bool)) else abs(predicted_value - actual_value) < 0.01
            }
            
            self.prediction_results[component].append(result)
            if len(self.prediction_results[component]) > self.max_history_size:
                self.prediction_results[component].popleft()
    
    def get_component_stats(self, component: str, time_window_minutes: Optional[int] = None) -> Dict[str, Any]:
        """
        Get performance statistics for a specific component
        
        Args:
            component: Name of the ML component
            time_window_minutes: Optional time window for recent stats
            
        Returns:
            Performance statistics
        """
        with self.lock:
            # Get timing data
            times = list(self.prediction_times[component])
            
            # Filter by time window if specified
            if time_window_minutes and self.prediction_results[component]:
                cutoff_time = datetime.now() - timedelta(minutes=time_window_minutes)
                recent_results = [r for r in self.prediction_results[component] if r['timestamp'] >= cutoff_time]
            else:
                recent_results = list(self.prediction_results[component])
            
            # Calculate basic stats
            stats = {
                'component': component,
                'total_predictions': self.prediction_counts[component],
                'total_errors': self.error_counts[component],
                'error_rate': self.error_counts[component] / max(self.prediction_counts[component], 1),
                'cache_hits': self.cache_hits[component],
                'cache_misses': self.cache_misses[component],
                'cache_hit_rate': self.cache_hits[component] / max(self.cache_hits[component] + self.cache_misses[component], 1)
            }
            
            # Timing statistics
            if times:
                stats.update({
                    'avg_latency_ms': statistics.mean(times),
                    'median_latency_ms': statistics.median(times),
                    'min_latency_ms': min(times),
                    'max_latency_ms': max(times),
                    'p95_latency_ms': self._percentile(times, 95),
                    'p99_latency_ms': self._percentile(times, 99)
                })
            else:
                stats.update({
                    'avg_latency_ms': 0,
                    'median_latency_ms': 0,
                    'min_latency_ms': 0,
                    'max_latency_ms': 0,
                    'p95_latency_ms': 0,
                    'p99_latency_ms': 0
                })
            
            # Accuracy statistics
            if recent_results:
                correct_predictions = sum(1 for r in recent_results if r['correct'])
                stats.update({
                    'recent_predictions': len(recent_results),
                    'recent_accuracy': correct_predictions / len(recent_results),
                    'recent_correct': correct_predictions,
                    'recent_incorrect': len(recent_results) - correct_predictions
                })
            else:
                stats.update({
                    'recent_predictions': 0,
                    'recent_accuracy': 0,
                    'recent_correct': 0,
                    'recent_incorrect': 0
                })
            
            # Historical accuracy
            accuracy_scores = list(self.accuracy_history[component])
            if accuracy_scores:
                stats.update({
                    'avg_accuracy': statistics.mean(accuracy_scores),
                    'min_accuracy': min(accuracy_scores),
                    'max_accuracy': max(accuracy_scores)
                })
            else:
                stats.update({
                    'avg_accuracy': 0,
                    'min_accuracy': 0,
                    'max_accuracy': 0
                })
            
            return stats
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get overall system performance statistics"""
        with self.lock:
            uptime = datetime.now() - self.system_metrics['start_time']
            
            # Calculate overall rates
            total_requests = self.system_metrics['total_predictions']
            total_cache_requests = self.system_metrics['total_cache_hits'] + self.system_metrics['total_cache_misses']
            
            stats = {
                'uptime_seconds': uptime.total_seconds(),
                'uptime_formatted': str(uptime),
                'total_predictions': total_requests,
                'total_errors': self.system_metrics['total_errors'],
                'overall_error_rate': self.system_metrics['total_errors'] / max(total_requests, 1),
                'total_cache_hits': self.system_metrics['total_cache_hits'],
                'total_cache_misses': self.system_metrics['total_cache_misses'],
                'overall_cache_hit_rate': self.system_metrics['total_cache_hits'] / max(total_cache_requests, 1),
                'predictions_per_second': total_requests / max(uptime.total_seconds(), 1),
                'active_components': len(self.prediction_counts),
                'components': list(self.prediction_counts.keys())
            }
            
            # Add component summaries
            component_summaries = {}
            for component in self.prediction_counts.keys():
                component_summaries[component] = {
                    'predictions': self.prediction_counts[component],
                    'errors': self.error_counts[component],
                    'error_rate': self.error_counts[component] / max(self.prediction_counts[component], 1)
                }
            
            stats['component_summaries'] = component_summaries
            
            return stats
    
    def get_performance_alerts(self, 
                             error_rate_threshold: float = 0.1,
                             latency_threshold_ms: float = 1000,
                             cache_hit_rate_threshold: float = 0.5) -> List[Dict[str, Any]]:
        """
        Get performance alerts based on thresholds
        
        Args:
            error_rate_threshold: Error rate threshold for alerts
            latency_threshold_ms: Latency threshold for alerts
            cache_hit_rate_threshold: Cache hit rate threshold for alerts
            
        Returns:
            List of performance alerts
        """
        alerts = []
        
        with self.lock:
            for component in self.prediction_counts.keys():
                stats = self.get_component_stats(component)
                
                # Check error rate
                if stats['error_rate'] > error_rate_threshold:
                    alerts.append({
                        'type': 'HIGH_ERROR_RATE',
                        'component': component,
                        'value': stats['error_rate'],
                        'threshold': error_rate_threshold,
                        'severity': 'HIGH' if stats['error_rate'] > error_rate_threshold * 2 else 'MEDIUM'
                    })
                
                # Check latency
                if stats['avg_latency_ms'] > latency_threshold_ms:
                    alerts.append({
                        'type': 'HIGH_LATENCY',
                        'component': component,
                        'value': stats['avg_latency_ms'],
                        'threshold': latency_threshold_ms,
                        'severity': 'HIGH' if stats['avg_latency_ms'] > latency_threshold_ms * 2 else 'MEDIUM'
                    })
                
                # Check cache hit rate
                if stats['cache_hit_rate'] < cache_hit_rate_threshold:
                    alerts.append({
                        'type': 'LOW_CACHE_HIT_RATE',
                        'component': component,
                        'value': stats['cache_hit_rate'],
                        'threshold': cache_hit_rate_threshold,
                        'severity': 'LOW'
                    })
        
        return alerts
    
    def reset_stats(self, component: Optional[str] = None) -> None:
        """
        Reset performance statistics
        
        Args:
            component: Specific component to reset, or None for all
        """
        with self.lock:
            if component:
                # Reset specific component
                self.prediction_times[component].clear()
                self.prediction_counts[component] = 0
                self.error_counts[component] = 0
                self.cache_hits[component] = 0
                self.cache_misses[component] = 0
                self.accuracy_history[component].clear()
                self.prediction_results[component].clear()
                logger.info(f"Performance stats reset for component: {component}")
            else:
                # Reset all
                self.prediction_times.clear()
                self.prediction_counts.clear()
                self.error_counts.clear()
                self.cache_hits.clear()
                self.cache_misses.clear()
                self.accuracy_history.clear()
                self.prediction_results.clear()
                self.system_metrics = {
                    'start_time': datetime.now(),
                    'total_predictions': 0,
                    'total_errors': 0,
                    'total_cache_hits': 0,
                    'total_cache_misses': 0
                }
                logger.info("All performance stats reset")
    
    def _percentile(self, data: List[float], percentile: float) -> float:
        """Calculate percentile of data"""
        if not data:
            return 0
        
        sorted_data = sorted(data)
        index = (percentile / 100) * (len(sorted_data) - 1)
        
        if index.is_integer():
            return sorted_data[int(index)]
        else:
            lower = sorted_data[int(index)]
            upper = sorted_data[int(index) + 1]
            return lower + (upper - lower) * (index - int(index))
    
    def export_stats(self, component: Optional[str] = None) -> Dict[str, Any]:
        """
        Export performance statistics for analysis
        
        Args:
            component: Specific component to export, or None for all
            
        Returns:
            Exported statistics
        """
        with self.lock:
            if component:
                return {
                    'component_stats': self.get_component_stats(component),
                    'export_time': datetime.now().isoformat()
                }
            else:
                export_data = {
                    'system_stats': self.get_system_stats(),
                    'component_stats': {},
                    'export_time': datetime.now().isoformat()
                }
                
                for comp in self.prediction_counts.keys():
                    export_data['component_stats'][comp] = self.get_component_stats(comp)
                
                return export_data

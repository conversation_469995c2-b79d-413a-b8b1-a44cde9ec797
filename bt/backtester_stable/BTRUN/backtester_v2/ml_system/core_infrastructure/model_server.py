#!/usr/bin/env python3
"""
Model Server
============

Real-time model serving infrastructure for ML predictions.
Provides fast, cached predictions with fallback mechanisms.
"""

import logging
import time
import threading
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


class ModelServer:
    """
    Real-time ML model serving infrastructure
    
    Provides:
    1. Fast model predictions with caching
    2. Model loading and management
    3. Performance monitoring
    4. Fallback mechanisms
    """
    
    def __init__(self, max_cache_size: int = 1000, cache_ttl_seconds: int = 60):
        """Initialize model server"""
        self.max_cache_size = max_cache_size
        self.cache_ttl_seconds = cache_ttl_seconds
        
        # Model storage
        self.loaded_models = {}
        self.model_metadata = {}
        
        # Prediction cache
        self.prediction_cache = {}
        self.cache_timestamps = {}
        self.cache_access_times = defaultdict(deque)
        
        # Performance tracking
        self.prediction_stats = defaultdict(lambda: {
            'count': 0,
            'total_time_ms': 0,
            'errors': 0,
            'cache_hits': 0
        })
        
        # Thread safety
        self.lock = threading.RLock()
        
        logger.info("✅ Model Server initialized")
    
    def load_model(self, model_name: str, model_path: str, model_metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Load a model for serving
        
        Args:
            model_name: Name of the model
            model_path: Path to the model file
            model_metadata: Optional metadata about the model
            
        Returns:
            True if loaded successfully, False otherwise
        """
        try:
            with self.lock:
                # Import here to avoid circular imports
                import joblib
                
                # Load the model
                model = joblib.load(model_path)
                
                # Store model and metadata
                self.loaded_models[model_name] = model
                self.model_metadata[model_name] = {
                    'path': model_path,
                    'loaded_at': datetime.now(),
                    'metadata': model_metadata or {},
                    'prediction_count': 0,
                    'last_used': None
                }
                
                logger.info(f"✅ Model loaded: {model_name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to load model {model_name}: {e}")
            return False
    
    def predict(self, model_name: str, features: Dict[str, Any], use_cache: bool = True) -> Optional[Dict[str, Any]]:
        """
        Make a prediction using a loaded model
        
        Args:
            model_name: Name of the model to use
            features: Input features for prediction
            use_cache: Whether to use cached predictions
            
        Returns:
            Prediction result or None if failed
        """
        start_time = time.time()
        
        try:
            with self.lock:
                # Check if model is loaded
                if model_name not in self.loaded_models:
                    logger.warning(f"Model not loaded: {model_name}")
                    return None
                
                # Generate cache key
                cache_key = self._generate_cache_key(model_name, features) if use_cache else None
                
                # Check cache
                if cache_key and self._is_cache_valid(cache_key):
                    self.prediction_stats[model_name]['cache_hits'] += 1
                    self.cache_access_times[cache_key].append(datetime.now())
                    logger.debug(f"Cache hit for model: {model_name}")
                    return self.prediction_cache[cache_key]
                
                # Get model
                model = self.loaded_models[model_name]
                
                # Prepare features
                feature_array = self._prepare_features(features)
                if feature_array is None:
                    return None
                
                # Make prediction
                prediction = model.predict(feature_array)
                
                # Get prediction probabilities if available
                probabilities = None
                if hasattr(model, 'predict_proba'):
                    try:
                        probabilities = model.predict_proba(feature_array)
                    except:
                        pass
                
                # Prepare result
                result = {
                    'prediction': prediction.tolist() if isinstance(prediction, np.ndarray) else prediction,
                    'probabilities': probabilities.tolist() if probabilities is not None else None,
                    'model_name': model_name,
                    'timestamp': datetime.now().isoformat(),
                    'features_used': list(features.keys())
                }
                
                # Cache result
                if cache_key:
                    self._cache_prediction(cache_key, result)
                
                # Update metadata
                self.model_metadata[model_name]['prediction_count'] += 1
                self.model_metadata[model_name]['last_used'] = datetime.now()
                
                return result
                
        except Exception as e:
            self.prediction_stats[model_name]['errors'] += 1
            logger.error(f"Prediction failed for model {model_name}: {e}")
            return None
            
        finally:
            # Update performance stats
            elapsed_ms = (time.time() - start_time) * 1000
            self.prediction_stats[model_name]['count'] += 1
            self.prediction_stats[model_name]['total_time_ms'] += elapsed_ms
    
    def batch_predict(self, model_name: str, features_list: List[Dict[str, Any]]) -> List[Optional[Dict[str, Any]]]:
        """
        Make batch predictions
        
        Args:
            model_name: Name of the model to use
            features_list: List of feature dictionaries
            
        Returns:
            List of prediction results
        """
        results = []
        for features in features_list:
            result = self.predict(model_name, features, use_cache=True)
            results.append(result)
        
        logger.info(f"Batch prediction completed: {model_name} - {len(features_list)} predictions")
        return results
    
    def _prepare_features(self, features: Dict[str, Any]) -> Optional[np.ndarray]:
        """Prepare features for model prediction"""
        try:
            # Convert to DataFrame for consistent handling
            df = pd.DataFrame([features])
            
            # Handle missing values
            df = df.fillna(0)
            
            # Convert to numpy array
            return df.values
            
        except Exception as e:
            logger.error(f"Feature preparation failed: {e}")
            return None
    
    def _generate_cache_key(self, model_name: str, features: Dict[str, Any]) -> str:
        """Generate cache key for prediction"""
        import hashlib
        import json
        
        # Create a deterministic string from features
        features_str = json.dumps(features, sort_keys=True)
        features_hash = hashlib.md5(features_str.encode()).hexdigest()[:8]
        
        return f"{model_name}_{features_hash}"
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached prediction is still valid"""
        if cache_key not in self.prediction_cache:
            return False
        
        cache_time = self.cache_timestamps.get(cache_key)
        if cache_time is None:
            return False
        
        age_seconds = (datetime.now() - cache_time).total_seconds()
        return age_seconds < self.cache_ttl_seconds
    
    def _cache_prediction(self, cache_key: str, result: Dict[str, Any]):
        """Cache a prediction result"""
        # Check cache size limit
        if len(self.prediction_cache) >= self.max_cache_size:
            self._evict_oldest_cache_entry()
        
        # Cache the result
        self.prediction_cache[cache_key] = result
        self.cache_timestamps[cache_key] = datetime.now()
        self.cache_access_times[cache_key].append(datetime.now())
    
    def _evict_oldest_cache_entry(self):
        """Evict the oldest cache entry"""
        if not self.cache_timestamps:
            return
        
        # Find oldest entry
        oldest_key = min(self.cache_timestamps.keys(), key=lambda k: self.cache_timestamps[k])
        
        # Remove from all caches
        self.prediction_cache.pop(oldest_key, None)
        self.cache_timestamps.pop(oldest_key, None)
        self.cache_access_times.pop(oldest_key, None)
    
    def clear_cache(self, model_name: Optional[str] = None):
        """Clear prediction cache"""
        with self.lock:
            if model_name:
                # Clear cache for specific model
                keys_to_remove = [k for k in self.prediction_cache.keys() if k.startswith(f"{model_name}_")]
                for key in keys_to_remove:
                    self.prediction_cache.pop(key, None)
                    self.cache_timestamps.pop(key, None)
                    self.cache_access_times.pop(key, None)
                logger.info(f"Cache cleared for model: {model_name}")
            else:
                # Clear all cache
                self.prediction_cache.clear()
                self.cache_timestamps.clear()
                self.cache_access_times.clear()
                logger.info("All prediction cache cleared")
    
    def get_model_stats(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get statistics for a specific model"""
        if model_name not in self.loaded_models:
            return None
        
        stats = self.prediction_stats[model_name].copy()
        metadata = self.model_metadata[model_name].copy()
        
        # Calculate average prediction time
        if stats['count'] > 0:
            stats['avg_time_ms'] = stats['total_time_ms'] / stats['count']
            stats['cache_hit_rate'] = stats['cache_hits'] / stats['count']
        else:
            stats['avg_time_ms'] = 0
            stats['cache_hit_rate'] = 0
        
        return {
            'model_name': model_name,
            'metadata': metadata,
            'performance': stats
        }
    
    def get_server_stats(self) -> Dict[str, Any]:
        """Get overall server statistics"""
        with self.lock:
            return {
                'loaded_models': len(self.loaded_models),
                'cache_size': len(self.prediction_cache),
                'max_cache_size': self.max_cache_size,
                'cache_ttl_seconds': self.cache_ttl_seconds,
                'models': list(self.loaded_models.keys()),
                'total_predictions': sum(stats['count'] for stats in self.prediction_stats.values()),
                'total_errors': sum(stats['errors'] for stats in self.prediction_stats.values()),
                'total_cache_hits': sum(stats['cache_hits'] for stats in self.prediction_stats.values())
            }
    
    def unload_model(self, model_name: str) -> bool:
        """Unload a model from memory"""
        try:
            with self.lock:
                if model_name in self.loaded_models:
                    del self.loaded_models[model_name]
                    del self.model_metadata[model_name]
                    
                    # Clear related cache
                    self.clear_cache(model_name)
                    
                    logger.info(f"Model unloaded: {model_name}")
                    return True
                else:
                    logger.warning(f"Model not loaded: {model_name}")
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to unload model {model_name}: {e}")
            return False
    
    def is_model_loaded(self, model_name: str) -> bool:
        """Check if a model is loaded"""
        return model_name in self.loaded_models

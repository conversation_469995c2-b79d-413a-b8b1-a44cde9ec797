#!/usr/bin/env python3
"""
ML System Core
==============

Core ML system that coordinates all ML capabilities for high-value use cases only.
No strategy pollution - only genuine ML use cases.
"""

import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime
import pandas as pd

logger = logging.getLogger(__name__)


class MLSystemCore:
    """
    Core ML system for focused, high-value ML capabilities
    
    Coordinates:
    1. ML_INDICATOR Enhancement
    2. Dedicated ML Straddle System
    3. Strategy Consolidator Intelligence
    4. Market Regime ML Enhancement (optional)
    """
    
    def __init__(self):
        """Initialize core ML system"""
        self.enabled = True
        self.fallback_on_error = True
        self.max_latency_ms = 100
        self.performance_tracker = None
        
        # ML components (lazy loaded)
        self._ml_indicator_enhancer = None
        self._straddle_system = None
        self._consolidator_intelligence = None
        self._regime_enhancer = None
        
        # Performance tracking
        self.prediction_count = 0
        self.error_count = 0
        self.total_latency_ms = 0
        
        logger.info("✅ ML System Core initialized - Focused approach")
    
    def get_ml_insights(self, context_type: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Get ML insights only where they add real value
        
        Args:
            context_type: Type of ML context (ML_INDICATOR, STRADDLE_PREDICTION, etc.)
            data: Input data for ML processing
            
        Returns:
            ML insights if applicable, None otherwise
        """
        start_time = time.time()
        
        try:
            if not self.enabled:
                return None
            
            # Route to appropriate ML component
            if context_type == "ML_INDICATOR_ENHANCEMENT":
                return self._get_ml_indicator_insights(data)
            elif context_type == "STRADDLE_PREDICTION":
                return self._get_straddle_insights(data)
            elif context_type == "STRATEGY_SELECTION":
                return self._get_consolidator_insights(data)
            elif context_type == "REGIME_ENHANCEMENT":
                return self._get_regime_insights(data)
            else:
                # No ML enhancement for other contexts
                logger.debug(f"No ML enhancement available for context: {context_type}")
                return None
                
        except Exception as e:
            self.error_count += 1
            logger.warning(f"ML prediction failed for {context_type}: {e}")
            
            if self.fallback_on_error:
                return None
            else:
                raise
                
        finally:
            # Track performance
            latency_ms = (time.time() - start_time) * 1000
            self.total_latency_ms += latency_ms
            self.prediction_count += 1
            
            if latency_ms > self.max_latency_ms:
                logger.warning(f"ML prediction latency {latency_ms:.1f}ms exceeds limit {self.max_latency_ms}ms")
    
    def _get_ml_indicator_insights(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get ML_INDICATOR enhancement insights"""
        if self._ml_indicator_enhancer is None:
            try:
                import sys
                import os
                # Add the ml_system directory to path
                ml_system_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                if ml_system_dir not in sys.path:
                    sys.path.insert(0, ml_system_dir)

                from ml_indicator_enhancement.ml_indicator_enhancer import MLIndicatorEnhancer
                self._ml_indicator_enhancer = MLIndicatorEnhancer()
            except ImportError as e:
                logger.error(f"Failed to import MLIndicatorEnhancer: {e}")
                return None

        return self._ml_indicator_enhancer.enhance_signals(data)
    
    def _get_straddle_insights(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get dedicated straddle ML insights"""
        if self._straddle_system is None:
            logger.info("Dedicated straddle system not yet implemented")
            return None

        return self._straddle_system.predict_opportunities(data)

    def _get_consolidator_insights(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get strategy consolidator ML insights"""
        if self._consolidator_intelligence is None:
            logger.info("Consolidator intelligence not yet implemented")
            return None

        return self._consolidator_intelligence.analyze_strategies(data)

    def _get_regime_insights(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get market regime ML insights"""
        if self._regime_enhancer is None:
            logger.info("Regime ML enhancement not yet implemented")
            return None

        return self._regime_enhancer.enhance_regime_detection(data)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get ML system performance statistics"""
        if self.prediction_count == 0:
            return {
                'predictions': 0,
                'errors': 0,
                'error_rate': 0.0,
                'avg_latency_ms': 0.0
            }
        
        return {
            'predictions': self.prediction_count,
            'errors': self.error_count,
            'error_rate': self.error_count / self.prediction_count,
            'avg_latency_ms': self.total_latency_ms / self.prediction_count,
            'total_latency_ms': self.total_latency_ms
        }
    
    def reset_stats(self):
        """Reset performance statistics"""
        self.prediction_count = 0
        self.error_count = 0
        self.total_latency_ms = 0
        logger.info("ML system performance stats reset")
    
    def enable(self):
        """Enable ML system"""
        self.enabled = True
        logger.info("✅ ML System enabled")
    
    def disable(self):
        """Disable ML system"""
        self.enabled = False
        logger.info("❌ ML System disabled")
    
    def is_healthy(self) -> bool:
        """Check if ML system is healthy"""
        if not self.enabled:
            return False
        
        if self.prediction_count == 0:
            return True
        
        # Check error rate
        error_rate = self.error_count / self.prediction_count
        if error_rate > 0.1:  # More than 10% errors
            return False
        
        # Check average latency
        avg_latency = self.total_latency_ms / self.prediction_count
        if avg_latency > self.max_latency_ms * 2:  # More than 2x expected latency
            return False
        
        return True
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive ML system status"""
        return {
            'enabled': self.enabled,
            'healthy': self.is_healthy(),
            'performance': self.get_performance_stats(),
            'components': {
                'ml_indicator_enhancer': self._ml_indicator_enhancer is not None,
                'straddle_system': self._straddle_system is not None,
                'consolidator_intelligence': self._consolidator_intelligence is not None,
                'regime_enhancer': self._regime_enhancer is not None
            },
            'timestamp': datetime.now().isoformat()
        }


# Global ML system instance
_ml_system_instance = None

def get_ml_system() -> MLSystemCore:
    """Get global ML system instance"""
    global _ml_system_instance
    if _ml_system_instance is None:
        _ml_system_instance = MLSystemCore()
    return _ml_system_instance

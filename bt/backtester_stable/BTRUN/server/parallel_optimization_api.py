#!/usr/bin/env python3
"""
Parallel Optimization API
=========================

FastAPI endpoints for parallel optimization and backtesting:
- Start/stop/pause optimization runs
- Real-time progress tracking
- Resource monitoring
- Results export
"""

import asyncio
import logging
import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from fastapi import FastAPI, HTTPException, BackgroundTasks, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel
import pandas as pd
import numpy as np

# Import optimization components
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../backtester_v2"))

from strategy_optimizer.optimization_engine import OptimizationEngine, OptimizationConfig, OptimizationResult
from strategy_optimizer.parallel_optimizer import ParallelOptimizer

logger = logging.getLogger(__name__)

# Pydantic models for API
class OptimizationRequest(BaseModel):
    strategy_type: str
    optimization_type: str
    parallel_workers: int = 4
    max_iterations: int = 100
    algorithm: str = "bayesian"
    parameter_bounds: Optional[Dict[str, List[float]]] = None
    objectives: Optional[List[str]] = None

class OptimizationStatus(BaseModel):
    optimization_id: str
    status: str
    progress: float
    current_iteration: int
    total_iterations: int
    best_score: Optional[float]
    elapsed_time: float
    eta_seconds: Optional[float]
    worker_status: List[Dict[str, Any]]

class ResourceUsage(BaseModel):
    cpu_usage: float
    memory_usage: float
    gpu_usage: float
    worker_utilization: float
    throughput_per_minute: float

# Global state management
class OptimizationManager:
    """Manages active optimizations and their state"""
    
    def __init__(self):
        self.active_optimizations = {}
        self.optimization_engine = OptimizationEngine()
        self.websocket_connections = set()
        
    async def start_optimization(
        self,
        request: OptimizationRequest,
        optimization_id: str
    ) -> str:
        """Start a new optimization run"""
        
        try:
            # Create optimization config
            config = OptimizationConfig(
                strategy_type=request.strategy_type,
                optimization_type=request.optimization_type,
                parallel_workers=request.parallel_workers,
                max_iterations=request.max_iterations,
                parameter_bounds=request.parameter_bounds or self._get_default_parameter_bounds(request.strategy_type),
                objectives=request.objectives or ['sharpe_ratio', 'max_drawdown', 'win_rate']
            )
            
            # Create objective function
            objective_function = self._create_objective_function(request.strategy_type)
            
            # Progress callback for real-time updates
            async def progress_callback(progress_data):
                await self._broadcast_progress_update(optimization_id, progress_data)
            
            # Store optimization info
            self.active_optimizations[optimization_id] = {
                'config': config,
                'status': 'running',
                'start_time': datetime.now(),
                'progress': 0.0,
                'current_iteration': 0,
                'best_score': None,
                'result': None
            }
            
            # Start optimization in background
            asyncio.create_task(self._run_optimization(
                optimization_id, config, objective_function, progress_callback
            ))
            
            return optimization_id
            
        except Exception as e:
            logger.error(f"Failed to start optimization: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def _run_optimization(
        self,
        optimization_id: str,
        config: OptimizationConfig,
        objective_function,
        progress_callback
    ):
        """Run optimization in background"""
        
        try:
            # Run optimization
            result = await self.optimization_engine.optimize_strategy(
                config, objective_function, progress_callback
            )
            
            # Update state
            if optimization_id in self.active_optimizations:
                self.active_optimizations[optimization_id]['status'] = 'completed'
                self.active_optimizations[optimization_id]['result'] = result
                self.active_optimizations[optimization_id]['progress'] = 1.0
            
            # Broadcast completion
            await self._broadcast_completion(optimization_id, result)
            
        except Exception as e:
            logger.error(f"Optimization {optimization_id} failed: {e}")
            if optimization_id in self.active_optimizations:
                self.active_optimizations[optimization_id]['status'] = 'failed'
                self.active_optimizations[optimization_id]['error'] = str(e)
    
    def _create_objective_function(self, strategy_type: str):
        """Create objective function for strategy type"""
        
        def objective_function(parameters: Dict[str, Any]) -> Dict[str, float]:
            """Mock objective function - in production this would run actual backtests"""
            
            # Simulate backtesting with random results influenced by parameters
            base_score = np.random.normal(1.5, 0.5)
            
            # Parameter influence (mock)
            if 'stop_loss' in parameters:
                base_score += (0.02 - float(parameters['stop_loss'])) * 10  # Prefer smaller stop loss
            
            if 'take_profit' in parameters:
                base_score += (float(parameters['take_profit']) - 0.03) * 5  # Prefer larger take profit
            
            # Simulate other metrics
            sharpe_ratio = max(0.1, base_score + np.random.normal(0, 0.2))
            max_drawdown = max(0.01, min(0.5, 0.15 - sharpe_ratio * 0.05 + np.random.normal(0, 0.02)))
            win_rate = max(0.3, min(0.8, 0.5 + sharpe_ratio * 0.1 + np.random.normal(0, 0.05)))
            profit_factor = max(1.0, sharpe_ratio * 0.8 + np.random.normal(0, 0.1))
            
            return {
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'profit_factor': profit_factor,
                'composite_score': sharpe_ratio  # Primary optimization target
            }
        
        return objective_function
    
    def _get_default_parameter_bounds(self, strategy_type: str) -> Dict[str, List[float]]:
        """Get default parameter bounds for strategy type"""
        
        bounds = {
            'TBS': {
                'stop_loss': [0.01, 0.05],
                'take_profit': [0.02, 0.1],
                'position_size': [0.01, 0.05],
                'lookback_period': [5, 30],
                'threshold': [0.005, 0.02]
            },
            'TV': {
                'stop_loss': [0.005, 0.03],
                'take_profit': [0.01, 0.08],
                'position_size': [0.005, 0.03],
                'signal_threshold': [0.6, 0.9],
                'exit_threshold': [0.3, 0.7]
            },
            'OI': {
                'oi_threshold': [0.1, 0.5],
                'volume_threshold': [1.2, 3.0],
                'strike_shift': [0, 5],
                'time_decay_factor': [0.8, 1.2],
                'position_size': [0.01, 0.04]
            },
            'ML_INDICATOR': {
                'confidence_threshold': [0.6, 0.9],
                'feature_importance_cutoff': [0.1, 0.3],
                'ensemble_weight': [0.3, 0.8],
                'risk_adjustment': [0.5, 1.5],
                'position_size': [0.005, 0.025]
            }
        }
        
        return bounds.get(strategy_type, bounds['TBS'])
    
    async def _broadcast_progress_update(self, optimization_id: str, progress_data: Dict[str, Any]):
        """Broadcast progress update to connected WebSocket clients"""
        
        if optimization_id in self.active_optimizations:
            # Update stored progress
            opt_data = self.active_optimizations[optimization_id]
            opt_data['progress'] = progress_data.get('progress', 0.0)
            opt_data['current_iteration'] = progress_data.get('iteration', 0)
            opt_data['best_score'] = progress_data.get('best_score')
            
            # Broadcast to WebSocket clients
            message = {
                'type': 'progress_update',
                'optimization_id': optimization_id,
                'data': progress_data
            }
            
            await self._broadcast_to_websockets(message)
    
    async def _broadcast_completion(self, optimization_id: str, result: OptimizationResult):
        """Broadcast optimization completion"""
        
        message = {
            'type': 'optimization_complete',
            'optimization_id': optimization_id,
            'result': {
                'best_parameters': result.best_parameters,
                'best_performance': result.best_performance,
                'iterations_completed': result.iterations_completed,
                'execution_time': result.execution_time
            }
        }
        
        await self._broadcast_to_websockets(message)
    
    async def _broadcast_to_websockets(self, message: Dict[str, Any]):
        """Broadcast message to all connected WebSocket clients"""
        
        if not self.websocket_connections:
            return
        
        message_str = json.dumps(message)
        disconnected = set()
        
        for websocket in self.websocket_connections:
            try:
                await websocket.send_text(message_str)
            except Exception:
                disconnected.add(websocket)
        
        # Remove disconnected clients
        self.websocket_connections -= disconnected
    
    def get_optimization_status(self, optimization_id: str) -> Optional[Dict[str, Any]]:
        """Get status of specific optimization"""
        return self.active_optimizations.get(optimization_id)
    
    def get_all_optimizations(self) -> Dict[str, Any]:
        """Get status of all optimizations"""
        return self.active_optimizations
    
    async def stop_optimization(self, optimization_id: str) -> bool:
        """Stop running optimization"""
        if optimization_id in self.active_optimizations:
            self.active_optimizations[optimization_id]['status'] = 'stopped'
            return True
        return False
    
    async def pause_optimization(self, optimization_id: str) -> bool:
        """Pause running optimization"""
        if optimization_id in self.active_optimizations:
            current_status = self.active_optimizations[optimization_id]['status']
            if current_status == 'running':
                self.active_optimizations[optimization_id]['status'] = 'paused'
            elif current_status == 'paused':
                self.active_optimizations[optimization_id]['status'] = 'running'
            return True
        return False
    
    def add_websocket_connection(self, websocket: WebSocket):
        """Add WebSocket connection"""
        self.websocket_connections.add(websocket)
    
    def remove_websocket_connection(self, websocket: WebSocket):
        """Remove WebSocket connection"""
        self.websocket_connections.discard(websocket)

# Global optimization manager
optimization_manager = OptimizationManager()

# FastAPI app
app = FastAPI(title="Parallel Optimization API", version="1.0.0")

@app.post("/api/optimization/start")
async def start_optimization(request: OptimizationRequest, background_tasks: BackgroundTasks):
    """Start a new optimization run"""
    
    optimization_id = str(uuid.uuid4())
    
    try:
        await optimization_manager.start_optimization(request, optimization_id)
        
        return JSONResponse({
            "optimization_id": optimization_id,
            "status": "started",
            "message": "Optimization started successfully"
        })
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/optimization/{optimization_id}/stop")
async def stop_optimization(optimization_id: str):
    """Stop running optimization"""
    
    success = await optimization_manager.stop_optimization(optimization_id)
    
    if success:
        return JSONResponse({"status": "stopped", "message": "Optimization stopped"})
    else:
        raise HTTPException(status_code=404, detail="Optimization not found")

@app.post("/api/optimization/{optimization_id}/pause")
async def pause_optimization(optimization_id: str):
    """Pause/resume optimization"""
    
    success = await optimization_manager.pause_optimization(optimization_id)
    
    if success:
        return JSONResponse({"status": "toggled", "message": "Optimization status toggled"})
    else:
        raise HTTPException(status_code=404, detail="Optimization not found")

@app.get("/api/optimization/{optimization_id}/status")
async def get_optimization_status(optimization_id: str):
    """Get optimization status"""
    
    status = optimization_manager.get_optimization_status(optimization_id)
    
    if status:
        return JSONResponse(status)
    else:
        raise HTTPException(status_code=404, detail="Optimization not found")

@app.get("/api/optimization/list")
async def list_optimizations():
    """List all optimizations"""
    
    return JSONResponse(optimization_manager.get_all_optimizations())

@app.get("/api/system/resources")
async def get_resource_usage():
    """Get system resource usage"""
    
    # Mock resource usage - in production this would use psutil or similar
    import random
    
    return JSONResponse({
        "cpu_usage": random.uniform(30, 80),
        "memory_usage": random.uniform(40, 70),
        "gpu_usage": random.uniform(20, 90),
        "worker_utilization": random.uniform(0.5, 1.0),
        "throughput_per_minute": random.uniform(5, 20),
        "timestamp": datetime.now().isoformat()
    })

@app.websocket("/ws/optimization")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates"""
    
    await websocket.accept()
    optimization_manager.add_websocket_connection(websocket)
    
    try:
        while True:
            # Keep connection alive
            await websocket.receive_text()
    except WebSocketDisconnect:
        optimization_manager.remove_websocket_connection(websocket)

@app.get("/api/optimization/{optimization_id}/export/{format}")
async def export_optimization_results(optimization_id: str, format: str):
    """Export optimization results"""
    
    status = optimization_manager.get_optimization_status(optimization_id)
    
    if not status or not status.get('result'):
        raise HTTPException(status_code=404, detail="Optimization results not found")
    
    result = status['result']
    
    if format == 'json':
        return JSONResponse(result.dict() if hasattr(result, 'dict') else result)
    elif format == 'csv':
        # Convert to CSV format
        df = pd.DataFrame([result.best_parameters])
        csv_content = df.to_csv(index=False)
        
        return JSONResponse({
            "content": csv_content,
            "filename": f"optimization_{optimization_id}.csv"
        })
    else:
        raise HTTPException(status_code=400, detail="Unsupported export format")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)

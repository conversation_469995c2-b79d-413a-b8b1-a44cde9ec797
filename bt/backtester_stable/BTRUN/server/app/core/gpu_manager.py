#!/usr/bin/env python3
"""
GPU Manager
===========

GPU resource management and monitoring.
"""

import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class GPUManager:
    """GPU resource manager"""
    
    def __init__(self):
        self.initialized = False
        self.gpu_available = False
        self.gpu_info = {}
    
    async def initialize(self):
        """Initialize GPU manager"""
        try:
            # Check for GPU availability
            self.gpu_available = self._check_gpu_availability()
            
            if self.gpu_available:
                self.gpu_info = self._get_gpu_info()
                logger.info(f"✅ GPU Manager initialized - GPU available: {self.gpu_available}")
            else:
                logger.info("✅ GPU Manager initialized - No GPU available, using CPU")
            
            self.initialized = True
            
        except Exception as e:
            logger.error(f"GPU Manager initialization failed: {e}")
            self.gpu_available = False
            self.initialized = True
    
    def _check_gpu_availability(self) -> bool:
        """Check if GPU is available"""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            try:
                import cupy
                return True
            except ImportError:
                return False
    
    def _get_gpu_info(self) -> Dict[str, Any]:
        """Get GPU information"""
        info = {
            "available": self.gpu_available,
            "device_count": 0,
            "devices": []
        }
        
        try:
            import torch
            if torch.cuda.is_available():
                info["device_count"] = torch.cuda.device_count()
                for i in range(info["device_count"]):
                    device_info = {
                        "id": i,
                        "name": torch.cuda.get_device_name(i),
                        "memory_total": torch.cuda.get_device_properties(i).total_memory,
                        "memory_allocated": torch.cuda.memory_allocated(i),
                        "memory_cached": torch.cuda.memory_reserved(i)
                    }
                    info["devices"].append(device_info)
        except Exception as e:
            logger.warning(f"Failed to get GPU info: {e}")
        
        return info
    
    async def cleanup(self):
        """Cleanup GPU resources"""
        try:
            if self.gpu_available:
                # Clear GPU cache if available
                try:
                    import torch
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                except ImportError:
                    pass
            
            logger.info("GPU Manager cleanup completed")
            
        except Exception as e:
            logger.error(f"GPU Manager cleanup failed: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get GPU manager status"""
        return {
            "initialized": self.initialized,
            "gpu_available": self.gpu_available,
            "gpu_info": self.gpu_info
        }
    
    def is_gpu_available(self) -> bool:
        """Check if GPU is available for use"""
        return self.initialized and self.gpu_available

#!/usr/bin/env python3
"""
Redis Configuration
===================

Redis connection and management for caching and session storage.
"""

import logging
from typing import Optional

logger = logging.getLogger(__name__)

# Mock Redis implementation for now
class MockRedis:
    def __init__(self):
        self.data = {}
    
    async def get(self, key: str):
        return self.data.get(key)
    
    async def set(self, key: str, value: str, ex: Optional[int] = None):
        self.data[key] = value
    
    async def delete(self, key: str):
        self.data.pop(key, None)
    
    async def close(self):
        pass

_redis_client: Optional[MockRedis] = None

async def init_redis():
    """Initialize Redis connection"""
    global _redis_client
    try:
        _redis_client = MockRedis()
        logger.info("✅ Redis (mock) initialized")
    except Exception as e:
        logger.error(f"Redis initialization failed: {e}")
        _redis_client = MockRedis()  # Fallback to mock

async def close_redis():
    """Close Redis connection"""
    global _redis_client
    if _redis_client:
        await _redis_client.close()
        _redis_client = None
        logger.info("Redis connection closed")

def get_redis() -> MockRedis:
    """Get Redis client"""
    global _redis_client
    if _redis_client is None:
        _redis_client = MockRedis()
    return _redis_client

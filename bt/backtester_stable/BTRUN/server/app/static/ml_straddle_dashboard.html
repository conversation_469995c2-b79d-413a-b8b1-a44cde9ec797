<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ML Straddle System Dashboard - Enhanced GPU Backtester</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .straddle-card {
            transition: transform 0.2s, box-shadow 0.2s;
            border-left: 4px solid #007bff;
        }
        .straddle-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .straddle-atm { border-left-color: #28a745; }
        .straddle-itm { border-left-color: #17a2b8; }
        .straddle-otm { border-left-color: #ffc107; }
        .straddle-triple { border-left-color: #dc3545; }
        
        .position-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        
        .pnl-positive { color: #28a745; font-weight: bold; }
        .pnl-negative { color: #dc3545; font-weight: bold; }
        .pnl-neutral { color: #6c757d; font-weight: bold; }
        
        .confidence-meter {
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #dc3545, #ffc107, #28a745);
            transition: width 0.3s ease;
        }
        
        .iv-indicator {
            font-size: 0.9em;
            padding: 2px 8px;
            border-radius: 12px;
        }
        
        .iv-low { background-color: #d4edda; color: #155724; }
        .iv-normal { background-color: #fff3cd; color: #856404; }
        .iv-high { background-color: #f8d7da; color: #721c24; }
        
        .strategy-status {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            border-radius: 5px;
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
        }
        
        .signal-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .signal-buy { background-color: #28a745; }
        .signal-sell { background-color: #dc3545; }
        .signal-hold { background-color: #6c757d; }
    </style>
</head>
<body class="bg-light">
    <!-- Header -->
    <div class="strategy-status">
        <i class="fas fa-chart-line me-2"></i>ML STRADDLE SYSTEM - Phase 2 Implementation
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-brain me-2"></i>ML Straddle Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="ml_dashboard.html">
                    <i class="fas fa-arrow-left me-1"></i>Back to ML System
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Strategy Overview Row -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card straddle-card straddle-atm">
                    <div class="card-body text-center">
                        <i class="fas fa-bullseye fa-2x text-success mb-2"></i>
                        <h6 class="card-title">ATM Straddle</h6>
                        <div class="d-flex justify-content-between">
                            <small>Active:</small>
                            <span class="fw-bold" id="atmActivePositions">0</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <small>PnL:</small>
                            <span class="fw-bold pnl-neutral" id="atmPnL">$0.00</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card straddle-card straddle-itm">
                    <div class="card-body text-center">
                        <i class="fas fa-arrow-down fa-2x text-info mb-2"></i>
                        <h6 class="card-title">ITM Straddle</h6>
                        <div class="d-flex justify-content-between">
                            <small>Active:</small>
                            <span class="fw-bold" id="itmActivePositions">0</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <small>PnL:</small>
                            <span class="fw-bold pnl-neutral" id="itmPnL">$0.00</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card straddle-card straddle-otm">
                    <div class="card-body text-center">
                        <i class="fas fa-arrow-up fa-2x text-warning mb-2"></i>
                        <h6 class="card-title">OTM Straddle</h6>
                        <div class="d-flex justify-content-between">
                            <small>Active:</small>
                            <span class="fw-bold" id="otmActivePositions">0</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <small>PnL:</small>
                            <span class="fw-bold pnl-neutral" id="otmPnL">$0.00</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card straddle-card straddle-triple">
                    <div class="card-body text-center">
                        <i class="fas fa-layer-group fa-2x text-danger mb-2"></i>
                        <h6 class="card-title">Triple Straddle</h6>
                        <div class="d-flex justify-content-between">
                            <small>Active:</small>
                            <span class="fw-bold" id="tripleActivePositions">0</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <small>PnL:</small>
                            <span class="fw-bold pnl-neutral" id="triplePnL">$0.00</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Real-time Signals Row -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-signal me-2"></i>Real-time ML Signals
                        </h5>
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshSignals()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row" id="signalsContainer">
                            <!-- ATM Signal -->
                            <div class="col-md-6 mb-3">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <i class="fas fa-bullseye me-2"></i>ATM Straddle Signal
                                    </div>
                                    <div class="card-body">
                                        <div class="d-flex align-items-center mb-2">
                                            <span class="signal-indicator signal-hold" id="atmSignalIndicator"></span>
                                            <span class="fw-bold" id="atmSignalAction">HOLD</span>
                                            <span class="ms-auto text-muted" id="atmSignalTime">--:--</span>
                                        </div>
                                        <div class="mb-2">
                                            <small class="text-muted">Confidence:</small>
                                            <div class="confidence-meter">
                                                <div class="confidence-fill" id="atmConfidenceFill" style="width: 0%"></div>
                                            </div>
                                            <small class="text-muted" id="atmConfidenceText">0%</small>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <small>Entry: <span id="atmEntryPrice">--</span></small>
                                            <small>Target: <span id="atmTargetPrice">--</span></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- ITM Signal -->
                            <div class="col-md-6 mb-3">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <i class="fas fa-arrow-down me-2"></i>ITM Straddle Signal
                                    </div>
                                    <div class="card-body">
                                        <div class="d-flex align-items-center mb-2">
                                            <span class="signal-indicator signal-hold" id="itmSignalIndicator"></span>
                                            <span class="fw-bold" id="itmSignalAction">HOLD</span>
                                            <span class="ms-auto text-muted" id="itmSignalTime">--:--</span>
                                        </div>
                                        <div class="mb-2">
                                            <small class="text-muted">Confidence:</small>
                                            <div class="confidence-meter">
                                                <div class="confidence-fill" id="itmConfidenceFill" style="width: 0%"></div>
                                            </div>
                                            <small class="text-muted" id="itmConfidenceText">0%</small>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <small>Entry: <span id="itmEntryPrice">--</span></small>
                                            <small>Target: <span id="itmTargetPrice">--</span></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Market Context -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>Market Context
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <small>Current Price:</small>
                                <span class="fw-bold" id="currentPrice">--</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <small>Current IV:</small>
                                <span class="iv-indicator iv-normal" id="currentIV">--</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <small>Volume Ratio:</small>
                                <span class="fw-bold" id="volumeRatio">--</span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <small class="text-muted">Market Regime:</small>
                            <div class="fw-bold" id="marketRegime">Loading...</div>
                            <div class="progress mt-1" style="height: 5px;">
                                <div class="progress-bar" id="regimeConfidence" style="width: 0%"></div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <small class="text-muted">Time to Expiry:</small>
                            <div class="fw-bold" id="timeToExpiry">-- minutes</div>
                        </div>
                        
                        <button class="btn btn-primary btn-sm w-100" onclick="generateTestSignal()">
                            <i class="fas fa-play me-2"></i>Generate Test Signal
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Positions Row -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>Active Positions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="activePositionsContainer">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-3x mb-3"></i>
                                <p>No active positions</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Charts Row -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>PnL Trends
                        </h6>
                    </div>
                    <div class="card-body">
                        <canvas id="pnlChart" height="200"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-pie me-2"></i>Strategy Distribution
                        </h6>
                    </div>
                    <div class="card-body">
                        <canvas id="strategyChart" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alerts -->
        <div id="alertContainer"></div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- ML Straddle Dashboard JavaScript -->
    <script>
        // ML Straddle Dashboard
        let pnlChart = null;
        let strategyChart = null;
        const API_BASE_URL = window.location.origin;
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeStraddleDashboard();
        });
        
        async function initializeStraddleDashboard() {
            console.log('🚀 Initializing ML Straddle Dashboard...');
            
            // Initialize charts
            initializePnLChart();
            initializeStrategyChart();
            
            // Load initial data
            await loadMarketContext();
            await refreshSignals();
            
            // Start real-time updates
            setInterval(refreshSignals, 30000); // Every 30 seconds
            setInterval(loadMarketContext, 10000); // Every 10 seconds
            
            console.log('✅ ML Straddle Dashboard initialized');
        }
        
        function initializePnLChart() {
            const ctx = document.getElementById('pnlChart');
            if (!ctx) return;
            
            pnlChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [
                        {
                            label: 'ATM PnL',
                            data: [],
                            borderColor: '#28a745',
                            backgroundColor: 'rgba(40, 167, 69, 0.1)',
                            tension: 0.4
                        },
                        {
                            label: 'ITM PnL',
                            data: [],
                            borderColor: '#17a2b8',
                            backgroundColor: 'rgba(23, 162, 184, 0.1)',
                            tension: 0.4
                        },
                        {
                            label: 'OTM PnL',
                            data: [],
                            borderColor: '#ffc107',
                            backgroundColor: 'rgba(255, 193, 7, 0.1)',
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'PnL ($)'
                            }
                        }
                    }
                }
            });
        }
        
        function initializeStrategyChart() {
            const ctx = document.getElementById('strategyChart');
            if (!ctx) return;
            
            strategyChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['ATM', 'ITM', 'OTM', 'Triple'],
                    datasets: [{
                        data: [0, 0, 0, 0],
                        backgroundColor: ['#28a745', '#17a2b8', '#ffc107', '#dc3545']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
        
        async function loadMarketContext() {
            try {
                // Mock market context data
                const mockData = {
                    current_price: 100 + Math.random() * 10 - 5,
                    current_iv: 0.2 + Math.random() * 0.1,
                    volume_ratio: 0.8 + Math.random() * 0.4,
                    regime_type: 'BULLISH_NORMAL_VOLATILE',
                    regime_confidence: 0.75 + Math.random() * 0.2,
                    time_to_expiry: 120 + Math.random() * 60
                };
                
                // Update UI
                document.getElementById('currentPrice').textContent = `$${mockData.current_price.toFixed(2)}`;
                
                const ivElement = document.getElementById('currentIV');
                ivElement.textContent = `${(mockData.current_iv * 100).toFixed(1)}%`;
                ivElement.className = `iv-indicator ${getIVClass(mockData.current_iv)}`;
                
                document.getElementById('volumeRatio').textContent = mockData.volume_ratio.toFixed(2);
                document.getElementById('marketRegime').textContent = mockData.regime_type.replace(/_/g, ' ');
                
                const regimeBar = document.getElementById('regimeConfidence');
                regimeBar.style.width = `${mockData.regime_confidence * 100}%`;
                
                document.getElementById('timeToExpiry').textContent = `${Math.round(mockData.time_to_expiry)} minutes`;
                
            } catch (error) {
                console.error('Failed to load market context:', error);
            }
        }
        
        function getIVClass(iv) {
            if (iv < 0.15) return 'iv-low';
            if (iv > 0.3) return 'iv-high';
            return 'iv-normal';
        }
        
        async function refreshSignals() {
            try {
                // Mock signal data
                const signals = {
                    atm: generateMockSignal(),
                    itm: generateMockSignal(),
                    otm: generateMockSignal(),
                    triple: generateMockSignal()
                };
                
                // Update ATM signal
                updateSignalDisplay('atm', signals.atm);
                updateSignalDisplay('itm', signals.itm);
                
                showAlert('Signals refreshed successfully', 'success');
                
            } catch (error) {
                console.error('Failed to refresh signals:', error);
                showAlert('Failed to refresh signals', 'danger');
            }
        }
        
        function generateMockSignal() {
            const actions = ['BUY', 'SELL', 'HOLD'];
            const action = actions[Math.floor(Math.random() * actions.length)];
            const confidence = Math.random();
            
            return {
                action: action,
                confidence: confidence,
                entry_price: 100 + Math.random() * 10 - 5,
                target_price: action !== 'HOLD' ? 100 + Math.random() * 15 - 7.5 : null,
                timestamp: new Date().toLocaleTimeString()
            };
        }
        
        function updateSignalDisplay(type, signal) {
            const indicator = document.getElementById(`${type}SignalIndicator`);
            const action = document.getElementById(`${type}SignalAction`);
            const time = document.getElementById(`${type}SignalTime`);
            const fill = document.getElementById(`${type}ConfidenceFill`);
            const text = document.getElementById(`${type}ConfidenceText`);
            const entry = document.getElementById(`${type}EntryPrice`);
            const target = document.getElementById(`${type}TargetPrice`);
            
            if (!indicator) return;
            
            // Update signal indicator
            indicator.className = `signal-indicator signal-${signal.action.toLowerCase()}`;
            action.textContent = signal.action;
            time.textContent = signal.timestamp;
            
            // Update confidence
            const confidencePercent = signal.confidence * 100;
            fill.style.width = `${confidencePercent}%`;
            text.textContent = `${confidencePercent.toFixed(1)}%`;
            
            // Update prices
            entry.textContent = signal.entry_price ? `$${signal.entry_price.toFixed(2)}` : '--';
            target.textContent = signal.target_price ? `$${signal.target_price.toFixed(2)}` : '--';
        }
        
        async function generateTestSignal() {
            try {
                showAlert('Generating test signal...', 'info');
                
                // Simulate API call delay
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Generate mock signals
                await refreshSignals();
                
                // Update charts with mock data
                updatePnLChart();
                updateStrategyChart();
                
                showAlert('Test signal generated successfully!', 'success');
                
            } catch (error) {
                console.error('Failed to generate test signal:', error);
                showAlert('Failed to generate test signal', 'danger');
            }
        }
        
        function updatePnLChart() {
            if (!pnlChart) return;
            
            const now = new Date().toLocaleTimeString();
            
            // Add new data point
            pnlChart.data.labels.push(now);
            pnlChart.data.datasets[0].data.push((Math.random() - 0.5) * 1000); // ATM
            pnlChart.data.datasets[1].data.push((Math.random() - 0.5) * 800);  // ITM
            pnlChart.data.datasets[2].data.push((Math.random() - 0.5) * 600);  // OTM
            
            // Keep only last 10 points
            if (pnlChart.data.labels.length > 10) {
                pnlChart.data.labels.shift();
                pnlChart.data.datasets.forEach(dataset => dataset.data.shift());
            }
            
            pnlChart.update();
        }
        
        function updateStrategyChart() {
            if (!strategyChart) return;
            
            // Update with random distribution
            const total = 100;
            const atm = Math.random() * 40;
            const itm = Math.random() * 30;
            const otm = Math.random() * 20;
            const triple = total - atm - itm - otm;
            
            strategyChart.data.datasets[0].data = [atm, itm, otm, Math.max(0, triple)];
            strategyChart.update();
        }
        
        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alertContainer');
            if (!alertContainer) return;
            
            const alertId = 'alert-' + Date.now();
            const icons = {
                'success': 'check-circle',
                'danger': 'exclamation-triangle',
                'warning': 'exclamation-circle',
                'info': 'info-circle'
            };
            
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" id="${alertId}" role="alert">
                    <i class="fas fa-${icons[type] || 'info-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            alertContainer.insertAdjacentHTML('beforeend', alertHtml);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 5000);
        }
    </script>
</body>
</html>

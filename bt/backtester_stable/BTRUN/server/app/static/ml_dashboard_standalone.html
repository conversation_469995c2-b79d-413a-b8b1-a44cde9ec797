<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ML System Dashboard - Enhanced GPU Backtester</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-healthy { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        
        .metric-card {
            transition: transform 0.2s;
        }
        .metric-card:hover {
            transform: translateY(-2px);
        }
        
        .enhancement-demo {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .confidence-bar {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997, #17a2b8);
            transition: width 0.3s ease;
        }
        
        .feature-importance {
            font-size: 0.85em;
        }
        
        .loading-spinner {
            display: none;
        }
        
        .alert-dismissible {
            position: relative;
        }
        
        .demo-mode {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            padding: 10px;
            text-align: center;
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Demo Mode Banner -->
    <div class="demo-mode">
        <i class="fas fa-flask me-2"></i>DEMO MODE - ML System Dashboard
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-brain me-2"></i>ML System Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <span class="nav-link">
                    <i class="fas fa-chart-line me-1"></i>Backtester V2
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- System Status Row -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-heartbeat me-2"></i>ML System Status
                        </h5>
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshStatus()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="d-flex align-items-center">
                                    <span class="status-indicator status-healthy" id="systemStatus"></span>
                                    <span id="systemStatusText">Operational</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">Components Loaded</small>
                                <div class="fw-bold" id="componentsLoaded">4</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">Avg Latency</small>
                                <div class="fw-bold" id="avgLatency">16.8ms</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">Error Rate</small>
                                <div class="fw-bold" id="errorRate">0.0%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Metrics Row -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-tachometer-alt fa-2x text-primary mb-2"></i>
                        <h6 class="card-title">Total Predictions</h6>
                        <h4 class="text-primary" id="totalPredictions">6</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x text-success mb-2"></i>
                        <h6 class="card-title">Avg Response Time</h6>
                        <h4 class="text-success" id="avgResponseTime">16.8ms</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x text-info mb-2"></i>
                        <h6 class="card-title">Success Rate</h6>
                        <h4 class="text-info" id="successRate">100%</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-2x text-warning mb-2"></i>
                        <h6 class="card-title">Enhancement Rate</h6>
                        <h4 class="text-warning" id="enhancementRate">+5.1%</h4>
                    </div>
                </div>
            </div>
        </div>

        <!-- ML Enhancement Demo Row -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card enhancement-demo">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-magic me-2"></i>ML_INDICATOR Enhancement Demo
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Input Configuration</h6>
                                <div class="mb-3">
                                    <label class="form-label">Regime Type</label>
                                    <select class="form-select" id="regimeType">
                                        <option value="BULLISH_NORMAL_VOLATILE">Bullish Normal Volatile</option>
                                        <option value="BULLISH_HIGH_VOLATILE">Bullish High Volatile</option>
                                        <option value="BEARISH_NORMAL_VOLATILE">Bearish Normal Volatile</option>
                                        <option value="NEUTRAL_LOW_VOLATILE">Neutral Low Volatile</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Base Confidence</label>
                                    <input type="range" class="form-range" id="baseConfidence" min="0" max="1" step="0.01" value="0.68">
                                    <small class="text-light" id="baseConfidenceValue">0.68</small>
                                </div>
                                <button class="btn btn-light" onclick="runEnhancementDemo()" id="enhanceBtn">
                                    <i class="fas fa-play me-2"></i>Run Enhancement
                                    <span class="loading-spinner">
                                        <i class="fas fa-spinner fa-spin"></i>
                                    </span>
                                </button>
                            </div>
                            <div class="col-md-6">
                                <h6>Enhancement Results</h6>
                                <div id="enhancementResults">
                                    <div class="text-center text-light-50 py-4">
                                        <i class="fas fa-chart-bar fa-3x mb-3"></i>
                                        <p>Run enhancement to see results</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>System Controls
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-success" onclick="showAlert('ML System is already enabled', 'success')">
                                <i class="fas fa-power-off me-2"></i>Enable ML System
                            </button>
                            <button class="btn btn-warning" onclick="showAlert('ML System disabled (demo)', 'warning')">
                                <i class="fas fa-pause me-2"></i>Disable ML System
                            </button>
                            <button class="btn btn-info" onclick="resetStats()">
                                <i class="fas fa-redo me-2"></i>Reset Statistics
                            </button>
                            <button class="btn btn-secondary" onclick="showAlert('Download feature coming soon', 'info')">
                                <i class="fas fa-download me-2"></i>Download Logs
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Chart Row -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-area me-2"></i>Performance Trends
                        </h6>
                    </div>
                    <div class="card-body">
                        <canvas id="performanceChart" height="100"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alerts -->
        <div id="alertContainer"></div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Standalone ML Dashboard JavaScript -->
    <script>
        // Standalone ML Dashboard for Demo
        let performanceChart = null;
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeDemo();
        });
        
        function initializeDemo() {
            console.log('🚀 Initializing ML Dashboard Demo...');
            
            // Initialize event listeners
            const baseConfidenceSlider = document.getElementById('baseConfidence');
            const baseConfidenceValue = document.getElementById('baseConfidenceValue');
            
            baseConfidenceSlider.addEventListener('input', (e) => {
                baseConfidenceValue.textContent = e.target.value;
            });
            
            // Initialize performance chart
            initPerformanceChart();
            
            console.log('✅ ML Dashboard Demo initialized');
        }
        
        function initPerformanceChart() {
            const ctx = document.getElementById('performanceChart');
            if (!ctx) return;
            
            performanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['09:15', '09:16', '09:17', '09:18', '09:19'],
                    datasets: [
                        {
                            label: 'Confidence Enhancement (%)',
                            data: [71.5, 73.2, 72.8, 74.1, 75.3],
                            borderColor: '#28a745',
                            backgroundColor: 'rgba(40, 167, 69, 0.1)',
                            tension: 0.4
                        },
                        {
                            label: 'Response Time (ms)',
                            data: [18.2, 16.8, 15.9, 17.1, 16.5],
                            borderColor: '#17a2b8',
                            backgroundColor: 'rgba(23, 162, 184, 0.1)',
                            tension: 0.4,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Confidence Enhancement (%)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Response Time (ms)'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    }
                }
            });
        }
        
        function refreshStatus() {
            showAlert('Status refreshed successfully', 'success');
        }
        
        function runEnhancementDemo() {
            const button = document.getElementById('enhanceBtn');
            const spinner = button.querySelector('.loading-spinner');
            const resultsDiv = document.getElementById('enhancementResults');
            
            // Show loading state
            button.disabled = true;
            spinner.style.display = 'inline';
            
            // Simulate API call
            setTimeout(() => {
                const baseConfidence = parseFloat(document.getElementById('baseConfidence').value);
                const regimeType = document.getElementById('regimeType').value;
                
                // Simulate enhancement
                const enhancedConfidence = Math.min(baseConfidence + 0.05 + Math.random() * 0.03, 1.0);
                const regimeAlignment = 0.72 + Math.random() * 0.1;
                const improvement = ((enhancedConfidence - baseConfidence) / baseConfidence * 100);
                
                // Display results
                resultsDiv.innerHTML = `
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Enhanced Confidence</span>
                            <span class="fw-bold">${(enhancedConfidence * 100).toFixed(1)}%</span>
                        </div>
                        <div class="confidence-bar">
                            <div class="confidence-fill" style="width: ${enhancedConfidence * 100}%"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Regime Alignment</span>
                            <span class="fw-bold">${(regimeAlignment * 100).toFixed(1)}%</span>
                        </div>
                        <div class="confidence-bar">
                            <div class="confidence-fill" style="width: ${regimeAlignment * 100}%"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Improvement</span>
                            <span class="fw-bold text-success">+${improvement.toFixed(1)}%</span>
                        </div>
                    </div>
                    
                    <div class="feature-importance">
                        <small class="text-light-75">Top Features:</small>
                        <div class="mt-1">
                            <small class="d-block">current_volume: 92.4%</small>
                            <small class="d-block">sma_5: 2.4%</small>
                            <small class="d-block">current_price: 2.4%</small>
                        </div>
                    </div>
                `;
                
                showAlert('ML enhancement completed successfully!', 'success');
                
                // Update chart
                addDataToChart(enhancedConfidence);
                
                // Hide loading state
                button.disabled = false;
                spinner.style.display = 'none';
            }, 2000);
        }
        
        function addDataToChart(confidence) {
            if (!performanceChart) return;
            
            const now = new Date().toLocaleTimeString();
            
            // Add new data point
            performanceChart.data.labels.push(now);
            performanceChart.data.datasets[0].data.push((confidence * 100).toFixed(1));
            performanceChart.data.datasets[1].data.push((Math.random() * 10 + 15).toFixed(1));
            
            // Keep only last 10 points
            if (performanceChart.data.labels.length > 10) {
                performanceChart.data.labels.shift();
                performanceChart.data.datasets[0].data.shift();
                performanceChart.data.datasets[1].data.shift();
            }
            
            performanceChart.update();
        }
        
        function resetStats() {
            // Reset metrics to initial values
            document.getElementById('totalPredictions').textContent = '0';
            document.getElementById('avgResponseTime').textContent = '0ms';
            document.getElementById('avgLatency').textContent = '0ms';
            
            showAlert('Statistics reset successfully', 'info');
        }
        
        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alertContainer');
            if (!alertContainer) return;
            
            const alertId = 'alert-' + Date.now();
            const icons = {
                'success': 'check-circle',
                'danger': 'exclamation-triangle',
                'warning': 'exclamation-circle',
                'info': 'info-circle'
            };
            
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" id="${alertId}" role="alert">
                    <i class="fas fa-${icons[type] || 'info-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            alertContainer.insertAdjacentHTML('beforeend', alertHtml);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 5000);
        }
    </script>
</body>
</html>

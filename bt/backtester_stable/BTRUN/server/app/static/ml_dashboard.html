<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ML System Dashboard - Enhanced GPU Backtester</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-healthy { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        
        .metric-card {
            transition: transform 0.2s;
        }
        .metric-card:hover {
            transform: translateY(-2px);
        }
        
        .enhancement-demo {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .confidence-bar {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997, #17a2b8);
            transition: width 0.3s ease;
        }
        
        .feature-importance {
            font-size: 0.85em;
        }
        
        .loading-spinner {
            display: none;
        }
        
        .alert-dismissible {
            position: relative;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-brain me-2"></i>ML System Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/bt">
                    <i class="fas fa-chart-line me-1"></i>Backtester
                </a>
                <a class="nav-link" href="/api/docs">
                    <i class="fas fa-book me-1"></i>API Docs
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- System Status Row -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-heartbeat me-2"></i>ML System Status
                        </h5>
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshStatus()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="d-flex align-items-center">
                                    <span class="status-indicator status-healthy" id="systemStatus"></span>
                                    <span id="systemStatusText">Loading...</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">Components Loaded</small>
                                <div class="fw-bold" id="componentsLoaded">-</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">Avg Latency</small>
                                <div class="fw-bold" id="avgLatency">-</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">Error Rate</small>
                                <div class="fw-bold" id="errorRate">-</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Metrics Row -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-tachometer-alt fa-2x text-primary mb-2"></i>
                        <h6 class="card-title">Total Predictions</h6>
                        <h4 class="text-primary" id="totalPredictions">0</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x text-success mb-2"></i>
                        <h6 class="card-title">Avg Response Time</h6>
                        <h4 class="text-success" id="avgResponseTime">0ms</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x text-info mb-2"></i>
                        <h6 class="card-title">Success Rate</h6>
                        <h4 class="text-info" id="successRate">100%</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-2x text-warning mb-2"></i>
                        <h6 class="card-title">Enhancement Rate</h6>
                        <h4 class="text-warning" id="enhancementRate">+5.1%</h4>
                    </div>
                </div>
            </div>
        </div>

        <!-- ML Enhancement Demo Row -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card enhancement-demo">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-magic me-2"></i>ML_INDICATOR Enhancement Demo
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Input Configuration</h6>
                                <div class="mb-3">
                                    <label class="form-label">Regime Type</label>
                                    <select class="form-select" id="regimeType">
                                        <option value="BULLISH_NORMAL_VOLATILE">Bullish Normal Volatile</option>
                                        <option value="BULLISH_HIGH_VOLATILE">Bullish High Volatile</option>
                                        <option value="BEARISH_NORMAL_VOLATILE">Bearish Normal Volatile</option>
                                        <option value="NEUTRAL_LOW_VOLATILE">Neutral Low Volatile</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Base Confidence</label>
                                    <input type="range" class="form-range" id="baseConfidence" min="0" max="1" step="0.01" value="0.68">
                                    <small class="text-light" id="baseConfidenceValue">0.68</small>
                                </div>
                                <button class="btn btn-light" onclick="runEnhancementDemo()">
                                    <i class="fas fa-play me-2"></i>Run Enhancement
                                    <span class="loading-spinner">
                                        <i class="fas fa-spinner fa-spin"></i>
                                    </span>
                                </button>
                            </div>
                            <div class="col-md-6">
                                <h6>Enhancement Results</h6>
                                <div id="enhancementResults">
                                    <div class="text-center text-light-50 py-4">
                                        <i class="fas fa-chart-bar fa-3x mb-3"></i>
                                        <p>Run enhancement to see results</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>System Controls
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-success" onclick="enableMLSystem()">
                                <i class="fas fa-power-off me-2"></i>Enable ML System
                            </button>
                            <button class="btn btn-warning" onclick="disableMLSystem()">
                                <i class="fas fa-pause me-2"></i>Disable ML System
                            </button>
                            <button class="btn btn-info" onclick="resetStats()">
                                <i class="fas fa-redo me-2"></i>Reset Statistics
                            </button>
                            <button class="btn btn-secondary" onclick="downloadLogs()">
                                <i class="fas fa-download me-2"></i>Download Logs
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Chart Row -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-area me-2"></i>Performance Trends
                        </h6>
                    </div>
                    <div class="card-body">
                        <canvas id="performanceChart" height="100"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alerts -->
        <div id="alertContainer"></div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- ML Dashboard JavaScript -->
    <script src="/static/js/ml_dashboard.js"></script>
</body>
</html>

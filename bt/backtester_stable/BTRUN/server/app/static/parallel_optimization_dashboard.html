<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parallel Optimization Dashboard - Enhanced GPU Backtester</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Animate.css -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2563eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .dashboard-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px;
            padding: 30px;
        }

        .optimization-card {
            background: linear-gradient(145deg, #ffffff, #f1f5f9);
            border: none;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .optimization-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .progress-container {
            position: relative;
            background: #f1f5f9;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }

        .progress-bar-custom {
            height: 8px;
            border-radius: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--success-color));
            transition: width 0.5s ease;
            position: relative;
            overflow: hidden;
        }

        .progress-bar-custom::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .worker-status {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin: 2px;
            transition: all 0.3s ease;
        }

        .worker-idle { background-color: #94a3b8; }
        .worker-busy { background-color: var(--success-color); animation: pulse 1s infinite; }
        .worker-error { background-color: var(--danger-color); }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .metric-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .metric-card:hover {
            transform: scale(1.05);
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin: 10px 0;
        }

        .eta-display {
            background: linear-gradient(45deg, var(--info-color), var(--primary-color));
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .task-item {
            background: white;
            border-left: 4px solid var(--primary-color);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .task-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        }

        .task-pending { border-left-color: #94a3b8; }
        .task-running { border-left-color: var(--warning-color); }
        .task-completed { border-left-color: var(--success-color); }
        .task-failed { border-left-color: var(--danger-color); }

        .optimization-controls {
            background: linear-gradient(135deg, #1f2937, #374151);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
        }

        .btn-gradient {
            background: linear-gradient(45deg, var(--primary-color), var(--info-color));
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(37, 99, 235, 0.3);
            color: white;
        }

        .real-time-chart {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-running { background-color: var(--success-color); animation: pulse 1s infinite; }
        .status-paused { background-color: var(--warning-color); }
        .status-stopped { background-color: var(--danger-color); }
        .status-completed { background-color: var(--info-color); }

        .pareto-front-viz {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .optimization-summary {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .notification-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            min-width: 300px;
        }

        .resource-monitor {
            background: linear-gradient(135deg, #374151, #1f2937);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }

        .cpu-usage, .memory-usage, .gpu-usage {
            margin: 10px 0;
        }

        .usage-bar {
            background: rgba(255, 255, 255, 0.2);
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
        }

        .usage-fill {
            height: 100%;
            transition: width 0.5s ease;
            border-radius: 4px;
        }

        .cpu-fill { background: linear-gradient(90deg, #10b981, #059669); }
        .memory-fill { background: linear-gradient(90deg, #f59e0b, #d97706); }
        .gpu-fill { background: linear-gradient(90deg, #8b5cf6, #7c3aed); }
    </style>
</head>
<body>
    <!-- Header -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: rgba(0,0,0,0.1);">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-rocket me-2"></i>Parallel Optimization Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-server me-1"></i>
                    <span id="systemStatus">System Ready</span>
                </span>
                <a class="nav-link" href="index.html">
                    <i class="fas fa-home me-1"></i>Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <!-- Main Dashboard Container -->
        <div class="dashboard-container">

            <!-- Optimization Controls -->
            <div class="optimization-controls">
                <div class="row">
                    <div class="col-md-8">
                        <h3><i class="fas fa-cogs me-2"></i>Optimization Control Center</h3>
                        <p class="mb-3">Configure and launch parallel optimization runs with real-time monitoring</p>

                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Strategy Type</label>
                                <select class="form-select" id="strategyType">
                                    <option value="TBS">Time-Based Strategy (TBS)</option>
                                    <option value="TV">TradingView Strategy (TV)</option>
                                    <option value="OI">Open Interest Strategy (OI)</option>
                                    <option value="ORB">Opening Range Breakout (ORB)</option>
                                    <option value="ML_INDICATOR">ML Indicator Strategy</option>
                                    <option value="PORTFOLIO">Portfolio Optimization</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Optimization Type</label>
                                <select class="form-select" id="optimizationType">
                                    <option value="parameter">Parameter Optimization</option>
                                    <option value="performance">Performance Optimization</option>
                                    <option value="multi_objective">Multi-Objective Optimization</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-4">
                                <label class="form-label">Parallel Workers</label>
                                <input type="number" class="form-control" id="parallelWorkers" value="8" min="1" max="16">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Max Iterations</label>
                                <input type="number" class="form-control" id="maxIterations" value="100" min="10" max="1000">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Algorithm</label>
                                <select class="form-select" id="algorithm">
                                    <option value="bayesian">Bayesian Optimization</option>
                                    <option value="genetic">Genetic Algorithm</option>
                                    <option value="grid">Grid Search</option>
                                    <option value="random">Random Search</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 text-center">
                        <div class="d-grid gap-2">
                            <button class="btn btn-gradient btn-lg" onclick="startOptimization()">
                                <i class="fas fa-play me-2"></i>Start Optimization
                            </button>
                            <button class="btn btn-outline-light" onclick="pauseOptimization()">
                                <i class="fas fa-pause me-2"></i>Pause
                            </button>
                            <button class="btn btn-outline-danger" onclick="stopOptimization()">
                                <i class="fas fa-stop me-2"></i>Stop
                            </button>
                        </div>

                        <div class="eta-display mt-3">
                            <i class="fas fa-clock me-2"></i>
                            <div>ETA: <span id="etaDisplay">--:--:--</span></div>
                            <small>Estimated Time Remaining</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Real-time Metrics Row -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="metric-card">
                        <i class="fas fa-tasks fa-2x mb-2"></i>
                        <div class="metric-value" id="totalTasks">0</div>
                        <div>Total Tasks</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <div class="metric-value" id="completedTasks">0</div>
                        <div>Completed</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <div class="metric-value" id="activeWorkers">0</div>
                        <div>Active Workers</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <i class="fas fa-tachometer-alt fa-2x mb-2"></i>
                        <div class="metric-value" id="throughput">0</div>
                        <div>Tasks/Min</div>
                    </div>
                </div>
            </div>

            <!-- Progress and Worker Status Row -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="optimization-card">
                        <div class="card-header bg-transparent">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>Optimization Progress
                                <span class="status-indicator status-running" id="optimizationStatus"></span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="progress-container">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Overall Progress</span>
                                    <span id="overallProgress">0%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar progress-bar-custom" id="progressBar" style="width: 0%"></div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <small class="text-muted">Current Iteration:</small>
                                    <div class="fw-bold" id="currentIteration">0 / 0</div>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted">Best Score:</small>
                                    <div class="fw-bold text-success" id="bestScore">--</div>
                                </div>
                            </div>

                            <div class="real-time-chart mt-3">
                                <canvas id="progressChart" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="optimization-card">
                        <div class="card-header bg-transparent">
                            <h6 class="mb-0">
                                <i class="fas fa-server me-2"></i>Worker Status
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Worker Pool:</span>
                                    <span id="workerCount">8 workers</span>
                                </div>
                                <div id="workerStatusGrid" class="mt-2">
                                    <!-- Worker status indicators will be populated here -->
                                </div>
                            </div>

                            <div class="resource-monitor">
                                <h6><i class="fas fa-microchip me-2"></i>Resource Usage</h6>

                                <div class="cpu-usage">
                                    <div class="d-flex justify-content-between">
                                        <span>CPU</span>
                                        <span id="cpuUsage">0%</span>
                                    </div>
                                    <div class="usage-bar">
                                        <div class="usage-fill cpu-fill" id="cpuBar" style="width: 0%"></div>
                                    </div>
                                </div>

                                <div class="memory-usage">
                                    <div class="d-flex justify-content-between">
                                        <span>Memory</span>
                                        <span id="memoryUsage">0%</span>
                                    </div>
                                    <div class="usage-bar">
                                        <div class="usage-fill memory-fill" id="memoryBar" style="width: 0%"></div>
                                    </div>
                                </div>

                                <div class="gpu-usage">
                                    <div class="d-flex justify-content-between">
                                        <span>GPU</span>
                                        <span id="gpuUsage">0%</span>
                                    </div>
                                    <div class="usage-bar">
                                        <div class="usage-fill gpu-fill" id="gpuBar" style="width: 0%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Task Queue and Results Row -->
            <div class="row mb-4">
                <div class="col-lg-6">
                    <div class="optimization-card">
                        <div class="card-header bg-transparent">
                            <h6 class="mb-0">
                                <i class="fas fa-list me-2"></i>Task Queue
                                <span class="badge bg-primary ms-2" id="queueSize">0</span>
                            </h6>
                        </div>
                        <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                            <div id="taskQueue">
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-inbox fa-3x mb-3"></i>
                                    <p>No tasks in queue</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="optimization-card">
                        <div class="card-header bg-transparent">
                            <h6 class="mb-0">
                                <i class="fas fa-trophy me-2"></i>Best Results
                            </h6>
                        </div>
                        <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                            <div id="bestResults">
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-chart-line fa-3x mb-3"></i>
                                    <p>No results yet</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Multi-Objective Optimization Results -->
            <div class="row mb-4" id="multiObjectiveSection" style="display: none;">
                <div class="col-12">
                    <div class="optimization-card">
                        <div class="card-header bg-transparent">
                            <h5 class="mb-0">
                                <i class="fas fa-bullseye me-2"></i>Multi-Objective Optimization Results
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-8">
                                    <div class="pareto-front-viz">
                                        <h6>Pareto Front Visualization</h6>
                                        <canvas id="paretoChart" height="300"></canvas>
                                    </div>
                                </div>
                                <div class="col-lg-4">
                                    <div class="optimization-summary">
                                        <h6><i class="fas fa-chart-pie me-2"></i>Trade-off Analysis</h6>
                                        <div id="tradeoffAnalysis">
                                            <div class="mb-2">
                                                <small>Pareto Solutions:</small>
                                                <div class="fw-bold" id="paretoSolutions">0</div>
                                            </div>
                                            <div class="mb-2">
                                                <small>Hypervolume:</small>
                                                <div class="fw-bold" id="hypervolume">--</div>
                                            </div>
                                            <div class="mb-2">
                                                <small>Spread:</small>
                                                <div class="fw-bold" id="spread">--</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Analytics -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="optimization-card">
                        <div class="card-header bg-transparent">
                            <h6 class="mb-0">
                                <i class="fas fa-analytics me-2"></i>Performance Analytics
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="real-time-chart">
                                <canvas id="performanceChart" height="250"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="optimization-card">
                        <div class="card-header bg-transparent">
                            <h6 class="mb-0">
                                <i class="fas fa-history me-2"></i>Optimization History
                            </h6>
                        </div>
                        <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                            <div id="optimizationHistory">
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-clock fa-2x mb-3"></i>
                                    <p>No history available</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Export and Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="optimization-controls">
                        <div class="row">
                            <div class="col-md-8">
                                <h6><i class="fas fa-download me-2"></i>Export Results</h6>
                                <p class="mb-3">Download optimization results and analysis reports</p>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="btn-group">
                                    <button class="btn btn-outline-light" onclick="exportResults('csv')">
                                        <i class="fas fa-file-csv me-2"></i>CSV
                                    </button>
                                    <button class="btn btn-outline-light" onclick="exportResults('json')">
                                        <i class="fas fa-file-code me-2"></i>JSON
                                    </button>
                                    <button class="btn btn-outline-light" onclick="exportResults('pdf')">
                                        <i class="fas fa-file-pdf me-2"></i>PDF Report
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Toast Container -->
    <div class="notification-toast" id="toastContainer"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Parallel Optimization Dashboard JavaScript -->
    <script>
        // Global variables
        let optimizationState = {
            isRunning: false,
            isPaused: false,
            currentOptimization: null,
            startTime: null,
            totalTasks: 0,
            completedTasks: 0,
            workers: [],
            bestScore: null,
            results: []
        };

        let charts = {
            progress: null,
            performance: null,
            pareto: null
        };

        const API_BASE_URL = window.location.origin;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
        });

        async function initializeDashboard() {
            console.log('🚀 Initializing Parallel Optimization Dashboard...');

            // Initialize charts
            initializeCharts();

            // Initialize worker status
            initializeWorkerStatus();

            // Start real-time updates
            setInterval(updateDashboard, 1000); // Every second
            setInterval(updateResourceUsage, 2000); // Every 2 seconds

            // Load optimization history
            await loadOptimizationHistory();

            console.log('✅ Dashboard initialized');
        }

        function initializeCharts() {
            // Progress Chart
            const progressCtx = document.getElementById('progressChart');
            if (progressCtx) {
                charts.progress = new Chart(progressCtx, {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [{
                            label: 'Best Score',
                            data: [],
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.4,
                            fill: true
                        }, {
                            label: 'Average Score',
                            data: [],
                            borderColor: '#06b6d4',
                            backgroundColor: 'rgba(6, 182, 212, 0.1)',
                            tension: 0.4,
                            fill: false
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Score'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'Iteration'
                                }
                            }
                        }
                    }
                });
            }

            // Performance Chart
            const performanceCtx = document.getElementById('performanceChart');
            if (performanceCtx) {
                charts.performance = new Chart(performanceCtx, {
                    type: 'bar',
                    data: {
                        labels: ['Sharpe Ratio', 'Max Drawdown', 'Win Rate', 'Profit Factor'],
                        datasets: [{
                            label: 'Current Best',
                            data: [0, 0, 0, 0],
                            backgroundColor: [
                                'rgba(16, 185, 129, 0.8)',
                                'rgba(239, 68, 68, 0.8)',
                                'rgba(6, 182, 212, 0.8)',
                                'rgba(245, 158, 11, 0.8)'
                            ],
                            borderColor: [
                                '#10b981',
                                '#ef4444',
                                '#06b6d4',
                                '#f59e0b'
                            ],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Value'
                                }
                            }
                        }
                    }
                });
            }

            // Pareto Chart (for multi-objective)
            const paretoCtx = document.getElementById('paretoChart');
            if (paretoCtx) {
                charts.pareto = new Chart(paretoCtx, {
                    type: 'scatter',
                    data: {
                        datasets: [{
                            label: 'Pareto Front',
                            data: [],
                            backgroundColor: 'rgba(139, 92, 246, 0.8)',
                            borderColor: '#8b5cf6',
                            pointRadius: 6,
                            pointHoverRadius: 8
                        }, {
                            label: 'Other Solutions',
                            data: [],
                            backgroundColor: 'rgba(156, 163, 175, 0.5)',
                            borderColor: '#9ca3af',
                            pointRadius: 4,
                            pointHoverRadius: 6
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top'
                            },
                            title: {
                                display: true,
                                text: 'Objective 1 vs Objective 2'
                            }
                        },
                        scales: {
                            x: {
                                title: {
                                    display: true,
                                    text: 'Objective 1'
                                }
                            },
                            y: {
                                title: {
                                    display: true,
                                    text: 'Objective 2'
                                }
                            }
                        }
                    }
                });
            }
        }

        function initializeWorkerStatus() {
            const workerCount = parseInt(document.getElementById('parallelWorkers').value);
            optimizationState.workers = Array(workerCount).fill().map((_, i) => ({
                id: i + 1,
                status: 'idle',
                currentTask: null,
                tasksCompleted: 0
            }));

            updateWorkerStatusDisplay();
        }

        function updateWorkerStatusDisplay() {
            const container = document.getElementById('workerStatusGrid');
            if (!container) return;

            container.innerHTML = '';

            optimizationState.workers.forEach(worker => {
                const workerElement = document.createElement('div');
                workerElement.className = `worker-status worker-${worker.status}`;
                workerElement.title = `Worker ${worker.id}: ${worker.status}${worker.currentTask ? ` (Task: ${worker.currentTask})` : ''}`;
                container.appendChild(workerElement);
            });

            // Update worker count display
            document.getElementById('workerCount').textContent = `${optimizationState.workers.length} workers`;

            // Update active workers metric
            const activeWorkers = optimizationState.workers.filter(w => w.status === 'busy').length;
            document.getElementById('activeWorkers').textContent = activeWorkers;
        }

        async function startOptimization() {
            try {
                if (optimizationState.isRunning) {
                    showNotification('Optimization is already running', 'warning');
                    return;
                }

                // Get configuration
                const config = {
                    strategy_type: document.getElementById('strategyType').value,
                    optimization_type: document.getElementById('optimizationType').value,
                    parallel_workers: parseInt(document.getElementById('parallelWorkers').value),
                    max_iterations: parseInt(document.getElementById('maxIterations').value),
                    algorithm: document.getElementById('algorithm').value
                };

                showNotification('Starting optimization...', 'info');

                // Update state
                optimizationState.isRunning = true;
                optimizationState.isPaused = false;
                optimizationState.startTime = new Date();
                optimizationState.totalTasks = config.max_iterations;
                optimizationState.completedTasks = 0;
                optimizationState.bestScore = null;

                // Update UI
                updateOptimizationStatus('running');
                updateProgressDisplay();

                // Initialize workers
                initializeWorkerStatus();

                // Simulate optimization start (in real implementation, this would call the backend)
                await simulateOptimizationRun(config);

                showNotification('Optimization started successfully!', 'success');

            } catch (error) {
                console.error('Failed to start optimization:', error);
                showNotification('Failed to start optimization', 'error');
                optimizationState.isRunning = false;
                updateOptimizationStatus('stopped');
            }
        }

        async function pauseOptimization() {
            if (!optimizationState.isRunning) {
                showNotification('No optimization is running', 'warning');
                return;
            }

            optimizationState.isPaused = !optimizationState.isPaused;

            if (optimizationState.isPaused) {
                updateOptimizationStatus('paused');
                showNotification('Optimization paused', 'info');
            } else {
                updateOptimizationStatus('running');
                showNotification('Optimization resumed', 'info');
            }
        }

        async function stopOptimization() {
            if (!optimizationState.isRunning) {
                showNotification('No optimization is running', 'warning');
                return;
            }

            optimizationState.isRunning = false;
            optimizationState.isPaused = false;

            // Reset workers
            optimizationState.workers.forEach(worker => {
                worker.status = 'idle';
                worker.currentTask = null;
            });

            updateOptimizationStatus('stopped');
            updateWorkerStatusDisplay();
            showNotification('Optimization stopped', 'info');
        }

        function updateOptimizationStatus(status) {
            const statusIndicator = document.getElementById('optimizationStatus');
            if (statusIndicator) {
                statusIndicator.className = `status-indicator status-${status}`;
            }

            const systemStatus = document.getElementById('systemStatus');
            if (systemStatus) {
                const statusText = {
                    'running': 'Optimization Running',
                    'paused': 'Optimization Paused',
                    'stopped': 'System Ready',
                    'completed': 'Optimization Complete'
                };
                systemStatus.textContent = statusText[status] || 'System Ready';
            }
        }

        async function simulateOptimizationRun(config) {
            // This simulates a real optimization run with realistic progress updates
            let iteration = 0;
            const maxIterations = config.max_iterations;

            const runIteration = () => {
                if (!optimizationState.isRunning || optimizationState.isPaused) {
                    setTimeout(runIteration, 1000);
                    return;
                }

                if (iteration >= maxIterations) {
                    // Optimization complete
                    optimizationState.isRunning = false;
                    updateOptimizationStatus('completed');
                    showNotification('Optimization completed!', 'success');
                    return;
                }

                iteration++;
                optimizationState.completedTasks = iteration;

                // Simulate task assignment to workers
                const availableWorkers = optimizationState.workers.filter(w => w.status === 'idle');
                if (availableWorkers.length > 0) {
                    const worker = availableWorkers[0];
                    worker.status = 'busy';
                    worker.currentTask = `task_${iteration}`;

                    // Simulate task completion after random time
                    setTimeout(() => {
                        worker.status = 'idle';
                        worker.currentTask = null;
                        worker.tasksCompleted++;
                    }, Math.random() * 3000 + 1000); // 1-4 seconds
                }

                // Generate mock results
                const score = Math.random() * 2 + 0.5; // Random score between 0.5 and 2.5
                if (!optimizationState.bestScore || score > optimizationState.bestScore) {
                    optimizationState.bestScore = score;
                }

                // Add to results
                optimizationState.results.push({
                    iteration: iteration,
                    score: score,
                    parameters: generateMockParameters(),
                    timestamp: new Date()
                });

                // Update charts
                updateProgressChart();
                updatePerformanceChart();

                // Update displays
                updateProgressDisplay();
                updateWorkerStatusDisplay();
                updateTaskQueue();
                updateBestResults();

                // Schedule next iteration
                setTimeout(runIteration, Math.random() * 2000 + 500); // 0.5-2.5 seconds
            };

            // Start the simulation
            runIteration();
        }

        function generateMockParameters() {
            return {
                stop_loss: (Math.random() * 0.05 + 0.01).toFixed(3),
                take_profit: (Math.random() * 0.1 + 0.02).toFixed(3),
                position_size: (Math.random() * 0.05 + 0.01).toFixed(3),
                lookback_period: Math.floor(Math.random() * 20 + 5),
                threshold: (Math.random() * 0.02 + 0.005).toFixed(4)
            };
        }

        function updateProgressDisplay() {
            const progress = optimizationState.totalTasks > 0 ?
                (optimizationState.completedTasks / optimizationState.totalTasks) * 100 : 0;

            // Update progress bar
            const progressBar = document.getElementById('progressBar');
            if (progressBar) {
                progressBar.style.width = `${progress}%`;
            }

            // Update progress text
            const progressText = document.getElementById('overallProgress');
            if (progressText) {
                progressText.textContent = `${progress.toFixed(1)}%`;
            }

            // Update iteration display
            const iterationDisplay = document.getElementById('currentIteration');
            if (iterationDisplay) {
                iterationDisplay.textContent = `${optimizationState.completedTasks} / ${optimizationState.totalTasks}`;
            }

            // Update best score
            const bestScoreDisplay = document.getElementById('bestScore');
            if (bestScoreDisplay) {
                bestScoreDisplay.textContent = optimizationState.bestScore ?
                    optimizationState.bestScore.toFixed(4) : '--';
            }

            // Update metrics
            document.getElementById('totalTasks').textContent = optimizationState.totalTasks;
            document.getElementById('completedTasks').textContent = optimizationState.completedTasks;

            // Calculate and update ETA
            updateETA();
        }

        function updateETA() {
            if (!optimizationState.isRunning || !optimizationState.startTime || optimizationState.completedTasks === 0) {
                document.getElementById('etaDisplay').textContent = '--:--:--';
                return;
            }

            const elapsed = (new Date() - optimizationState.startTime) / 1000; // seconds
            const rate = optimizationState.completedTasks / elapsed; // tasks per second
            const remaining = optimizationState.totalTasks - optimizationState.completedTasks;
            const eta = remaining / rate; // seconds

            const hours = Math.floor(eta / 3600);
            const minutes = Math.floor((eta % 3600) / 60);
            const seconds = Math.floor(eta % 60);

            const etaString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('etaDisplay').textContent = etaString;
        }

        function updateProgressChart() {
            if (!charts.progress || optimizationState.results.length === 0) return;

            const labels = optimizationState.results.map(r => r.iteration);
            const bestScores = [];
            const avgScores = [];

            let runningBest = 0;
            let runningSum = 0;

            optimizationState.results.forEach((result, index) => {
                runningBest = Math.max(runningBest, result.score);
                runningSum += result.score;

                bestScores.push(runningBest);
                avgScores.push(runningSum / (index + 1));
            });

            charts.progress.data.labels = labels;
            charts.progress.data.datasets[0].data = bestScores;
            charts.progress.data.datasets[1].data = avgScores;
            charts.progress.update('none');
        }

        function updatePerformanceChart() {
            if (!charts.performance || !optimizationState.bestScore) return;

            // Mock performance metrics based on best score
            const metrics = [
                optimizationState.bestScore, // Sharpe Ratio
                Math.max(0, 0.3 - optimizationState.bestScore * 0.1), // Max Drawdown (lower is better)
                Math.min(1, 0.4 + optimizationState.bestScore * 0.2), // Win Rate
                Math.max(1, optimizationState.bestScore * 1.5) // Profit Factor
            ];

            charts.performance.data.datasets[0].data = metrics;
            charts.performance.update('none');
        }

        function updateTaskQueue() {
            const container = document.getElementById('taskQueue');
            if (!container) return;

            // Show recent tasks
            const recentTasks = optimizationState.results.slice(-5).reverse();

            if (recentTasks.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-3x mb-3"></i>
                        <p>No tasks in queue</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = recentTasks.map(task => `
                <div class="task-item task-completed">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>Task ${task.iteration}</strong>
                            <div class="small text-muted">Score: ${task.score.toFixed(4)}</div>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-success">Completed</span>
                            <div class="small text-muted">${task.timestamp.toLocaleTimeString()}</div>
                        </div>
                    </div>
                </div>
            `).join('');

            // Update queue size
            document.getElementById('queueSize').textContent = recentTasks.length;
        }

        function updateBestResults() {
            const container = document.getElementById('bestResults');
            if (!container) return;

            // Get top 5 results
            const topResults = [...optimizationState.results]
                .sort((a, b) => b.score - a.score)
                .slice(0, 5);

            if (topResults.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-chart-line fa-3x mb-3"></i>
                        <p>No results yet</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = topResults.map((result, index) => `
                <div class="task-item task-completed">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>#${index + 1} - Iteration ${result.iteration}</strong>
                            <div class="small text-success">Score: ${result.score.toFixed(4)}</div>
                            <div class="small text-muted">
                                SL: ${result.parameters.stop_loss},
                                TP: ${result.parameters.take_profit}
                            </div>
                        </div>
                        <div class="text-end">
                            <i class="fas fa-trophy text-warning"></i>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function updateResourceUsage() {
            // Simulate resource usage
            const cpuUsage = Math.random() * 40 + 30; // 30-70%
            const memoryUsage = Math.random() * 30 + 40; // 40-70%
            const gpuUsage = optimizationState.isRunning ? Math.random() * 30 + 60 : Math.random() * 20; // 60-90% when running

            // Update displays
            document.getElementById('cpuUsage').textContent = `${cpuUsage.toFixed(1)}%`;
            document.getElementById('memoryUsage').textContent = `${memoryUsage.toFixed(1)}%`;
            document.getElementById('gpuUsage').textContent = `${gpuUsage.toFixed(1)}%`;

            // Update bars
            document.getElementById('cpuBar').style.width = `${cpuUsage}%`;
            document.getElementById('memoryBar').style.width = `${memoryUsage}%`;
            document.getElementById('gpuBar').style.width = `${gpuUsage}%`;

            // Calculate throughput
            if (optimizationState.isRunning && optimizationState.startTime) {
                const elapsed = (new Date() - optimizationState.startTime) / 1000 / 60; // minutes
                const throughput = elapsed > 0 ? (optimizationState.completedTasks / elapsed).toFixed(1) : '0';
                document.getElementById('throughput').textContent = throughput;
            }
        }

        async function updateDashboard() {
            // This would typically fetch real-time data from the backend
            // For now, we just update the displays with current state
            updateProgressDisplay();
            updateWorkerStatusDisplay();
        }

        async function loadOptimizationHistory() {
            // Mock optimization history
            const history = [
                {
                    id: 'opt_001',
                    strategy: 'TBS',
                    type: 'Parameter',
                    status: 'Completed',
                    score: 2.34,
                    duration: '15:32',
                    timestamp: new Date(Date.now() - 86400000) // 1 day ago
                },
                {
                    id: 'opt_002',
                    strategy: 'TV',
                    type: 'Multi-Objective',
                    status: 'Completed',
                    score: 1.89,
                    duration: '23:45',
                    timestamp: new Date(Date.now() - 172800000) // 2 days ago
                }
            ];

            const container = document.getElementById('optimizationHistory');
            if (!container) return;

            if (history.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-clock fa-2x mb-3"></i>
                        <p>No history available</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = history.map(item => `
                <div class="task-item task-completed mb-2">
                    <div>
                        <strong>${item.id}</strong>
                        <div class="small text-muted">${item.strategy} - ${item.type}</div>
                        <div class="small text-success">Score: ${item.score}</div>
                        <div class="small text-muted">Duration: ${item.duration}</div>
                    </div>
                </div>
            `).join('');
        }

        function showNotification(message, type = 'info') {
            const container = document.getElementById('toastContainer');
            if (!container) return;

            const toastId = 'toast_' + Date.now();
            const bgClass = {
                'success': 'bg-success',
                'error': 'bg-danger',
                'warning': 'bg-warning',
                'info': 'bg-info'
            }[type] || 'bg-info';

            const icon = {
                'success': 'fa-check-circle',
                'error': 'fa-exclamation-circle',
                'warning': 'fa-exclamation-triangle',
                'info': 'fa-info-circle'
            }[type] || 'fa-info-circle';

            const toastHTML = `
                <div class="toast show ${bgClass} text-white" id="${toastId}" role="alert">
                    <div class="toast-header ${bgClass} text-white border-0">
                        <i class="fas ${icon} me-2"></i>
                        <strong class="me-auto">Optimization System</strong>
                        <button type="button" class="btn-close btn-close-white" onclick="document.getElementById('${toastId}').remove()"></button>
                    </div>
                    <div class="toast-body">
                        ${message}
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', toastHTML);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                const toast = document.getElementById(toastId);
                if (toast) toast.remove();
            }, 5000);
        }

        function exportResults(format) {
            if (optimizationState.results.length === 0) {
                showNotification('No results to export', 'warning');
                return;
            }

            const data = {
                optimization_config: {
                    strategy_type: document.getElementById('strategyType').value,
                    optimization_type: document.getElementById('optimizationType').value,
                    algorithm: document.getElementById('algorithm').value
                },
                results: optimizationState.results,
                summary: {
                    total_iterations: optimizationState.totalTasks,
                    completed_iterations: optimizationState.completedTasks,
                    best_score: optimizationState.bestScore,
                    duration: optimizationState.startTime ?
                        ((new Date() - optimizationState.startTime) / 1000).toFixed(0) + ' seconds' : 'N/A'
                }
            };

            let content, filename, mimeType;

            switch (format) {
                case 'csv':
                    content = convertToCSV(optimizationState.results);
                    filename = `optimization_results_${new Date().toISOString().split('T')[0]}.csv`;
                    mimeType = 'text/csv';
                    break;
                case 'json':
                    content = JSON.stringify(data, null, 2);
                    filename = `optimization_results_${new Date().toISOString().split('T')[0]}.json`;
                    mimeType = 'application/json';
                    break;
                case 'pdf':
                    showNotification('PDF export functionality coming soon!', 'info');
                    return;
                default:
                    showNotification('Unknown export format', 'error');
                    return;
            }

            // Create download
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showNotification(`Results exported as ${format.toUpperCase()}`, 'success');
        }

        function convertToCSV(results) {
            if (results.length === 0) return '';

            const headers = ['iteration', 'score', 'stop_loss', 'take_profit', 'position_size', 'lookback_period', 'threshold', 'timestamp'];
            const rows = results.map(result => [
                result.iteration,
                result.score,
                result.parameters.stop_loss,
                result.parameters.take_profit,
                result.parameters.position_size,
                result.parameters.lookback_period,
                result.parameters.threshold,
                result.timestamp.toISOString()
            ]);

            return [headers, ...rows].map(row => row.join(',')).join('\n');
        }

        // Event listeners for form changes
        document.getElementById('parallelWorkers').addEventListener('change', function() {
            if (!optimizationState.isRunning) {
                initializeWorkerStatus();
            }
        });

        document.getElementById('optimizationType').addEventListener('change', function() {
            const isMultiObjective = this.value === 'multi_objective';
            const multiObjectiveSection = document.getElementById('multiObjectiveSection');
            if (multiObjectiveSection) {
                multiObjectiveSection.style.display = isMultiObjective ? 'block' : 'none';
            }
        });
    </script>
</body>
</html>
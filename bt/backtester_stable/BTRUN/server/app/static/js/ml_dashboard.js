/**
 * ML Dashboard JavaScript
 * =======================
 * 
 * Frontend logic for the ML System Dashboard
 */

class MLDashboard {
    constructor() {
        this.apiBase = '/api/v1/ml';
        this.performanceChart = null;
        this.refreshInterval = null;
        
        this.init();
    }
    
    async init() {
        console.log('🚀 Initializing ML Dashboard...');
        
        // Initialize components
        this.initEventListeners();
        this.initPerformanceChart();
        
        // Load initial data
        await this.refreshStatus();
        await this.loadPerformanceData();
        
        // Start auto-refresh
        this.startAutoRefresh();
        
        console.log('✅ ML Dashboard initialized');
    }
    
    initEventListeners() {
        // Base confidence slider
        const baseConfidenceSlider = document.getElementById('baseConfidence');
        const baseConfidenceValue = document.getElementById('baseConfidenceValue');
        
        if (baseConfidenceSlider && baseConfidenceValue) {
            baseConfidenceSlider.addEventListener('input', (e) => {
                baseConfidenceValue.textContent = e.target.value;
            });
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'r':
                        e.preventDefault();
                        this.refreshStatus();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.runEnhancementDemo();
                        break;
                }
            }
        });
    }
    
    initPerformanceChart() {
        const ctx = document.getElementById('performanceChart');
        if (!ctx) return;
        
        this.performanceChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'Confidence Enhancement',
                        data: [],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4
                    },
                    {
                        label: 'Response Time (ms)',
                        data: [],
                        borderColor: '#17a2b8',
                        backgroundColor: 'rgba(23, 162, 184, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Confidence Enhancement (%)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Response Time (ms)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                }
            }
        });
    }
    
    async refreshStatus() {
        try {
            console.log('🔄 Refreshing ML system status...');
            
            const response = await fetch(`${this.apiBase}/status`);
            const data = await response.json();
            
            if (response.ok) {
                this.updateStatusDisplay(data);
                this.updatePerformanceMetrics(data.performance);
            } else {
                this.showAlert('Failed to fetch ML system status', 'danger');
            }
        } catch (error) {
            console.error('Status refresh failed:', error);
            this.showAlert('Connection error while fetching status', 'danger');
            this.updateStatusDisplay({ enabled: false, healthy: false });
        }
    }
    
    updateStatusDisplay(status) {
        // System status indicator
        const statusIndicator = document.getElementById('systemStatus');
        const statusText = document.getElementById('systemStatusText');
        
        if (statusIndicator && statusText) {
            statusIndicator.className = 'status-indicator ' + 
                (status.healthy ? 'status-healthy' : 'status-error');
            statusText.textContent = status.enabled ? 
                (status.healthy ? 'Operational' : 'Degraded') : 'Disabled';
        }
        
        // Components loaded
        const componentsLoaded = document.getElementById('componentsLoaded');
        if (componentsLoaded) {
            componentsLoaded.textContent = status.components_loaded || 0;
        }
    }
    
    updatePerformanceMetrics(performance) {
        // Update metric cards
        const totalPredictions = document.getElementById('totalPredictions');
        const avgResponseTime = document.getElementById('avgResponseTime');
        const successRate = document.getElementById('successRate');
        const avgLatency = document.getElementById('avgLatency');
        const errorRate = document.getElementById('errorRate');
        
        if (totalPredictions) {
            totalPredictions.textContent = performance.predictions || 0;
        }
        
        if (avgResponseTime) {
            avgResponseTime.textContent = `${(performance.avg_latency_ms || 0).toFixed(1)}ms`;
        }
        
        if (avgLatency) {
            avgLatency.textContent = `${(performance.avg_latency_ms || 0).toFixed(1)}ms`;
        }
        
        if (successRate) {
            const rate = ((1 - (performance.error_rate || 0)) * 100).toFixed(1);
            successRate.textContent = `${rate}%`;
        }
        
        if (errorRate) {
            const rate = ((performance.error_rate || 0) * 100).toFixed(1);
            errorRate.textContent = `${rate}%`;
        }
    }
    
    async runEnhancementDemo() {
        const button = document.querySelector('button[onclick="runEnhancementDemo()"]');
        const spinner = button.querySelector('.loading-spinner');
        const resultsDiv = document.getElementById('enhancementResults');
        
        try {
            // Show loading state
            button.disabled = true;
            spinner.style.display = 'inline';
            
            // Get demo data
            const demoResponse = await fetch(`${this.apiBase}/demo-data`);
            const demoData = await demoResponse.json();
            
            // Customize with user inputs
            const regimeType = document.getElementById('regimeType').value;
            const baseConfidence = parseFloat(document.getElementById('baseConfidence').value);
            
            demoData.base_signals.base_confidence = baseConfidence;
            demoData.regime_context.regime_type = regimeType;
            
            // Run enhancement
            const enhanceResponse = await fetch(`${this.apiBase}/enhance`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(demoData)
            });
            
            const result = await enhanceResponse.json();
            
            if (result.success) {
                this.displayEnhancementResults(result, baseConfidence);
                this.showAlert('ML enhancement completed successfully!', 'success');
                
                // Update chart
                this.addDataToChart(result.enhanced_confidence, result.enhancement_metadata?.enhancement_time);
            } else {
                this.showAlert(`Enhancement failed: ${result.error_message}`, 'danger');
                resultsDiv.innerHTML = `
                    <div class="text-center text-danger py-4">
                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                        <p>Enhancement failed: ${result.error_message}</p>
                    </div>
                `;
            }
        } catch (error) {
            console.error('Enhancement demo failed:', error);
            this.showAlert('Enhancement demo failed due to connection error', 'danger');
        } finally {
            // Hide loading state
            button.disabled = false;
            spinner.style.display = 'none';
        }
    }
    
    displayEnhancementResults(result, baseConfidence) {
        const resultsDiv = document.getElementById('enhancementResults');
        
        const improvement = ((result.enhanced_confidence - baseConfidence) / baseConfidence * 100).toFixed(1);
        const confidencePercent = (result.enhanced_confidence * 100).toFixed(1);
        const alignmentPercent = (result.regime_alignment * 100).toFixed(1);
        
        resultsDiv.innerHTML = `
            <div class="mb-3">
                <div class="d-flex justify-content-between">
                    <span>Enhanced Confidence</span>
                    <span class="fw-bold">${confidencePercent}%</span>
                </div>
                <div class="confidence-bar">
                    <div class="confidence-fill" style="width: ${confidencePercent}%"></div>
                </div>
            </div>
            
            <div class="mb-3">
                <div class="d-flex justify-content-between">
                    <span>Regime Alignment</span>
                    <span class="fw-bold">${alignmentPercent}%</span>
                </div>
                <div class="confidence-bar">
                    <div class="confidence-fill" style="width: ${alignmentPercent}%"></div>
                </div>
            </div>
            
            <div class="mb-3">
                <div class="d-flex justify-content-between">
                    <span>Improvement</span>
                    <span class="fw-bold text-success">+${improvement}%</span>
                </div>
            </div>
            
            <div class="feature-importance">
                <small class="text-light-75">Top Features:</small>
                <div class="mt-1">
                    ${this.renderFeatureImportance(result.feature_importance)}
                </div>
            </div>
        `;
    }
    
    renderFeatureImportance(importance) {
        if (!importance) return '<small>No feature data available</small>';
        
        const sorted = Object.entries(importance)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3);
        
        return sorted.map(([feature, score]) => 
            `<small class="d-block">${feature}: ${(score * 100).toFixed(1)}%</small>`
        ).join('');
    }
    
    addDataToChart(confidence, timestamp) {
        if (!this.performanceChart) return;
        
        const chart = this.performanceChart;
        const now = new Date().toLocaleTimeString();
        
        // Add new data point
        chart.data.labels.push(now);
        chart.data.datasets[0].data.push((confidence * 100).toFixed(1));
        chart.data.datasets[1].data.push(Math.random() * 50 + 10); // Simulated response time
        
        // Keep only last 10 points
        if (chart.data.labels.length > 10) {
            chart.data.labels.shift();
            chart.data.datasets[0].data.shift();
            chart.data.datasets[1].data.shift();
        }
        
        chart.update();
    }
    
    async loadPerformanceData() {
        try {
            const response = await fetch(`${this.apiBase}/performance`);
            const data = await response.json();
            
            // Initialize chart with some sample data
            this.initializeChartData();
        } catch (error) {
            console.error('Failed to load performance data:', error);
        }
    }
    
    initializeChartData() {
        if (!this.performanceChart) return;
        
        // Add some initial sample data
        const times = [];
        const confidences = [];
        const responseTimes = [];
        
        for (let i = 0; i < 5; i++) {
            const time = new Date(Date.now() - (4-i) * 60000).toLocaleTimeString();
            times.push(time);
            confidences.push((70 + Math.random() * 15).toFixed(1));
            responseTimes.push((15 + Math.random() * 10).toFixed(1));
        }
        
        this.performanceChart.data.labels = times;
        this.performanceChart.data.datasets[0].data = confidences;
        this.performanceChart.data.datasets[1].data = responseTimes;
        this.performanceChart.update();
    }
    
    startAutoRefresh() {
        // Refresh status every 30 seconds
        this.refreshInterval = setInterval(() => {
            this.refreshStatus();
        }, 30000);
    }
    
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
    
    showAlert(message, type = 'info') {
        const alertContainer = document.getElementById('alertContainer');
        if (!alertContainer) return;
        
        const alertId = 'alert-' + Date.now();
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" id="${alertId}" role="alert">
                <i class="fas fa-${this.getAlertIcon(type)} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        alertContainer.insertAdjacentHTML('beforeend', alertHtml);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const alert = document.getElementById(alertId);
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    }
    
    getAlertIcon(type) {
        const icons = {
            'success': 'check-circle',
            'danger': 'exclamation-triangle',
            'warning': 'exclamation-circle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
}

// Global functions for button onclick handlers
async function refreshStatus() {
    if (window.mlDashboard) {
        await window.mlDashboard.refreshStatus();
    }
}

async function runEnhancementDemo() {
    if (window.mlDashboard) {
        await window.mlDashboard.runEnhancementDemo();
    }
}

async function enableMLSystem() {
    try {
        const response = await fetch('/api/v1/ml/enable', { method: 'POST' });
        const result = await response.json();
        
        if (result.success) {
            window.mlDashboard.showAlert('ML system enabled successfully', 'success');
            await window.mlDashboard.refreshStatus();
        } else {
            window.mlDashboard.showAlert('Failed to enable ML system', 'danger');
        }
    } catch (error) {
        window.mlDashboard.showAlert('Connection error', 'danger');
    }
}

async function disableMLSystem() {
    try {
        const response = await fetch('/api/v1/ml/disable', { method: 'POST' });
        const result = await response.json();
        
        if (result.success) {
            window.mlDashboard.showAlert('ML system disabled', 'warning');
            await window.mlDashboard.refreshStatus();
        } else {
            window.mlDashboard.showAlert('Failed to disable ML system', 'danger');
        }
    } catch (error) {
        window.mlDashboard.showAlert('Connection error', 'danger');
    }
}

async function resetStats() {
    try {
        const response = await fetch('/api/v1/ml/reset-stats', { method: 'POST' });
        const result = await response.json();
        
        if (result.success) {
            window.mlDashboard.showAlert('Statistics reset successfully', 'info');
            await window.mlDashboard.refreshStatus();
        } else {
            window.mlDashboard.showAlert('Failed to reset statistics', 'danger');
        }
    } catch (error) {
        window.mlDashboard.showAlert('Connection error', 'danger');
    }
}

function downloadLogs() {
    window.mlDashboard.showAlert('Log download feature coming soon', 'info');
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.mlDashboard = new MLDashboard();
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.mlDashboard) {
        window.mlDashboard.stopAutoRefresh();
    }
});

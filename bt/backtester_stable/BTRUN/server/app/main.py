"""
Main FastAPI application factory
"""
import asyncio
from contextlib import asynccontextmanager
from typing import Async<PERSON>enerator

from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
# from prometheus_fastapi_instrumentator import Instrumentator
# from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor

from .api import api_router
from .core.config import settings
from .core.database import init_db, close_db
from .core.redis import init_redis, close_redis
from .core.gpu_manager import GPUManager
from .websocket import websocket_manager

# V2 Integration - removed due to import issues, using direct route inclusion instead


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator:
    """
    Application lifespan manager for startup and shutdown
    """
    # Startup
    print("🚀 Starting Enhanced GPU Backtester Server...")
    
    # Initialize databases
    await init_db()
    await init_redis()
    
    # Initialize GPU manager
    app.state.gpu_manager = GPUManager()
    await app.state.gpu_manager.initialize()
    
    # Initialize WebSocket manager
    app.state.ws_manager = websocket_manager
    
    # Start background tasks
    app.state.background_tasks = []
    
    print("✅ Server started successfully!")
    
    yield
    
    # Shutdown
    print("🛑 Shutting down server...")
    
    # Cancel background tasks
    for task in app.state.background_tasks:
        task.cancel()
    await asyncio.gather(*app.state.background_tasks, return_exceptions=True)
    
    # Cleanup
    await app.state.gpu_manager.cleanup()
    await close_redis()
    await close_db()
    
    print("👋 Server shutdown complete!")


def create_app() -> FastAPI:
    """
    Create and configure FastAPI application
    """
    app = FastAPI(
        title="Enhanced GPU Backtester API",
        description="High-performance backtesting service with GPU acceleration",
        version="2.0.0",
        docs_url="/api/docs",
        redoc_url="/api/redoc",
        openapi_url="/api/openapi.json",
        lifespan=lifespan
    )
    
    # Add middlewares
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Mount static files
    app.mount("/static", StaticFiles(directory="app/static"), name="static")
    
    # Add routers
    app.include_router(api_router, prefix="/api/v1")
    
    # Add V2 routes directly
    try:
        # Try to use integrated V2 routes first
        from .api.routes.v2_api_integrated import router as v2_router
        print("✅ Using integrated V2 API routes")
    except ImportError:
        try:
            # Try simple V2 routes
            from .api.routes.v2_api_simple import router as v2_router
            print("✅ Using simple V2 API routes (subprocess-based)")
        except ImportError:
            # Fallback to original V2 routes
            from .api.routes.v2_api import router as v2_router
            print("⚠️ Using simulated V2 API routes")
    
    app.include_router(v2_router, prefix="/api", tags=["v2"])
    
    # Add V2 WebSocket routes
    try:
        from .api.routes.v2_websocket import router as v2_ws_router
        app.include_router(v2_ws_router, prefix="/api", tags=["v2-websocket"])
        print("✅ V2 WebSocket routes loaded")
    except ImportError:
        print("⚠️ V2 WebSocket routes not available")
    
    # Add V2 Auth routes
    try:
        from .api.routes.v2_auth import router as v2_auth_router
        app.include_router(v2_auth_router, prefix="/api", tags=["v2-auth"])
        print("✅ V2 Auth routes loaded")
    except ImportError:
        print("⚠️ V2 Auth routes not available")
    
    # Add Excel Upload routes with YAML conversion
    try:
        from .api.v2.excel_upload_endpoint import router as excel_router
        app.include_router(excel_router, prefix="/api/v2", tags=["excel-upload"])
        print("✅ Excel upload routes with YAML conversion loaded")
    except ImportError as e:
        print(f"⚠️ Excel upload routes not available: {e}")
    
    # Setup V2 error handlers
    try:
        from .api.routes.v2_error_handler import setup_v2_error_handlers
        setup_v2_error_handlers(app)
        print("✅ V2 error handlers configured")
    except ImportError:
        print("⚠️ V2 error handlers not available")
    
    # Add page routes
    @app.get("/")
    async def index_page():
        return FileResponse("app/static/index_enterprise.html")
    
    @app.get("/login")
    async def login_page():
        return FileResponse("app/static/login_marvelquant.html")
    
    @app.get("/bt")
    async def backtest_page():
        return FileResponse("app/static/index.html")
    
    @app.get("/auto-backtest")
    async def auto_backtest_page():
        return FileResponse("app/static/auto_backtest.html")

    @app.get("/ml-dashboard")
    async def ml_dashboard_page():
        return FileResponse("app/static/ml_dashboard.html")

    @app.get("/favicon.ico")
    async def favicon():
        return FileResponse("app/static/favicon.ico")
    
    # Add instrumentation
    # if settings.ENABLE_METRICS:
    #     Instrumentator().instrument(app).expose(app, endpoint="/metrics")
    
    # if settings.ENABLE_TRACING:
    #     FastAPIInstrumentor.instrument_app(app)
    
    return app


# Create app instance
app = create_app()
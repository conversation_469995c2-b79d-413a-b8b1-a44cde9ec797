"""
API router aggregation
"""
from fastapi import APIRouter

from .routes import (
    auth,
    backtest,
    portfolio,
    strategy,
    data,
    health,
    websocket,
    validation,
    auto_backtest_enhanced as auto_backtest,
    logs,
    ml_system
)

api_router = APIRouter()

# Include all route modules
api_router.include_router(health.router, prefix="/health", tags=["health"])
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(backtest.router, prefix="/backtest", tags=["backtest"])
api_router.include_router(portfolio.router, prefix="/portfolio", tags=["portfolio"])
api_router.include_router(strategy.router, prefix="/strategy", tags=["strategy"])
api_router.include_router(data.router, prefix="/data", tags=["data"])
api_router.include_router(websocket.router, prefix="/ws", tags=["websocket"])
api_router.include_router(validation.router, prefix="/validation", tags=["validation"])
api_router.include_router(auto_backtest.router, prefix="/auto-backtest", tags=["auto-backtest"])
api_router.include_router(logs.router, prefix="/logs", tags=["logs"])
api_router.include_router(ml_system.router, prefix="/ml", tags=["ml-system"])
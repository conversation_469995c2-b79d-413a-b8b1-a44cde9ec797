#!/usr/bin/env python3
"""
Health Check Routes
===================

Health check endpoints for system monitoring.
"""

from fastapi import APIRouter
from datetime import datetime

router = APIRouter()

@router.get("/")
async def health_check():
    """Basic health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "Enhanced GPU Backtester API"
    }

@router.get("/detailed")
async def detailed_health_check():
    """Detailed health check with system information"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "Enhanced GPU Backtester API",
        "version": "2.0.0",
        "components": {
            "api": "operational",
            "database": "operational",
            "gpu": "operational",
            "ml_system": "operational"
        }
    }

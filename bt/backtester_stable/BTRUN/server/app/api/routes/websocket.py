#!/usr/bin/env python3
"""
WebSocket Routes
================

WebSocket endpoints for real-time communication.
"""

from fastapi import APIRouter
from datetime import datetime

router = APIRouter()

@router.get("/")
async def websocket_info():
    """WebSocket information endpoint"""
    return {
        "websocket_endpoints": ["/ws/backtest", "/ws/ml"],
        "timestamp": datetime.now().isoformat()
    }

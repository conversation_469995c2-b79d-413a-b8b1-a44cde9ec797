#!/usr/bin/env python3
"""
ML System API Routes
====================

FastAPI routes for the ML system integration.
Provides endpoints for ML_INDICATOR enhancement, system monitoring, and configuration.
"""

import logging
import sys
import os
from typing import Dict, Any, List, Optional
from datetime import datetime
import pandas as pd

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

# Add ML system to path
ml_system_path = os.path.join(os.path.dirname(__file__), "../../../backtester_v2/ml_system")
if ml_system_path not in sys.path:
    sys.path.insert(0, ml_system_path)

logger = logging.getLogger(__name__)

router = APIRouter()

# Pydantic models for API
class MLIndicatorSignal(BaseModel):
    strength: float = Field(..., ge=0, le=1, description="Signal strength between 0 and 1")
    direction: str = Field(..., description="Signal direction: BUY, SELL, HOLD")
    confidence: Optional[float] = Field(None, ge=0, le=1, description="Signal confidence")

class MLIndicatorData(BaseModel):
    indicators: List[Dict[str, Any]] = Field(..., description="List of indicator values")
    signals: List[MLIndicatorSignal] = Field(..., description="List of signals")
    features: List[str] = Field(..., description="List of feature names")
    base_confidence: float = Field(..., ge=0, le=1, description="Base confidence score")

class MarketData(BaseModel):
    timestamp: List[str] = Field(..., description="Timestamps")
    open: List[float] = Field(..., description="Open prices")
    high: List[float] = Field(..., description="High prices")
    low: List[float] = Field(..., description="Low prices")
    close: List[float] = Field(..., description="Close prices")
    volume: List[int] = Field(..., description="Volume data")

class RegimeContext(BaseModel):
    regime_type: str = Field(..., description="Market regime type")
    confidence: float = Field(..., ge=0, le=1, description="Regime confidence")
    alignment_score: Optional[float] = Field(None, ge=0, le=1, description="Alignment score")
    stability: Optional[float] = Field(None, ge=0, le=1, description="Regime stability")
    duration_minutes: Optional[int] = Field(None, description="Regime duration in minutes")

class MLEnhancementRequest(BaseModel):
    base_signals: MLIndicatorData = Field(..., description="Base ML_INDICATOR signals")
    market_data: Optional[MarketData] = Field(None, description="Market data")
    regime_context: Optional[RegimeContext] = Field(None, description="Market regime context")

class MLEnhancementResponse(BaseModel):
    success: bool = Field(..., description="Whether enhancement was successful")
    enhanced_confidence: Optional[float] = Field(None, description="Enhanced confidence score")
    regime_alignment: Optional[float] = Field(None, description="Regime alignment score")
    feature_importance: Optional[Dict[str, float]] = Field(None, description="Feature importance scores")
    ensemble_components: Optional[Dict[str, Any]] = Field(None, description="Ensemble model results")
    enhancement_metadata: Optional[Dict[str, Any]] = Field(None, description="Enhancement metadata")
    error_message: Optional[str] = Field(None, description="Error message if failed")

class MLSystemStatus(BaseModel):
    enabled: bool = Field(..., description="Whether ML system is enabled")
    healthy: bool = Field(..., description="Whether ML system is healthy")
    components_loaded: int = Field(..., description="Number of components loaded")
    performance: Dict[str, Any] = Field(..., description="Performance statistics")
    timestamp: str = Field(..., description="Status timestamp")


def get_ml_system():
    """Get ML system instance"""
    try:
        from core_infrastructure.ml_system_core import get_ml_system
        return get_ml_system()
    except ImportError as e:
        logger.error(f"Failed to import ML system: {e}")
        raise HTTPException(status_code=500, detail="ML system not available")


@router.post("/ml/enhance", response_model=MLEnhancementResponse)
async def enhance_ml_indicator_signals(
    request: MLEnhancementRequest,
    background_tasks: BackgroundTasks
) -> MLEnhancementResponse:
    """
    Enhance ML_INDICATOR signals with advanced ML capabilities
    """
    try:
        # Get ML system
        ml_system = get_ml_system()
        
        # Convert request to ML system format
        enhancement_data = {
            'base_signals': {
                'signals': [signal.dict() for signal in request.base_signals.signals],
                'indicators': request.base_signals.indicators,
                'features': request.base_signals.features,
                'base_confidence': request.base_signals.base_confidence
            }
        }
        
        # Add market data if provided
        if request.market_data:
            market_df = pd.DataFrame({
                'timestamp': pd.to_datetime(request.market_data.timestamp),
                'open': request.market_data.open,
                'high': request.market_data.high,
                'low': request.market_data.low,
                'close': request.market_data.close,
                'volume': request.market_data.volume
            })
            enhancement_data['market_data'] = market_df
        
        # Add regime context if provided
        if request.regime_context:
            enhancement_data['regime_context'] = request.regime_context.dict()
        
        # Get ML enhancement
        enhanced_result = ml_system.get_ml_insights("ML_INDICATOR_ENHANCEMENT", enhancement_data)
        
        if enhanced_result:
            return MLEnhancementResponse(
                success=True,
                enhanced_confidence=enhanced_result.get('confidence_score'),
                regime_alignment=enhanced_result.get('regime_alignment'),
                feature_importance=enhanced_result.get('feature_importance'),
                ensemble_components=enhanced_result.get('ensemble_components'),
                enhancement_metadata=enhanced_result.get('enhancement_metadata')
            )
        else:
            return MLEnhancementResponse(
                success=False,
                error_message="ML enhancement failed - no result returned"
            )
            
    except Exception as e:
        logger.error(f"ML enhancement failed: {e}")
        return MLEnhancementResponse(
            success=False,
            error_message=str(e)
        )


@router.get("/ml/status", response_model=MLSystemStatus)
async def get_ml_system_status() -> MLSystemStatus:
    """
    Get ML system status and health information
    """
    try:
        ml_system = get_ml_system()
        status = ml_system.get_status()
        
        return MLSystemStatus(
            enabled=status['enabled'],
            healthy=status['healthy'],
            components_loaded=sum(status['components'].values()),
            performance=status['performance'],
            timestamp=status['timestamp']
        )
        
    except Exception as e:
        logger.error(f"Failed to get ML system status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/ml/performance")
async def get_ml_performance_stats():
    """
    Get detailed ML system performance statistics
    """
    try:
        ml_system = get_ml_system()
        return {
            "performance_stats": ml_system.get_performance_stats(),
            "system_status": ml_system.get_status(),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get ML performance stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ml/reset-stats")
async def reset_ml_performance_stats():
    """
    Reset ML system performance statistics
    """
    try:
        ml_system = get_ml_system()
        ml_system.reset_stats()
        
        return {
            "success": True,
            "message": "ML performance statistics reset",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to reset ML stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ml/enable")
async def enable_ml_system():
    """
    Enable ML system
    """
    try:
        ml_system = get_ml_system()
        ml_system.enable()
        
        return {
            "success": True,
            "message": "ML system enabled",
            "status": ml_system.get_status(),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to enable ML system: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ml/disable")
async def disable_ml_system():
    """
    Disable ML system
    """
    try:
        ml_system = get_ml_system()
        ml_system.disable()
        
        return {
            "success": True,
            "message": "ML system disabled",
            "status": ml_system.get_status(),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to disable ML system: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/ml/health")
async def ml_system_health_check():
    """
    Simple health check for ML system
    """
    try:
        ml_system = get_ml_system()
        is_healthy = ml_system.is_healthy()
        
        return {
            "healthy": is_healthy,
            "status": "operational" if is_healthy else "degraded",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"ML health check failed: {e}")
        return {
            "healthy": False,
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@router.get("/ml/demo-data")
async def get_demo_data():
    """
    Get demo data for testing ML enhancement
    """
    return {
        "base_signals": {
            "indicators": [
                {"name": "RSI", "value": 0.65},
                {"name": "MACD", "value": 0.72},
                {"name": "SMA_CROSS", "value": 0.58},
                {"name": "BOLLINGER", "value": 0.63},
                {"name": "STOCH", "value": 0.69}
            ],
            "signals": [
                {"strength": 0.75, "direction": "BUY", "confidence": 0.8},
                {"strength": 0.65, "direction": "HOLD", "confidence": 0.6},
                {"strength": 0.55, "direction": "SELL", "confidence": 0.7}
            ],
            "features": ["rsi", "macd", "sma_cross", "bollinger", "stochastic"],
            "base_confidence": 0.68
        },
        "market_data": {
            "timestamp": [
                "2024-01-01T09:15:00",
                "2024-01-01T09:16:00",
                "2024-01-01T09:17:00",
                "2024-01-01T09:18:00",
                "2024-01-01T09:19:00"
            ],
            "open": [100.0, 100.5, 101.0, 100.8, 101.2],
            "high": [100.8, 101.2, 101.5, 101.3, 101.8],
            "low": [99.5, 100.0, 100.5, 100.3, 100.8],
            "close": [100.5, 101.0, 100.8, 101.2, 101.5],
            "volume": [1000, 1100, 1200, 1050, 1300]
        },
        "regime_context": {
            "regime_type": "BULLISH_NORMAL_VOLATILE",
            "confidence": 0.85,
            "alignment_score": 0.72,
            "stability": 0.78,
            "duration_minutes": 45
        }
    }

#!/usr/bin/env python3
"""
Portfolio Routes
================

Portfolio management endpoints.
"""

from fastapi import APIRouter
from datetime import datetime

router = APIRouter()

@router.get("/")
async def get_portfolios():
    """Get all portfolios"""
    return {
        "portfolios": [],
        "timestamp": datetime.now().isoformat()
    }

@router.get("/{portfolio_id}")
async def get_portfolio(portfolio_id: str):
    """Get specific portfolio"""
    return {
        "portfolio_id": portfolio_id,
        "timestamp": datetime.now().isoformat()
    }

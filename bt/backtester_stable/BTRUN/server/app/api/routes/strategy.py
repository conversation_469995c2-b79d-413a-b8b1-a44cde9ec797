#!/usr/bin/env python3
"""
Strategy Routes
===============

Strategy management endpoints.
"""

from fastapi import APIRouter
from datetime import datetime

router = APIRouter()

@router.get("/")
async def get_strategies():
    """Get all strategies"""
    return {
        "strategies": ["TBS", "TV", "ORB", "OI", "POS", "ML_INDICATOR"],
        "timestamp": datetime.now().isoformat()
    }

@router.get("/{strategy_type}")
async def get_strategy(strategy_type: str):
    """Get specific strategy"""
    return {
        "strategy_type": strategy_type,
        "timestamp": datetime.now().isoformat()
    }

#!/usr/bin/env python3
"""
Simple ML Server
================

Simplified FastAPI server for testing ML system UI.
"""

import sys
import os
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
import uvicorn

# Add ML system to path
ml_system_path = os.path.join(os.path.dirname(__file__), "../backtester_v2/ml_system")
if ml_system_path not in sys.path:
    sys.path.insert(0, ml_system_path)

app = FastAPI(
    title="ML System Dashboard",
    description="ML System Dashboard for Enhanced GPU Backtester",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Import ML routes
try:
    from app.api.routes.ml_system import router as ml_router
    app.include_router(ml_router, prefix="/api/v1/ml", tags=["ml-system"])
    print("✅ ML routes loaded successfully")
except ImportError as e:
    print(f"⚠️ Failed to load ML routes: {e}")
    
    # Create basic ML endpoints for testing
    @app.get("/api/v1/ml/status")
    async def ml_status():
        return {
            "enabled": True,
            "healthy": True,
            "components_loaded": 1,
            "performance": {
                "predictions": 0,
                "avg_latency_ms": 0,
                "error_rate": 0
            },
            "timestamp": "2024-01-01T00:00:00"
        }
    
    @app.get("/api/v1/ml/demo-data")
    async def demo_data():
        return {
            "base_signals": {
                "indicators": [
                    {"name": "RSI", "value": 0.65},
                    {"name": "MACD", "value": 0.72}
                ],
                "signals": [
                    {"strength": 0.75, "direction": "BUY", "confidence": 0.8}
                ],
                "features": ["rsi", "macd"],
                "base_confidence": 0.68
            },
            "regime_context": {
                "regime_type": "BULLISH_NORMAL_VOLATILE",
                "confidence": 0.85
            }
        }
    
    @app.post("/api/v1/ml/enhance")
    async def enhance_signals(request: dict):
        # Mock enhancement
        base_conf = request.get("base_signals", {}).get("base_confidence", 0.68)
        enhanced_conf = min(base_conf + 0.05, 1.0)
        
        return {
            "success": True,
            "enhanced_confidence": enhanced_conf,
            "regime_alignment": 0.72,
            "feature_importance": {
                "rsi": 0.6,
                "macd": 0.4
            },
            "enhancement_metadata": {
                "enhancement_time": "2024-01-01T00:00:00"
            }
        }

# Page routes
@app.get("/")
async def index():
    return FileResponse("app/static/ml_dashboard.html")

@app.get("/ml-dashboard")
async def ml_dashboard():
    return FileResponse("app/static/ml_dashboard.html")

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "ML Dashboard"}

if __name__ == "__main__":
    print("🚀 Starting Simple ML Server...")
    uvicorn.run(
        "simple_ml_server:app",
        host="0.0.0.0",
        port=8002,
        reload=True,
        log_level="info"
    )

#!/usr/bin/env python3
"""
ML UI Comprehensive Test Suite
===============================

Comprehensive testing of the ML Dashboard UI using Playwright.
Tests all functionality, edge cases, and potential issues.
"""

import asyncio
import sys
import os
from playwright.async_api import async_playwright

class MLUITester:
    def __init__(self):
        self.browser = None
        self.page = None
        self.test_results = []
        
    async def setup(self):
        """Setup browser and page"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=True)
        self.page = await self.browser.new_page()
        
        # Navigate to ML dashboard
        file_path = "file://" + os.path.abspath("app/static/ml_dashboard_standalone.html")
        await self.page.goto(file_path)
        await self.page.wait_for_load_state('networkidle')
        
    async def teardown(self):
        """Cleanup browser"""
        if self.browser:
            await self.browser.close()
    
    async def test_page_load(self):
        """Test if page loads correctly"""
        try:
            title = await self.page.title()
            assert "ML System Dashboard" in title
            
            # Check if key elements are present
            await self.page.wait_for_selector('#systemStatus')
            await self.page.wait_for_selector('#regimeType')
            await self.page.wait_for_selector('#baseConfidence')
            await self.page.wait_for_selector('#enhancementResults')
            
            self.test_results.append(("Page Load", "PASS", "All elements loaded successfully"))
            return True
        except Exception as e:
            self.test_results.append(("Page Load", "FAIL", str(e)))
            return False
    
    async def test_confidence_slider(self):
        """Test confidence slider functionality"""
        try:
            slider = self.page.locator('#baseConfidence')
            value_display = self.page.locator('#baseConfidenceValue')
            
            # Test different values
            test_values = [('0.25', '0.25'), ('0.50', '0.5'), ('0.75', '0.75'), ('1.0', '1')]

            for input_value, expected_display in test_values:
                # Use evaluate to set range input value
                await slider.evaluate(f"element => element.value = '{input_value}'")
                await slider.dispatch_event('input')
                await asyncio.sleep(0.1)

                displayed_value = await value_display.text_content()
                assert displayed_value == expected_display, f"Expected {expected_display}, got {displayed_value}"
            
            self.test_results.append(("Confidence Slider", "PASS", "All values updated correctly"))
            return True
        except Exception as e:
            self.test_results.append(("Confidence Slider", "FAIL", str(e)))
            return False
    
    async def test_regime_selector(self):
        """Test regime type selector"""
        try:
            selector = self.page.locator('#regimeType')
            
            # Test different regime types
            regimes = [
                "BULLISH_NORMAL_VOLATILE",
                "BULLISH_HIGH_VOLATILE", 
                "BEARISH_NORMAL_VOLATILE",
                "NEUTRAL_LOW_VOLATILE"
            ]
            
            for regime in regimes:
                await selector.select_option(regime)
                await asyncio.sleep(0.1)
                
                selected_value = await selector.input_value()
                assert selected_value == regime, f"Expected {regime}, got {selected_value}"
            
            self.test_results.append(("Regime Selector", "PASS", "All regime types selectable"))
            return True
        except Exception as e:
            self.test_results.append(("Regime Selector", "FAIL", str(e)))
            return False
    
    async def test_enhancement_demo(self):
        """Test ML enhancement demo functionality"""
        try:
            # Set test values
            slider = self.page.locator('#baseConfidence')
            await slider.evaluate("element => element.value = '0.70'")
            await slider.dispatch_event('input')
            await self.page.locator('#regimeType').select_option('BULLISH_HIGH_VOLATILE')
            
            # Click enhancement button
            enhance_button = self.page.locator('button:has-text("Run Enhancement")')
            await enhance_button.click()
            
            # Wait for loading state
            await asyncio.sleep(0.5)
            
            # Check if button is disabled during processing
            is_disabled = await enhance_button.is_disabled()
            assert is_disabled, "Button should be disabled during processing"
            
            # Wait for completion
            await asyncio.sleep(3)
            
            # Check if results are displayed
            results = self.page.locator('#enhancementResults')
            results_text = await results.text_content()
            
            assert "Enhanced Confidence" in results_text
            assert "Regime Alignment" in results_text
            assert "Improvement" in results_text
            assert "Top Features" in results_text
            
            # Check if button is re-enabled
            is_enabled = not await enhance_button.is_disabled()
            assert is_enabled, "Button should be re-enabled after processing"
            
            self.test_results.append(("Enhancement Demo", "PASS", "Enhancement completed successfully"))
            return True
        except Exception as e:
            self.test_results.append(("Enhancement Demo", "FAIL", str(e)))
            return False
    
    async def test_system_controls(self):
        """Test system control buttons"""
        try:
            # Test each control button
            buttons = [
                ("Enable ML System", "ML System is already enabled"),
                ("Disable ML System", "ML System disabled"),
                ("Reset Statistics", "Statistics reset successfully")
            ]
            
            for button_text, expected_alert in buttons:
                button = self.page.locator(f'button:has-text("{button_text}")')
                await button.click()
                await asyncio.sleep(0.5)
                
                # Check if alert appeared (get the last alert)
                alert = self.page.locator('.alert').last
                alert_text = await alert.text_content()
                assert expected_alert in alert_text, f"Expected '{expected_alert}' in alert"
                
                await asyncio.sleep(1)  # Wait for alert to auto-dismiss
            
            self.test_results.append(("System Controls", "PASS", "All control buttons working"))
            return True
        except Exception as e:
            self.test_results.append(("System Controls", "FAIL", str(e)))
            return False
    
    async def test_responsive_design(self):
        """Test responsive design at different screen sizes"""
        try:
            # Test different viewport sizes
            viewports = [
                (1920, 1080, "Desktop"),
                (1024, 768, "Tablet"),
                (375, 667, "Mobile")
            ]
            
            for width, height, device in viewports:
                await self.page.set_viewport_size({"width": width, "height": height})
                await asyncio.sleep(0.5)
                
                # Check if key elements are still visible
                status_card = self.page.locator('.card').first
                is_visible = await status_card.is_visible()
                assert is_visible, f"Status card not visible on {device}"
                
                # Test enhancement demo on mobile
                if device == "Mobile":
                    enhance_button = self.page.locator('button:has-text("Run Enhancement")')
                    await enhance_button.click()
                    await asyncio.sleep(3)
                    
                    results = self.page.locator('#enhancementResults')
                    is_visible = await results.is_visible()
                    assert is_visible, "Enhancement results not visible on mobile"
            
            self.test_results.append(("Responsive Design", "PASS", "All viewports working correctly"))
            return True
        except Exception as e:
            self.test_results.append(("Responsive Design", "FAIL", str(e)))
            return False
    
    async def test_edge_cases(self):
        """Test edge cases and error scenarios"""
        try:
            # Reset to desktop size
            await self.page.set_viewport_size({"width": 1920, "height": 1080})
            
            # Test extreme confidence values
            extreme_values = ['0.01', '0.99']
            
            for value in extreme_values:
                slider = self.page.locator('#baseConfidence')
                await slider.evaluate(f"element => element.value = '{value}'")
                await slider.dispatch_event('input')
                
                enhance_button = self.page.locator('button:has-text("Run Enhancement")')
                await enhance_button.click()
                await asyncio.sleep(3)
                
                # Check if enhancement completed without errors
                results = self.page.locator('#enhancementResults')
                results_text = await results.text_content()
                assert "Enhanced Confidence" in results_text, f"Enhancement failed for value {value}"
            
            # Test rapid clicking (should not cause issues)
            slider = self.page.locator('#baseConfidence')
            await slider.evaluate("element => element.value = '0.50'")
            await slider.dispatch_event('input')
            enhance_button = self.page.locator('button:has-text("Run Enhancement")')
            
            # Click multiple times rapidly
            for _ in range(3):
                await enhance_button.click()
                await asyncio.sleep(0.1)
            
            await asyncio.sleep(3)
            
            # Should still work normally
            results = self.page.locator('#enhancementResults')
            results_text = await results.text_content()
            assert "Enhanced Confidence" in results_text, "Rapid clicking caused issues"
            
            self.test_results.append(("Edge Cases", "PASS", "All edge cases handled correctly"))
            return True
        except Exception as e:
            self.test_results.append(("Edge Cases", "FAIL", str(e)))
            return False
    
    async def test_performance_chart(self):
        """Test performance chart functionality"""
        try:
            # Check if chart canvas exists
            chart_canvas = self.page.locator('#performanceChart')
            is_visible = await chart_canvas.is_visible()
            assert is_visible, "Performance chart not visible"
            
            # Run enhancement to add data to chart
            slider = self.page.locator('#baseConfidence')
            await slider.evaluate("element => element.value = '0.65'")
            await slider.dispatch_event('input')
            enhance_button = self.page.locator('button:has-text("Run Enhancement")')
            await enhance_button.click()
            await asyncio.sleep(3)
            
            # Chart should still be visible after data update
            is_visible = await chart_canvas.is_visible()
            assert is_visible, "Performance chart disappeared after data update"
            
            self.test_results.append(("Performance Chart", "PASS", "Chart functioning correctly"))
            return True
        except Exception as e:
            self.test_results.append(("Performance Chart", "FAIL", str(e)))
            return False
    
    async def run_all_tests(self):
        """Run all tests"""
        print("🧪 Starting ML UI Comprehensive Test Suite...")
        print("=" * 60)
        
        await self.setup()
        
        tests = [
            self.test_page_load,
            self.test_confidence_slider,
            self.test_regime_selector,
            self.test_enhancement_demo,
            self.test_system_controls,
            self.test_responsive_design,
            self.test_edge_cases,
            self.test_performance_chart
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                result = await test()
                if result:
                    passed += 1
            except Exception as e:
                print(f"❌ Test {test.__name__} failed with exception: {e}")
        
        await self.teardown()
        
        # Print results
        print("\n" + "=" * 60)
        print("🏁 Test Results Summary:")
        print("=" * 60)
        
        for test_name, status, message in self.test_results:
            icon = "✅" if status == "PASS" else "❌"
            print(f"{icon} {test_name}: {status} - {message}")
        
        print(f"\n📊 Overall Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! ML UI is production-ready!")
        else:
            print(f"⚠️  {total - passed} tests failed. Please review and fix issues.")
        
        return passed == total

async def main():
    """Main test runner"""
    tester = MLUITester()
    success = await tester.run_all_tests()
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

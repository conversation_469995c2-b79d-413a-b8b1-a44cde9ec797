#!/usr/bin/env python3
"""
Simple Test Server for ML Dashboard
===================================
"""

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
import uvicorn

app = FastAPI(title="ML Dashboard Test Server")

# Mount static files
app.mount("/static", StaticFiles(directory="app/static"), name="static")

@app.get("/")
async def index():
    return FileResponse("app/static/ml_dashboard_standalone.html")

@app.get("/health")
async def health():
    return {"status": "healthy", "message": "Test server running"}

@app.get("/api/v1/ml/status")
async def ml_status():
    return {
        "enabled": True,
        "healthy": True,
        "components_loaded": 4,
        "performance": {
            "predictions": 0,
            "errors": 0,
            "error_rate": 0.0,
            "avg_latency_ms": 16.8,
            "total_latency_ms": 0
        },
        "timestamp": "2024-01-01T00:00:00"
    }

@app.post("/api/v1/ml/enhance")
async def ml_enhance():
    return {
        "success": True,
        "enhanced_confidence": 0.75,
        "regime_alignment": 0.72,
        "feature_importance": {
            "current_volume": 0.924,
            "sma_5": 0.024,
            "current_price": 0.024
        },
        "enhancement_metadata": {
            "enhancement_time": "2024-01-01T00:00:00"
        }
    }

@app.post("/api/v1/ml/enable")
async def enable_ml():
    return {"success": True, "message": "ML system enabled"}

@app.post("/api/v1/ml/disable")
async def disable_ml():
    return {"success": True, "message": "ML system disabled"}

@app.post("/api/v1/ml/reset-stats")
async def reset_stats():
    return {"success": True, "message": "Statistics reset"}

@app.get("/api/v1/ml/demo-data")
async def demo_data():
    return {
        "base_signals": {
            "indicators": [{"name": "RSI", "value": 0.65}],
            "signals": [{"strength": 0.75, "direction": "BUY", "confidence": 0.8}],
            "features": ["rsi", "macd"],
            "base_confidence": 0.68
        },
        "regime_context": {
            "regime_type": "BULLISH_NORMAL_VOLATILE",
            "confidence": 0.85
        }
    }

if __name__ == "__main__":
    print("🚀 Starting ML Dashboard Test Server...")
    uvicorn.run(app, host="0.0.0.0", port=8003)
